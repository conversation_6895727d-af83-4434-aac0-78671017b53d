buildscript {
    ext.kotlin_version = '1.5.0'
    repositories {
        google()
        jcenter()
        maven { url 'https://maven.google.com' }
        mavenCentral()
        maven { url 'https://maven.aliyun.com/repository/jcenter' }
        maven { url 'https://jitpack.io' }
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:4.0.2'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        classpath 'com.google.gms:google-services:4.3.5'
    }
}

allprojects {
    repositories {
        google()
        jcenter()
        maven { url 'https://maven.google.com' }
        mavenCentral()
        maven { url 'https://maven.aliyun.com/repository/jcenter' }
        maven { url 'https://jitpack.io' }
    }
}

rootProject.buildDir = '../build'
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}
subprojects {
    project.evaluationDependsOn(':app')
}

task clean(type: Delete) {
    delete rootProject.buildDir
}
