name: trackcarvcc
description: A new Flutter application.

# The following line prevents the package from being accidentally published to
# pub.dev using `pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
version: 1.4.2+326
environment:
  sdk: ">=2.7.0 <3.0.0"

dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.0

  get: ^3.17.1
  get_storage: ^1.3.2
  dio: ^3.0.10
  cached_network_image: ^2.5.1
  shared_preferences: ^0.5.12+4
  mask_text_input_formatter: ^1.1.0
  url_launcher: ^5.7.10
  fluttertoast: ^7.1.1
  flutter_rating_bar: ^3.2.0+1
  android_intent: ^0.3.7+7
  pull_to_refresh: 1.6.3

  # A Flutter plugin to easily handle realtime location in iOS and Android.
  location: 3.2.1
  # google_maps_flutter: ^1.0.10
  # Stateful map controller for Google Maps google_maps_flutter. Manage Markers, Circles, Polylines, and Polygons.
#  google_maps_controller: 1.1.0
  bottom_sheet_bar: ^1.0.2+7
  firebase_core: ^0.7.0
  firebase_messaging: ^8.0.0-dev.15
  flutter_local_notifications: ^4.0.1+2
  multi_image_picker2:
    git:
      url: https://PhamNamX19:<EMAIL>/PhamNamX19/multi_image_picker2.git
  flutter_absolute_path: ^1.0.6
  permission_handler:
    git:
      url: https://PhamNamX19:<EMAIL>/PhamNamX19/permission_handler-5.1.0.git

  # This Flutter plugin provides an API for querying information about an application package.
  package_info_plus: ^0.6.4
  flutter_polyline_points: ^0.2.4
  geocoding: ^1.0.5
#  syncfusion_flutter_maps: ^18.4.49-beta
  flutter_datetime_picker: ^1.5.0
  intl: ^0.16.1
  flutter_form_builder: ^4.2.0
  image_picker:
    git:
      url: https://PhamNamX19:<EMAIL>/PhamNamX19/image_picker_0.6.7.git
#  file_picker:
#    git:
#      url: https://PhamNamX19:<EMAIL>/PhamNamX19/file_picker.git
  syncfusion_flutter_pdfviewer:

  vtmap_gl: ^2.0.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_native_splash: ^0.2.9
  flutter_launcher_icons: ^0.7.5

dependency_overrides:
  photo_manager: 0.6.0

  flutter_plugin_android_lifecycle:
    git:
      url: https://PhamNamX19:<EMAIL>/PhamNamX19/flutter_plugin_android_lifecycle.git

flutter_native_splash:
  background_image: assets/images/bg_splash.jpg
  color: "#FFFFFF"
  image: assets/images/bg_splash.jpg
  android: true
  ios: true
  android_gravity: fill
  ios_content_mode: scaleAspectFill

flutter_icons:
  image_path: "assets/images/ic_launcher.jpg"
  android: true
  ios: true

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter.
flutter:
  uses-material-design: true
  assets:
    - assets/images/
  fonts:
    - family: Quicksand
      fonts:
        - asset: assets/fonts/Quicksand-Bold.ttf
        - asset: assets/fonts/Quicksand-Regular.ttf
        - asset: assets/fonts/Quicksand-Medium.ttf
    - family: Lato
      fonts:
        - asset: assets/fonts/Lato-Regular.ttf
        - asset: assets/fonts/Lato-Bold.ttf
