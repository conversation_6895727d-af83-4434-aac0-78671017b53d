# Generated by pub
# See https://dart.dev/tools/pub/glossary#lockfile
packages:
  android_intent:
    dependency: "direct main"
    description:
      name: android_intent
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.3.7+7"
  archive:
    dependency: transitive
    description:
      name: archive
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.13"
  args:
    dependency: transitive
    description:
      name: args
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.6.0"
  async:
    dependency: transitive
    description:
      name: async
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.5.0-nullsafety.1"
  basic_utils:
    dependency: transitive
    description:
      name: basic_utils
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.7.1"
  boolean_selector:
    dependency: transitive
    description:
      name: boolean_selector
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.0-nullsafety.1"
  bottom_sheet_bar:
    dependency: "direct main"
    description:
      name: bottom_sheet_bar
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.2+7"
  cached_network_image:
    dependency: "direct main"
    description:
      name: cached_network_image
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.5.1"
  characters:
    dependency: transitive
    description:
      name: characters
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.1.0-nullsafety.3"
  charcode:
    dependency: transitive
    description:
      name: charcode
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.2.0-nullsafety.1"
  clock:
    dependency: transitive
    description:
      name: clock
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.1.0-nullsafety.1"
  collection:
    dependency: transitive
    description:
      name: collection
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.15.0-nullsafety.3"
  convert:
    dependency: transitive
    description:
      name: convert
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.1"
  crypto:
    dependency: transitive
    description:
      name: crypto
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.5"
  cupertino_icons:
    dependency: "direct main"
    description:
      name: cupertino_icons
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.0"
  date_range_picker:
    dependency: transitive
    description:
      name: date_range_picker
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.7"
  datetime_picker_formfield:
    dependency: transitive
    description:
      name: datetime_picker_formfield
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.0"
  dio:
    dependency: "direct main"
    description:
      name: dio
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.0.10"
  dropdown_search:
    dependency: transitive
    description:
      name: dropdown_search
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.4.9"
  fake_async:
    dependency: transitive
    description:
      name: fake_async
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.2.0-nullsafety.1"
  ffi:
    dependency: transitive
    description:
      name: ffi
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.1.3"
  file:
    dependency: transitive
    description:
      name: file
      url: "https://pub.dartlang.org"
    source: hosted
    version: "5.2.1"
  firebase_core:
    dependency: "direct main"
    description:
      name: firebase_core
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.7.0"
  firebase_core_platform_interface:
    dependency: transitive
    description:
      name: firebase_core_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.0.1"
  firebase_core_web:
    dependency: transitive
    description:
      name: firebase_core_web
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.2.1+3"
  firebase_messaging:
    dependency: "direct main"
    description:
      name: firebase_messaging
      url: "https://pub.dartlang.org"
    source: hosted
    version: "8.0.0-dev.15"
  firebase_messaging_platform_interface:
    dependency: transitive
    description:
      name: firebase_messaging_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.0-dev.10"
  firebase_messaging_web:
    dependency: transitive
    description:
      name: firebase_messaging_web
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.1.0-dev.6"
  flutter:
    dependency: "direct main"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_absolute_path:
    dependency: "direct main"
    description:
      name: flutter_absolute_path
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.6"
  flutter_blurhash:
    dependency: transitive
    description:
      name: flutter_blurhash
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.5.0"
  flutter_cache_manager:
    dependency: transitive
    description:
      name: flutter_cache_manager
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.2"
  flutter_chips_input:
    dependency: transitive
    description:
      name: flutter_chips_input
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.9.5"
  flutter_colorpicker:
    dependency: transitive
    description:
      name: flutter_colorpicker
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.3.5"
  flutter_datetime_picker:
    dependency: "direct main"
    description:
      name: flutter_datetime_picker
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.5.0"
  flutter_form_builder:
    dependency: "direct main"
    description:
      name: flutter_form_builder
      url: "https://pub.dartlang.org"
    source: hosted
    version: "4.2.0"
  flutter_keyboard_visibility:
    dependency: transitive
    description:
      name: flutter_keyboard_visibility
      url: "https://pub.dartlang.org"
    source: hosted
    version: "4.0.6"
  flutter_keyboard_visibility_platform_interface:
    dependency: transitive
    description:
      name: flutter_keyboard_visibility_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.1"
  flutter_keyboard_visibility_web:
    dependency: transitive
    description:
      name: flutter_keyboard_visibility_web
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.1"
  flutter_launcher_icons:
    dependency: "direct dev"
    description:
      name: flutter_launcher_icons
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.7.5"
  flutter_local_notifications:
    dependency: "direct main"
    description:
      name: flutter_local_notifications
      url: "https://pub.dartlang.org"
    source: hosted
    version: "4.0.1+2"
  flutter_local_notifications_platform_interface:
    dependency: transitive
    description:
      name: flutter_local_notifications_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.0+1"
  flutter_localizations:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_native_splash:
    dependency: "direct dev"
    description:
      name: flutter_native_splash
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.2.11"
  flutter_plugin_android_lifecycle:
    dependency: "direct overridden"
    description:
      path: "."
      ref: HEAD
      resolved-ref: "91a4b5d3f6d5c29d794ff555a9037ef3eb33e6ad"
      url: "https://PhamNamX19:<EMAIL>/PhamNamX19/flutter_plugin_android_lifecycle.git"
    source: git
    version: "1.0.11"
  flutter_polyline_points:
    dependency: "direct main"
    description:
      name: flutter_polyline_points
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.2.4"
  flutter_rating_bar:
    dependency: "direct main"
    description:
      name: flutter_rating_bar
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.2.0+1"
  flutter_test:
    dependency: "direct dev"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_touch_spin:
    dependency: transitive
    description:
      name: flutter_touch_spin
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.1"
  flutter_typeahead:
    dependency: transitive
    description:
      name: flutter_typeahead
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.9.3"
  flutter_web_plugins:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.0"
  fluttertoast:
    dependency: "direct main"
    description:
      name: fluttertoast
      url: "https://pub.dartlang.org"
    source: hosted
    version: "7.1.8"
  geocoding:
    dependency: "direct main"
    description:
      name: geocoding
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.5"
  geocoding_platform_interface:
    dependency: transitive
    description:
      name: geocoding_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.1+1"
  get:
    dependency: "direct main"
    description:
      name: get
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.26.0"
  get_storage:
    dependency: "direct main"
    description:
      name: get_storage
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.4.0"
  http:
    dependency: transitive
    description:
      name: http
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.12.2"
  http_parser:
    dependency: transitive
    description:
      name: http_parser
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.1.4"
  image:
    dependency: transitive
    description:
      name: image
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.19"
  image_picker:
    dependency: "direct main"
    description:
      path: "."
      ref: HEAD
      resolved-ref: "1c6bea0ea31485b9b97e1f3b5b0f3c3620de9d65"
      url: "https://PhamNamX19:<EMAIL>/PhamNamX19/image_picker_0.6.7.git"
    source: git
    version: "0.6.7+22"
  image_picker_platform_interface:
    dependency: transitive
    description:
      name: image_picker_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.1.6"
  intl:
    dependency: "direct main"
    description:
      name: intl
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.16.1"
  js:
    dependency: transitive
    description:
      name: js
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.6.2"
  json_annotation:
    dependency: transitive
    description:
      name: json_annotation
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.1.1"
  location:
    dependency: "direct main"
    description:
      name: location
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.2.1"
  location_platform_interface:
    dependency: transitive
    description:
      name: location_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.1.0"
  location_web:
    dependency: transitive
    description:
      name: location_web
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.1"
  logging:
    dependency: transitive
    description:
      name: logging
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.11.4"
  mask_text_input_formatter:
    dependency: "direct main"
    description:
      name: mask_text_input_formatter
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.2.1"
  matcher:
    dependency: transitive
    description:
      name: matcher
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.12.10-nullsafety.1"
  measure_size:
    dependency: transitive
    description:
      name: measure_size
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.0+1"
  meta:
    dependency: transitive
    description:
      name: meta
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.3.0-nullsafety.3"
  multi_image_picker2:
    dependency: "direct main"
    description:
      path: "."
      ref: HEAD
      resolved-ref: "586a2bf8ea6695174612495407a141a2857388e2"
      url: "https://PhamNamX19:<EMAIL>/PhamNamX19/multi_image_picker2.git"
    source: git
    version: "5.0.1"
  octo_image:
    dependency: transitive
    description:
      name: octo_image
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.3.0"
  package_info_plus:
    dependency: "direct main"
    description:
      name: package_info_plus
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.6.4"
  package_info_plus_linux:
    dependency: transitive
    description:
      name: package_info_plus_linux
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.1.1"
  package_info_plus_macos:
    dependency: transitive
    description:
      name: package_info_plus_macos
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.2.0"
  package_info_plus_platform_interface:
    dependency: transitive
    description:
      name: package_info_plus_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.3.0"
  package_info_plus_web:
    dependency: transitive
    description:
      name: package_info_plus_web
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.2.1"
  package_info_plus_windows:
    dependency: transitive
    description:
      name: package_info_plus_windows
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.2.0"
  path:
    dependency: transitive
    description:
      name: path
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.8.0-nullsafety.1"
  path_provider:
    dependency: transitive
    description:
      name: path_provider
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.6.28"
  path_provider_linux:
    dependency: transitive
    description:
      name: path_provider_linux
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.0.1+2"
  path_provider_macos:
    dependency: transitive
    description:
      name: path_provider_macos
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.0.4+8"
  path_provider_platform_interface:
    dependency: transitive
    description:
      name: path_provider_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.4"
  path_provider_windows:
    dependency: transitive
    description:
      name: path_provider_windows
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.0.4+3"
  pedantic:
    dependency: transitive
    description:
      name: pedantic
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.9.2"
  permission_handler:
    dependency: "direct main"
    description:
      path: "."
      ref: HEAD
      resolved-ref: "958ed808be3101de8cb22d31970ff855ca611f8e"
      url: "https://PhamNamX19:<EMAIL>/PhamNamX19/permission_handler-5.1.0.git"
    source: git
    version: "5.1.0+2"
  permission_handler_platform_interface:
    dependency: transitive
    description:
      name: permission_handler_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.2"
  petitparser:
    dependency: transitive
    description:
      name: petitparser
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.1.0"
  photo_manager:
    dependency: "direct overridden"
    description:
      name: photo_manager
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.6.0"
  platform:
    dependency: transitive
    description:
      name: platform
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.2.1"
  platform_detect:
    dependency: transitive
    description:
      name: platform_detect
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.4.2"
  plugin_platform_interface:
    dependency: transitive
    description:
      name: plugin_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.3"
  pointycastle:
    dependency: transitive
    description:
      name: pointycastle
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.1"
  process:
    dependency: transitive
    description:
      name: process
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.0.13"
  pub_semver:
    dependency: transitive
    description:
      name: pub_semver
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.4.4"
  pull_to_refresh:
    dependency: "direct main"
    description:
      name: pull_to_refresh
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.6.3"
  quiver:
    dependency: transitive
    description:
      name: quiver
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.5"
  random_string:
    dependency: transitive
    description:
      name: random_string
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.0"
  rating_bar:
    dependency: transitive
    description:
      name: rating_bar
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.2.0"
  rxdart:
    dependency: transitive
    description:
      name: rxdart
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.25.0"
  service_worker:
    dependency: transitive
    description:
      name: service_worker
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.2.4"
  shared_preferences:
    dependency: "direct main"
    description:
      name: shared_preferences
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.5.12+4"
  shared_preferences_linux:
    dependency: transitive
    description:
      name: shared_preferences_linux
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.0.2+4"
  shared_preferences_macos:
    dependency: transitive
    description:
      name: shared_preferences_macos
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.0.1+11"
  shared_preferences_platform_interface:
    dependency: transitive
    description:
      name: shared_preferences_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.4"
  shared_preferences_web:
    dependency: transitive
    description:
      name: shared_preferences_web
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.1.2+7"
  shared_preferences_windows:
    dependency: transitive
    description:
      name: shared_preferences_windows
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.0.2+3"
  signature:
    dependency: transitive
    description:
      name: signature
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.2.1"
  sky_engine:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.99"
  source_span:
    dependency: transitive
    description:
      name: source_span
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.8.0-nullsafety.2"
  sqflite:
    dependency: transitive
    description:
      name: sqflite
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.3.2+4"
  sqflite_common:
    dependency: transitive
    description:
      name: sqflite_common
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.3+3"
  stack_trace:
    dependency: transitive
    description:
      name: stack_trace
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.10.0-nullsafety.1"
  stream_channel:
    dependency: transitive
    description:
      name: stream_channel
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.0-nullsafety.1"
  string_scanner:
    dependency: transitive
    description:
      name: string_scanner
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.1.0-nullsafety.1"
  syncfusion_flutter_core:
    dependency: transitive
    description:
      name: syncfusion_flutter_core
      url: "https://pub.dartlang.org"
    source: hosted
    version: "18.4.49"
  syncfusion_flutter_pdf:
    dependency: transitive
    description:
      name: syncfusion_flutter_pdf
      url: "https://pub.dartlang.org"
    source: hosted
    version: "18.4.49-beta"
  syncfusion_flutter_pdfviewer:
    dependency: "direct main"
    description:
      name: syncfusion_flutter_pdfviewer
      url: "https://pub.dartlang.org"
    source: hosted
    version: "18.4.49-beta"
  synchronized:
    dependency: transitive
    description:
      name: synchronized
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.2.0+2"
  term_glyph:
    dependency: transitive
    description:
      name: term_glyph
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.2.0-nullsafety.1"
  test_api:
    dependency: transitive
    description:
      name: test_api
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.2.19-nullsafety.2"
  timezone:
    dependency: transitive
    description:
      name: timezone
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.6.1"
  typed_data:
    dependency: transitive
    description:
      name: typed_data
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.3.0-nullsafety.3"
  url_launcher:
    dependency: "direct main"
    description:
      name: url_launcher
      url: "https://pub.dartlang.org"
    source: hosted
    version: "5.7.10"
  url_launcher_linux:
    dependency: transitive
    description:
      name: url_launcher_linux
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.0.1+4"
  url_launcher_macos:
    dependency: transitive
    description:
      name: url_launcher_macos
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.0.1+9"
  url_launcher_platform_interface:
    dependency: transitive
    description:
      name: url_launcher_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.9"
  url_launcher_web:
    dependency: transitive
    description:
      name: url_launcher_web
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.1.5+3"
  url_launcher_windows:
    dependency: transitive
    description:
      name: url_launcher_windows
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.0.1+3"
  uuid:
    dependency: transitive
    description:
      name: uuid
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.2.2"
  validators:
    dependency: transitive
    description:
      name: validators
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.1"
  vector_math:
    dependency: transitive
    description:
      name: vector_math
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.0-nullsafety.3"
  vin_decoder:
    dependency: transitive
    description:
      name: vin_decoder
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.1.3"
  vtmap_gl:
    dependency: "direct main"
    description:
      name: vtmap_gl
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.0"
  vtmap_gl_platform_interface:
    dependency: transitive
    description:
      name: vtmap_gl_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.0.8"
  win32:
    dependency: transitive
    description:
      name: win32
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.7.4+1"
  xdg_directories:
    dependency: transitive
    description:
      name: xdg_directories
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.1.2"
  xml:
    dependency: transitive
    description:
      name: xml
      url: "https://pub.dartlang.org"
    source: hosted
    version: "4.5.1"
  yaml:
    dependency: transitive
    description:
      name: yaml
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.2.1"
sdks:
  dart: ">=2.10.2 <2.11.0"
  flutter: ">=1.22.2 <2.0.0"
