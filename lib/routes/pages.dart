import 'package:get/get.dart';
import 'package:trackcarvcc/constants/constants.dart';
import 'package:trackcarvcc/routes/bindings/bindings.dart';
import 'package:trackcarvcc/routes/bindings/open_command_bindding.dart';
import 'package:trackcarvcc/routes/bindings/register_binding.dart';
import 'package:trackcarvcc/routes/routes.dart';
import 'package:trackcarvcc/ui/car_order_creation/widget/item_planned_route.dart';
import 'package:trackcarvcc/ui/crave_car/view/crave_car_page.dart';
import 'package:trackcarvcc/ui/engineering_date/view/create_engineering_date_page.dart';
import 'package:trackcarvcc/ui/engineering_date/view/list_engineering_date_page.dart';
import 'package:trackcarvcc/ui/explanation/ui/explanation_detail_page.dart';
import 'package:trackcarvcc/ui/login/register.dart';
import 'package:trackcarvcc/ui/open_command/open_command_page.dart';
import 'package:trackcarvcc/ui/planned_outed/planned_outed_binding.dart';
import 'package:trackcarvcc/ui/planned_outed/planned_outed_page.dart';
import 'package:trackcarvcc/ui/ui.dart';

import 'bindings/update_binding.dart';

class Pages {
  Pages._(); //this is to prevent anyone from instantiating this object
  static final getPages = [
    GetPage(
      name: Routers.initialRoute,
      page: () => SplashPage(),
      binding: SplashBinding(),
    ),
    GetPage(
      name: Routers.login,
      page: () => LoginPage(),
      binding: LoginBinding(),
    ),
    GetPage(
      name: Routers.register,
      page: () => RegisterPage(),
      binding: RegisterBinding(),
    ),
    GetPage(
      name: Routers.nav,
      page: () => AppNavigationPage(),
      binding: AppNavigationBinding(),
    ),
    GetPage(
      name: Routers.address,
      page: () => AddressSelectionPage(),
      binding: AddressBinding(),
    ),
    GetPage(
      name: Routers.update_car_order,
      page: () => ListBookCarPage(typeMenu: Constants.TYPE_LIST_BOOK_CAR),
      binding: AppNavigationBinding(),
    ),
    GetPage(
      name: Routers.list_car_creation,
      page: () => CarOrderCreationPage(
        pageType: Get.arguments['type'] ?? PageType.UPDATE,
      ),
      binding: UpdateOrderBinding(),
    ),
    GetPage(
      name: Routers.detail,
      page: () => BookCarDetailPage(
        bookCar: Get.arguments[Constants.KEY_BOOK_CAR],
        typeMenu: Get.arguments[Constants.KEY_TYPE_MENU],
      ),
      binding: BookCarDetailBinding(),
    ),
    GetPage(
      name: Routers.open_command,
      page: () => OpenCommandPage(
        bookCar: Get.arguments[Constants.KEY_BOOK_CAR],
        listTogether: Get.arguments[Constants.KEY_LIST_TOGETHER],
      ),
      binding: OpenCommandBinding(),
    ),
    GetPage(
      name: Routers.list_engineering_date_page,
      page: () => ListOrderRequestPage(),
      binding: AppNavigationBinding(),
    ),
    GetPage(
      name: Routers.create_engineering_date_page,
      page: () => CreateEngineeringDatePage(engineeringDateDTO: Get.arguments),
      binding: AppNavigationBinding(),
    ),
    GetPage(
      name: Routers.crave_car_page,
      page: () => CraveCarPage(pageType: Get.arguments == null ? PageTypeCraveCar.CREATE_ORDER : PageTypeCraveCar.UPDATE_ORDER, craveCarDto: Get.arguments),
      binding: AppNavigationBinding(),
    ),
    GetPage(
      name: Routers.planned_routed,
      page: () => PlannedRouted(
        points: Get.arguments,
        // startPoint: Get.arguments[0],
        // destinationPoint: Get.arguments[1],
      ),
      binding: PlannedOutedBinding(),
    ),
    GetPage(
      name: Routers.explanationDetail,
      page: () => ExplanationDetailPage(
        explanationDTO: Get.arguments[0],
        isShowAction: Get.arguments[1],
      ),
      binding: AppNavigationBinding(),
    ),
  ];
}
