import 'package:dio/dio.dart';
import 'package:get/get.dart';
import 'package:trackcarvcc/repository/api/api.dart';
import 'package:trackcarvcc/repository/api/http_manager.dart';

class InitialBinding implements Bindings {
  @override
  void dependencies() {
    // Get.lazyPut<DataCenter>(() => DataCenter());
    Get.put<HTTPManager>(HTTPManager(Dio()), permanent: true);
    Get.put<Api>(Api(Get.find<HTTPManager>()), permanent: true);
  }
}
