import 'package:get/get.dart';
import 'package:trackcarvcc/controllers/controllers.dart';
import 'package:trackcarvcc/controllers/register_controller.dart';
import 'package:trackcarvcc/repository/api/api.dart';

class RegisterBinding implements Bindings {
  @override
  void dependencies() {
    Get.lazyPut<RegisterController>(
      () => RegisterController(Get.find<Api>()),
      fenix: true,
    );
  }
}
