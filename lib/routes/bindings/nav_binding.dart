import 'package:get/get.dart';
import 'package:trackcarvcc/controllers/controllers.dart';
import 'package:trackcarvcc/controllers/menu_controller.dart';
import 'package:trackcarvcc/controllers/monitoring_map_controller.dart';
import 'package:trackcarvcc/repository/api/api.dart';
import 'package:trackcarvcc/ui/crave_car/controller/crave_car_controller.dart';
import 'package:trackcarvcc/ui/crave_car/controller/list_crave_car_controller.dart';
import 'package:trackcarvcc/ui/engineering_date/controller/create_engineering_date_controller.dart';
import 'package:trackcarvcc/ui/engineering_date/controller/list_engineering_date_controller.dart';
import 'package:trackcarvcc/ui/explanation/data/view_model/explanation_view_model.dart';

class AppNavigationBinding implements Bindings {
  @override
  void dependencies() {
    Get.lazyPut<ListBookCarController>(
      () => ListBookCarController(Get.find<Api>()),
      fenix: true,
    );
    Get.lazyPut<CarOrderCreationController>(
      () => CarOrderCreationController(Get.find<Api>()),
      fenix: false,
    );
    Get.lazyPut<MapController>(
      () => MapController(Get.find<Api>()),
      fenix: true,
    );
    Get.lazyPut<MonitoringMapController>(
      () => MonitoringMapController(Get.find<Api>()),
      fenix: true,
    );

    Get.lazyPut<MenuController>(
      () => MenuController(Get.find<Api>()),
      fenix: true,
    );

    Get.lazyPut<ListEngineeringDateController>(
      () => ListEngineeringDateController(Get.find<Api>()),
      fenix: true,
    );

    Get.lazyPut<CreateEngineeringDateController>(
      () => CreateEngineeringDateController(Get.find<Api>()),
      fenix: true,
    );
    Get.lazyPut<CraveCarController>(
      () => CraveCarController(Get.find<Api>()),
      fenix: true,
    );
    Get.lazyPut<ListCraveCarController>(
      () => ListCraveCarController(Get.find<Api>()),
      fenix: true,
    );
    Get.lazyPut<ExplanationViewModel>(
      () => ExplanationViewModel(Get.find<Api>()),
      fenix: true,
    );
  }
}
