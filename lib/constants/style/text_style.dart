part of style;

class CommonTextStyle {
  static final textStyleFontLatoNormal = TextStyle(
    color: AppThemes.colorViettelBlack,
    fontSize: mFontSizeMedium,
    // fontFamily: '<PERSON><PERSON>',
    fontStyle: FontStyle.normal,
    fontWeight: FontWeight.normal,
  );

  static final textStyleFontLatoNormalBold =
      textStyleFontLatoNormal.copyWith(fontWeight: FontWeight.bold);

  static final textStyleFontLatoNormalLight =
      textStyleFontLatoNormal.copyWith(fontWeight: FontWeight.w300);

  static final textStyleFontLatoNormalBoldWhite = textStyleFontLatoNormal
      .copyWith(fontWeight: FontWeight.bold, color: Colors.white);

  static final textStyleFontLatoLargeBoldWhite =
      textStyleFontLatoNormalBoldWhite.copyWith(fontSize: mFontSizeLarge);

  static final textStyleFontLatoLargeBoldBlack =
      textStyleFontLatoNormalBoldWhite.copyWith(
          fontSize: mFontSizeLarge, color: Colors.black);

  static final textStyleFontLatoRedNormal =
      textStyleFontLatoNormal.copyWith(color: AppThemes.colorViettelRed);

  static final textStyleFontLatoRedO2Normal =
      textStyleFontLatoNormal.copyWith(color: AppThemes.colorViettelRed02);

  static final textStyleFontLatoNormalHint =
      textStyleFontLatoNormal.copyWith(color: AppThemes.colorViettelGray2);

  static final textStyleFontLatoNormalGray3 =
      textStyleFontLatoNormal.copyWith(color: AppThemes.colorViettelGray3);

  static final textStyleFontLatoNormalGray1 =
      textStyleFontLatoNormal.copyWith(color: AppThemes.colorViettelGray1);

  static final textStyleFontLatoBigBold =
      textStyleFontLatoLargeBoldBlack.copyWith(fontSize: mFontSizeBig);
}
