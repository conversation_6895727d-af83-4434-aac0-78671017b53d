import 'package:flutter/material.dart';
import 'package:trackcarvcc/constants/style/style.dart';

class Indicator {
  static InputDecoration inputDecorationBorder({
    String labelText,
    String hintText,
    IconData suffixIcon,
    Color suffixIconColor = Colors.grey,
  }) {
    return InputDecoration(
      labelStyle: CommonTextStyle.textStyleFontLatoNormal,
      isDense: true,
      enabledBorder: OutlineInputBorder(
        borderSide: BorderSide(
          width: 1,
          color: Color(0xffB5B4B4),
        ),
        borderRadius: BorderRadius.circular(6),
      ),
      hintText: hintText,
      hintStyle: CommonTextStyle.textStyleFontLatoNormal.copyWith(color: Color(0xff44494D),),
      errorBorder: OutlineInputBorder(
        borderSide: BorderSide(
          width: 1,
          color: Colors.red,
        ),
        borderRadius: BorderRadius.circular(6),
      ),
      errorMaxLines: 3,
      focusedErrorBorder: OutlineInputBorder(
        borderSide: BorderSide(
          width: 1,
          color: Colors.red,
        ),
        borderRadius: BorderRadius.circular(6),
      ),
      focusedBorder: OutlineInputBorder(
        borderSide: BorderSide(
          width: 1.5,
          color: Color(0xff000000),
        ),
        borderRadius: BorderRadius.circular(6),
      ),
      disabledBorder: OutlineInputBorder(
        borderSide: BorderSide(
          width: 1,
          color: Color(0xff44494D),
        ),
        borderRadius: BorderRadius.circular(6),
      ),
      contentPadding: EdgeInsets.symmetric(
        vertical: 12,
        horizontal: 5,
      ),
      labelText: labelText,
      suffixIcon: suffixIcon != null
          ? Icon(
              suffixIcon,
              color: suffixIconColor,
              size: 18,
            )
          : null,
    );
  }
}
