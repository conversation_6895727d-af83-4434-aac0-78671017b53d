import 'dart:math';

import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:trackcarvcc/constants/constants.dart';
import 'package:trackcarvcc/helpers/extensions.dart';
import 'package:trackcarvcc/helpers/string_utils.dart';
import 'package:trackcarvcc/models/base/authentication_info.dart';
import 'package:trackcarvcc/models/models.dart';
import 'package:trackcarvcc/models/request/get_list_book_car_request.dart';
import 'package:trackcarvcc/repository/api/api.dart';
import 'package:trackcarvcc/repository/preferences/data_center.dart';

class ListBookCarController extends GetxController {
  Api api;

  ListBookCarController(this.api);

  final TextEditingController searchController = TextEditingController();

  var appState = Rx<AppState>();
  String searchText = '';
  RxList<LstBookCarDto> listBookCars = RxList<LstBookCarDto>();
  List<LstBookCarDto> backupListBookCars = List<LstBookCarDto>();

  RxString status = RxString();

  int typeMenu;

  void search(String value) {
    searchText = value;
    listBookCars.clear();
    listBookCars.addAll(backupListBookCars
        .where((element) =>
            StringUtils.removeDiacritics(element.code).toLowerCase().contains(
                StringUtils.removeDiacritics(value.trim()).toLowerCase()) ||
            (element.licenseCar != null &&
                StringUtils.removeDiacritics(element.licenseCar)
                    .toLowerCase()
                    .contains(StringUtils.removeDiacritics(value.trim())
                        .toLowerCase())))
        .where(
            (element) => status() == null ? true : element.status == status())
        .toList());
  }

  void filter(String status) {
    this.status.value = status;
    listBookCars.clear();
    listBookCars.addAll(backupListBookCars
        .where((element) =>
            StringUtils.removeDiacritics(element.code).toLowerCase().contains(
                StringUtils.removeDiacritics(searchText.trim())
                    .toLowerCase()) ||
            (element.licenseCar != null &&
                StringUtils.removeDiacritics(element.licenseCar)
                    .toLowerCase()
                    .contains(StringUtils.removeDiacritics(searchText.trim())
                        .toLowerCase())))
        .where((element) =>
            this.status() == null ? true : checkItemStatus(element) == status)
        .toList());
  }

  String checkItemStatus(LstBookCarDto item) {
    switch (typeMenu) {
      case Constants.TYPE_ARRANGE_CAR_MANAGER:
        return item.statusCaptainCar;
      case Constants.TYPE_ARRANGE_CAR_TCT:
        return item.statusDriverBoard;
      case Constants.TYPE_TTHT_QLTS:
        return item.statusTthtPqlts;
      case Constants.TYPE_BROWSING_CAR_TCT:
        return item.statusAdministrative;
      case Constants.TYPE_BROWSING_CAR_MANAGER:
        return item.statusManagerCar;
      case Constants.TYPE_ACCEPT_WORK:
        return item.statusDriver;
      case Constants.TYPE_BOOK_CAR_APPROVAL:
        return item.statusManage;
      default:
        return item.status;
    }
  }

  fetchData() async {
    try {
      appState.value = AppState.LOADING;
      final user = DataCenter.shared().getUserInfo();
      final request = GetListBookCarRequest(
        authenticationInfo: AuthenticationInfo(
          username: user.loginName,
          password: DataCenter.shared().getPassword(),
        ),
        sysUserId: user.sysUserId,
        typeMenu: typeMenu,
        email: user.email,
        loginName: user.loginName,
      );
      final response = await api.getListBookCar(request);

      final result = BaseApiResponse.fromJson(response);
      if (result.success) {
        final data = ListBookCarResponse.fromJson(result.data);
        if (data.resultInfo.status == RESULT_OK) {
          backupListBookCars = data.lstBookCarDto;
          listBookCars = RxList<LstBookCarDto>.from(data.lstBookCarDto);

          appState.value = AppState.DONE;
        } else {
          appState.value = AppState.ERROR;
        }
      } else {
        appState.value = AppState.ERROR;
      }
    } catch (e) {
      appState.value = AppState.ERROR;
    }
  }

  handleRefresh() async {
    try {
      showLoadingDialog();
      final user = DataCenter.shared().getUserInfo();
      final request = GetListBookCarRequest(
        authenticationInfo: AuthenticationInfo(
          username: user.loginName,
          password: DataCenter.shared().getPassword(),
        ),
        sysUserId: user.sysUserId,
        typeMenu: typeMenu,
        email: user.email,
        loginName: user.loginName,
      );
      final response = await api.getListBookCar(request);

      final result = BaseApiResponse.fromJson(response);
      if (result.success) {
        final data = ListBookCarResponse.fromJson(result.data);
        if (data.resultInfo.status == RESULT_OK) {
          backupListBookCars = data.lstBookCarDto;

          listBookCars.clear();
          listBookCars.addAll(backupListBookCars
              .where((element) =>
                  StringUtils.removeDiacritics(element.code)
                      .toLowerCase()
                      .contains(StringUtils.removeDiacritics(searchText.trim())
                          .toLowerCase()) ||
                  (element.licenseCar != null &&
                      StringUtils.removeDiacritics(element.licenseCar)
                          .toLowerCase()
                          .contains(
                              StringUtils.removeDiacritics(searchText.trim())
                                  .toLowerCase())))
              .where((element) =>
                  this.status() == null ? true : element.status == status())
              .toList());
        } else {
          showErrorToast();
        }
      } else {
        showErrorToast();
      }
      dismissLoadingDialog();
    } catch (e) {
      dismissLoadingDialog();
      showErrorToast();
    }
  }

  clear() {
    searchText = '';
    appState = Rx<AppState>();
    status = RxString();
    searchController.clear();
    listBookCars.clear();
    backupListBookCars.clear();
  }
}
