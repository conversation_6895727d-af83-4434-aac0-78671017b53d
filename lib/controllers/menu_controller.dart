import 'package:get/get.dart';
import 'package:location/location.dart';
import 'package:trackcarvcc/constants/constants.dart';
import 'package:trackcarvcc/models/base/authentication_info.dart';
import 'package:trackcarvcc/models/base/base_api_response.dart';
import 'package:trackcarvcc/models/request/update_car_location_request.dart';
import 'package:trackcarvcc/models/response/update_car_location_response.dart';
import 'package:trackcarvcc/repository/api/api.dart';
import 'package:trackcarvcc/helpers/extensions.dart';
import 'package:trackcarvcc/repository/preferences/data_center.dart';

class MenuController extends GetxController {
  Api api;

  MenuController(this.api);

  RxBool switchValue = false.obs;

  updateLocation() async {
    try {
      final user = DataCenter.shared().getUserInfo();
      Location location = Location();
      LocationData _locationData = await location.getLocation();
      UpdateCarLocationRequest request = UpdateCarLocationRequest(
          loginName: user.loginName,
          latitude: _locationData.latitude.toString(),
          longitude: _locationData.longitude.toString(),
          authenticationInfo: AuthenticationInfo(
              username: user.loginName,
              password: DataCenter.shared().getPassword()));
      final response = await api.updateCarLocation(request);

      final result = BaseApiResponse.fromJson(response);
      if (result.success) {
        final data = UpdateCarLocationResponse.fromJson(result.data);
        if (data.resultInfo.status == RESULT_OK) {
          showErrorToast(error: 'Cập nhật vị trí xe thành công');
        } else {
          showErrorToast(error: 'Cập nhật vị trí xe thất bại');
        }
      } else {
        showErrorToast(error: 'Cập nhật vị trí xe thất bại');
      }
    } catch (e) {
      showErrorToast(error: 'Cập nhật vị trí xe thất bại');
    }
  }
}
