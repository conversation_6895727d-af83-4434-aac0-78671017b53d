import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:trackcarvcc/constants/constants.dart';
import 'package:trackcarvcc/models/base/base_api_response.dart';
import 'package:trackcarvcc/models/models.dart';
import 'package:trackcarvcc/models/request/list_quan_huyen_request.dart';
import 'package:trackcarvcc/models/response/list_city_response.dart';
import 'package:trackcarvcc/models/response/list_quan_huyen_response.dart';
import 'package:trackcarvcc/models/response/list_xa_phuong_response.dart';
import 'package:trackcarvcc/repository/api/api.dart';
import 'package:trackcarvcc/repository/preferences/pref.dart';

class AddressController extends GetxController {
  Api api;
  final appState = Rx<AppState>();

  AddressController(this.api);

  final TextEditingController searchController = TextEditingController();

  //
  final TextEditingController cityTextFieldController = TextEditingController();
  final TextEditingController quanHuyenTextFieldController =
      TextEditingController();
  final TextEditingController xaPhuongTextFieldController =
      TextEditingController();
  final TextEditingController thonXomTextFieldController =
      TextEditingController();

  final searchTextCity = ''.obs;
  final searchTextQuanHuyen = ''.obs;
  final searchTextPhuongXa = ''.obs;
  RxList<Area> listCities = RxList<Area>();
  RxList<Area> listQuanHuyen = RxList<Area>();
  RxList<Area> listPhuongXa = RxList<Area>();

  List<Area> backupListCities = List<Area>();
  List<Area> backupListQuanHuyen = List<Area>();
  List<Area> backupListXaPhuong = List<Area>();

  final validate = false.obs;
  final textCity = ''.obs;
  final provinceId = 0.obs;

  void changeCity(String value) => textCity.value = value;
  final textQuanHuyen = ''.obs;

  void changeQuanHuyen(String value) => textQuanHuyen.value = value;
  final textXaPhuong = ''.obs;

  void changeXaPhuong(String value) => textXaPhuong.value = value;
  final textThonXom = ''.obs;

  void changeThonXom(String value) => textThonXom.value = value;

  int cityCode = -1;

  void changeCityCode(int value) => cityCode = value;

  int districtCode = -1;

  void changeDistrictCode(int value) => districtCode = value;

  /// ================================
  /// action
  void searchCity(String value) {
    listCities.clear();
    listCities.addAll(backupListCities
        .where((element) =>
            element.nameLocation.toLowerCase().contains(value.toLowerCase()))
        .toList());
  }

  void searchQuanHuyen(String value) {
    listQuanHuyen.clear();
    listQuanHuyen.addAll(backupListQuanHuyen
        .where((element) =>
            element.nameLocation.toLowerCase().contains(value.toLowerCase()))
        .toList());
  }

  void searchPhuongXa(String value) {
    listPhuongXa.clear();
    listPhuongXa.addAll(backupListXaPhuong
        .where((element) =>
            element.nameLocation.toLowerCase().contains(value.toLowerCase()))
        .toList());
  }

  fetchCity() async {
    try {
      appState.value = AppState.LOADING;
      await Future.delayed(const Duration(milliseconds: 500));

      final response = await api.getListCity();

      final result = BaseApiResponse.fromJson(response);
      if (result.success) {
        final data = ListCityResponse.fromJson(result.data);
        if (data.resultInfo.status == RESULT_OK) {
          listCities = RxList<Area>.from(data.areaProvinceCity);
          backupListCities = data.areaProvinceCity;

          appState.value = AppState.DONE;
        } else {
          appState.value = AppState.ERROR;
        }
      } else {
        appState.value = AppState.ERROR;
      }
    } catch (e) {
      appState.value = AppState.ERROR;
    }
  }

  fetchQuanHuyen({int cityCode}) async {
    try {
      appState.value = AppState.LOADING;
      await Future.delayed(const Duration(milliseconds: 500));

      if (cityCode != null && cityCode != -1) {
        final user = DataCenter.shared().getUserInfo();
        final ListQuanHuyenRequest request = ListQuanHuyenRequest(
            bookCarDto: BookCarDto(parentId: cityCode),
            sysUserRequest: SysUserRequest(
              authenticationInfo: AuthenticationInfo(
                username: user.loginName,
              ),
              sysUserId: user.sysUserId,
            ));

        final response = await api.getListQuanHuyen(request);

        final result = BaseApiResponse.fromJson(response);
        if (result.success) {
          final data = ListQuanHuyenResponse.fromJson(result.data);
          if (data.resultInfo.status == RESULT_OK) {
            listQuanHuyen = RxList<Area>.from(data.areaDistrict);
            backupListQuanHuyen = data.areaDistrict;

            appState.value = AppState.DONE;
          } else {
            appState.value = AppState.ERROR;
          }
        } else {
          appState.value = AppState.ERROR;
        }
      } else {
        appState.value = AppState.UN_DEFINED;
      }
    } catch (e) {
      appState.value = AppState.ERROR;
    }
  }

  fetchPhuongXa({int districtCode}) async {
    try {
      appState.value = AppState.LOADING;
      await Future.delayed(const Duration(milliseconds: 500));

      if (districtCode != null && districtCode != -1) {
        final user = DataCenter.shared().getUserInfo();
        final ListQuanHuyenRequest request = ListQuanHuyenRequest(
            bookCarDto: BookCarDto(parentId: districtCode),
            sysUserRequest: SysUserRequest(
              authenticationInfo: AuthenticationInfo(
                username: user.loginName,
              ),
              sysUserId: user.sysUserId,
            ));

        final response = await api.getListXaPhuong(request);

        final result = BaseApiResponse.fromJson(response);
        if (result.success) {
          final data = ListXaPhuongResponse.fromJson(result.data);
          if (data.resultInfo.status == RESULT_OK) {
            listPhuongXa = RxList<Area>.from(data.areaWard);
            backupListXaPhuong = data.areaWard;

            appState.value = AppState.DONE;
          } else {
            appState.value = AppState.ERROR;
          }
        } else {
          appState.value = AppState.ERROR;
        }
      } else {
        appState.value = AppState.UN_DEFINED;
      }
    } catch (e) {
      appState.value = AppState.ERROR;
    }
  }

  /// =============================
  /// validate
  bool get cityIsValid {
    if (textCity.value.isEmpty && validate.value) return false;
    return true;
  }

  bool get quanHuyenIsValid {
    if ((textCity.value.isEmpty || textQuanHuyen.value.isEmpty) &&
        validate.value) return false;
    return true;
  }

  bool get xaPhuongIsValid {
    if ((textCity.value.isEmpty ||
            textQuanHuyen.value.isEmpty ||
            textXaPhuong.value.isEmpty) &&
        validate.value) return false;
    return true;
  }

  bool get formIsValid =>
      cityIsValid && quanHuyenIsValid && xaPhuongIsValid;
}
