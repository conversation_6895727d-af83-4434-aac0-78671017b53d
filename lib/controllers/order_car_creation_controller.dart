import 'dart:convert';
import 'dart:io';
import 'dart:math';

import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_polyline_points/flutter_polyline_points.dart';
import 'package:get/get.dart';
// import 'package:google_maps_controller/google_maps_controller.dart';
import 'package:location/location.dart';
import 'package:geocoding/geocoding.dart' as geocoding;
import 'package:trackcarvcc/constants/app_state.dart';
import 'package:trackcarvcc/constants/constants.dart';
import 'package:trackcarvcc/helpers/string_utils.dart';
import 'package:trackcarvcc/models/base/file_attachment.dart';
import 'package:trackcarvcc/models/models.dart';
import 'package:trackcarvcc/models/request/create_order_car_request.dart';
import 'package:trackcarvcc/models/request/list_car_request.dart';
import 'package:trackcarvcc/models/request/list_driver_request.dart';
import 'package:trackcarvcc/models/request/list_user_together_request.dart';
import 'package:trackcarvcc/models/request/user_go_with_request.dart';
import 'package:trackcarvcc/models/request/user_manager_request.dart';
import 'package:trackcarvcc/models/request/user_role_request.dart';
import 'package:trackcarvcc/models/response/car_type_response.dart';
import 'package:trackcarvcc/models/response/create_car_response.dart';
import 'package:trackcarvcc/models/response/google_api_distance_response.dart';
import 'package:trackcarvcc/models/response/list_car_response.dart';
import 'package:trackcarvcc/models/response/list_driver_repsonse.dart';
import 'package:trackcarvcc/models/response/list_user_manager_response.dart';
import 'package:trackcarvcc/models/response/list_user_together_response.dart';
import 'package:trackcarvcc/models/response/user_go_with_response.dart';
import 'package:trackcarvcc/models/response/user_role_response.dart';
import 'package:trackcarvcc/models/response/vtmap_distance_response.dart';
import 'package:trackcarvcc/repository/api/api.dart';
import 'package:trackcarvcc/repository/preferences/data_center.dart';
import 'package:trackcarvcc/helpers/extensions.dart';
import 'package:trackcarvcc/ui/app_nav/nav_manager.dart';
import 'package:trackcarvcc/ui/car_order_creation/car_order_creation_page.dart';
import 'package:trackcarvcc/ui/car_order_creation/widget/transition_type_dropdown_sheet.dart';
import 'package:trackcarvcc/utils/download_util.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:intl/intl.dart';
import '../models/base/lst_serve_field.dart';
import 'package:path/path.dart' as path;

class CarOrderCreationController extends GetxController {
  Api api;

  CarOrderCreationController(this.api);

  final appState = Rx<AppState>();

  TextEditingController startPointController = TextEditingController();
  TextEditingController timeStartController = TextEditingController();
  TextEditingController timeFinishController = TextEditingController();
  TextEditingController jobDescriptionController = TextEditingController();
  TextEditingController transitionTypeController = TextEditingController();
  TextEditingController carTypeController = TextEditingController();
  TextEditingController serveFieldController = TextEditingController();
  TextEditingController weightController = TextEditingController();
  TextEditingController userGoWithController = TextEditingController();
  TextEditingController userAcceptController = TextEditingController();
  TextEditingController carTransitionTypeController = TextEditingController();
  TextEditingController driverTransitionTypeController = TextEditingController();
  RxList<FileAttachment> listFile = RxList<FileAttachment>();

  RxString userUnit = ''.obs;
  RxString userName = ''.obs;
  RxString userEmail = ''.obs;
  RxString userPhone = ''.obs;

  RxString startPoint = ''.obs;
  RxInt startPointProvinceId = 0.obs;

  // RxString destinationPoint = ''.obs;
  RxString timeStart = ''.obs;
  RxString timeFinish = ''.obs;
  RxString jobDescription = ''.obs;
  RxString transitionType = ''.obs;
  RxInt transitionCode = 0.obs;
  RxString carType = ''.obs;
  RxString weight = ''.obs;
  RxString userGoWith = ''.obs;
  RxString userAccept = ''.obs;

  RxString carLicence = ''.obs;
  RxString carDriver = ''.obs;

  RxBool validate = false.obs;
  RxBool visibilityOfCarAndDriver = false.obs;
  RxBool visibilityOfNextUserAccept = false.obs;

  RxBool visibilityOfExpectDistance = false.obs;

  RxBool outOfProvince = false.obs;

  String startProvince = '';
  String destinationProvince = '';

  void changeVisibilityOfExpectDistance() {
    if (startPointController.text.isNotEmpty && destinationPoints.isNotEmpty && destinationPoints.last.toAddress.isNotEmpty) {
      visibilityOfExpectDistance.value = true;
      outOfProvince.value = startProvince != destinationProvince;

      expectDistance.value = 0.0;
      // expectTimeDuration.value = 0;
      getListGoogleApiDistance();
    } else {
      visibilityOfExpectDistance.value = false;
    }
  }

  void changeStartPoint(String value) => startPoint.value = value;

  // void changeDestinationPoint(String value) => destinationPoint.value = value;
  void changeDestinationPoint(List<BookCarAddress> value) => destinationPoints.assignAll(value);

  void changeTimeStart(String value) => timeStart.value = value;

  void changeTimeFinish(String value) => timeFinish.value = value;

  void changeJobDescription(String value) => jobDescription.value = value;

  void changeTransitionType(TransitionTypeItem transitionTypeItem) {
    transitionType.value = transitionTypeItem.value;
    transitionCode.value = transitionTypeItem.code;
  }

  void changeCarType(String value, LstBookCarDto carTypeIsChose) {
    carType.value = value;
    carTypeChosen = carTypeIsChose;
  }

  void changeWeight(double value) {
    weightCar.value = value;
    weightController.text = value != null ? value.toString() : 0.0.toString();
  }

  void changeUserGoWith(String value) => userGoWith.value = value;

  void changeUserAccept(String value) => userAccept.value = value;

  void changeCarLicence(String value) => carLicence.value = value;

  void changeCarDriver(String value) => carDriver.value = value;

  RxList<LstApprovePersons> listUserQLTS = RxList<LstApprovePersons>([]);
  LstApprovePersons userQLTSSelected;

  RxList<LstApprovePersons> listUserCarLeader = RxList<LstApprovePersons>([]);
  LstApprovePersons userLeaderSelected;

  RxList<LstApprovePersons> listUserCarCaptain = RxList<LstApprovePersons>([]);
  LstApprovePersons userCaptainSelected;

  RxList<LstBookCarDto> listCarType = RxList<LstBookCarDto>([]);
  LstBookCarDto carTypeChosen;

  List<LstBookCarDto> backUpListCarType = List<LstBookCarDto>();

  RxList<LstBookCarDto> listCar = RxList<LstBookCarDto>([]);
  LstBookCarDto carChosen;
  List<LstBookCarDto> backUpListCar = List<LstBookCarDto>();

  RxList<LstBookCarDto> listCarDriver = RxList<LstBookCarDto>([]);
  LstBookCarDto driverChosen;
  List<LstBookCarDto> backUpListCarDriver = List<LstBookCarDto>();

  RxList<UserLogin> listUserGoWith = RxList<UserLogin>([]);
  List<UserLogin> backUpListUserGoWith = List<UserLogin>();

  RxList<UserLogin> listUserManager = RxList<UserLogin>([]);
  List<UserLogin> backUpListUserManager = List<UserLogin>();
  RxList<UserLogin> listUserManagerChosen = RxList<UserLogin>([]);

  RxList<UserLogin> listUserGoWithChosen = RxList<UserLogin>([]);

  RxInt numberUserChosen = 0.obs;
  RxDouble weightCar = 0.0.obs;

  int typeBookCarChosen = 0;

  RxDouble expectDistance = 0.0.obs;
  RxString expectTime = ''.obs;
  // RxInt expectTimeDuration = 0.obs;

  LstBookCarDto bookCarDtoObj = LstBookCarDto();

  RxList<LstServeFieldDto> lstServeFieldDto = RxList<LstServeFieldDto>([
    LstServeFieldDto(name: 'Vận hành khai thác', code: 1),
    LstServeFieldDto(name: 'Xây dựng B2B', code: 2),
    LstServeFieldDto(name: 'Xây dựng B2C', code: 3),
    LstServeFieldDto(name: 'Giải pháp & Dịch vụ kỹ thuật', code: 4),
    LstServeFieldDto(name: 'Đầu tư hạ tầng', code: 5),
    LstServeFieldDto(name: 'Khối Phòng/Ban (KCQ)', code: 6),
  ]);

  // LstServeFieldDto serveFieldDtoChosen;
  RxList<LstServeFieldDto> listServeFieldDtoChosen = RxList<LstServeFieldDto>([]);

  void synServeFieldDtoChosen(String serveField) {
    listServeFieldDtoChosen.clear();
    var lst = serveField.split(',');
    if (lst.isNotEmpty) {
      lst.forEach((element) {
        var check = lstServeFieldDto.firstWhere(
          (e) => e.code.toString() == element,
          orElse: () => LstServeFieldDto(),
        );
        if (check.code != null) {
          listServeFieldDtoChosen.add(check);
        }
      });
    }
  }

  void deleteServeFieldDtoChosen(int code) {
    if (listServeFieldDtoChosen.isNotEmpty) {
      listServeFieldDtoChosen.removeWhere((element) => element.code == code);
    }
  }

  void addServeFieldDtoChosen(LstServeFieldDto item) {
    if (listServeFieldDtoChosen.contains(item)) {
      showErrorToast(error: 'Bạn đã chọn lĩnh vực này');
    } else {
      listServeFieldDtoChosen.add(item);
    }
  }

  ///===================================================
  ///
  init() {
    timeStartController = TextEditingController(text: StringUtils.parseDateTime24Hour(DateTime.now()));
    changeTimeStart(StringUtils.parseDateTime24Hour(DateTime.now()));
    timeFinishController = TextEditingController(text: StringUtils.parseDateTime24Hour(DateTime.now()));
    changeTimeFinish(StringUtils.parseDateTime24Hour(DateTime.now()));
  }

  /// ==================================================
  /// call api
  ///
  getListGoogleApiDistance() async {
    // await getGoogleApiDistance(startPoint.value, destinationPoints.first.toAddress);
    // if (destinationPoints.length > 1) {
    //   for (int i = 0; i < destinationPoints.length - 1; i++) {
    //     await getGoogleApiDistance(destinationPoints[i].toAddress, destinationPoints[i + 1].toAddress);
    //   }
    // }
    await getGoogleApiDistance(startPoint.value, destinationPoints.map((e) => e.toAddress).toList());
  }

  RxBool getDistanceSuccess = true.obs;

  getGoogleApiDistance(String origins, List<String> destinationPoints) async {
    try {
      expectDistance.value = 0.0;
      showLoadingDialog();
      List<geocoding.Location> originsLocations = await geocoding.locationFromAddress(origins);

      String destinationPoint = '';
      for (var element in destinationPoints) {
        if (element.isNotEmpty) {
          List<geocoding.Location> destinationPointsLocations = await geocoding.locationFromAddress(element);
          destinationPoint += '${destinationPointsLocations.first.latitude},${destinationPointsLocations.first.longitude};';
        }
      }

      final response = await api.vtMapDistanceMatrix({
        'origins': '${originsLocations.first.latitude},${originsLocations.first.longitude}',
        'destinations': destinationPoint,
        'units': 'metric',
        'mode': 'driving',
        'key': Constants.ACCESS_TOKEN,
      });

      final result = BaseApiResponse.fromJson(response);
      if (result.success) {
        final data = VtMapDistanceResponse.fromJson(result.data);
        if (data.status == RESULT_OK) {
          getDistanceSuccess.value = true;
          // notify
          expectDistance.value = data.rows.first.elements.fold(0.0, (sum, element) => sum + element.distance.value / 1000);

          var totalTime = data.rows.first.elements.fold(0, (sum, element) => sum + element.duration.value);

          expectTime.value = totalTime > 3600
              ? '${(totalTime / 3600).floor()} giờ ${(totalTime % 3600 / 60).floor()} phút'
              : '${(totalTime / 60).floor()} phút';

          // expectTimeDuration.value = expectTimeDuration.value + data.rows[0].elements[0].duration.value;
          // expectTime.value = Duration(seconds: expectTimeDuration.value).inHours.toString() +
          //     ' giờ ' +
          //     Duration(seconds: expectTimeDuration.value).inMinutes.remainder(60).toString() +
          //     ' phút';
        } else {
          getDistanceSuccess.value = false;
          // showErrorToast(error: 'Xảy ra lỗi khi lấy thông tin khoảng cách!');
        }
      } else {
        getDistanceSuccess.value = false;

        // showErrorToast(error: 'Xảy ra lỗi khi lấy thông tin khoảng cách!');
      }
      dismissLoadingDialog();
    } catch (e) {
      getDistanceSuccess.value = false;

      // showErrorToast(error: 'Xảy ra lỗi khi lấy thông tin khoảng cách!');
      dismissLoadingDialog();
    }
  }

  Future<bool> addOrUpdateCarOrderCreation(PageType pageType) async {
    try {
      if (destinationPoints.any((element) => element.toAddress.isEmpty)) {
        showErrorToast(error: 'Địa chỉ không được để trống!');
        return false;
      }

      showLoadingDialog();

      if (!kDebugMode) {
        await _getStart(startPoint.value);
        await _getEnd(destinationPoints.last.toAddress);
        await _getPolyline();
      }

      // prepare request info
      CreateOrderCarRequest request = _createCarOrderRequest();
      final response = (pageType == PageType.CREATE_ORDER || pageType == PageType.COPY_ORDER) ? await api.createOrderCarCreation(request) : await api.updateOrderCarCreation(request);

      final result = BaseApiResponse.fromJson(response);
      if (result.success) {
        final data = CreateCarOrderResponse.fromJson(result.data);
        dismissLoadingDialog();
        if (data.resultInfo.status == RESULT_OK) {
          showErrorToast(error: (pageType == PageType.CREATE_ORDER || pageType == PageType.COPY_ORDER) ? 'Tạo phiếu đặt xe thành công!' : 'Cập nhật phiếu đặt xe thành công');
          listServeFieldDtoChosen.clear();
          // navigate to list create order car
          if (pageType == PageType.CREATE_ORDER) {
            NavAppPageManager.shared.moveToPage(NavAppPageScreen.LIST_BOOK_CAR, shouldPopNavDrawer: false);
          } else {
            Get.back(result: true);
          }
          return true;
        } else {
          showErrorToast(error: (pageType == PageType.CREATE_ORDER || pageType == PageType.COPY_ORDER) ? 'Xảy ra lỗi khi tạo phiếu đặt xe!' : 'Xảy ra lỗi khi cập nhật phiếu đặt xe');
          return false;
        }
      } else {
        showErrorToast(error: (pageType == PageType.CREATE_ORDER || pageType == PageType.COPY_ORDER) ? 'Xảy ra lỗi khi tạo phiếu đặt xe!' : 'Xảy ra lỗi khi cập nhật phiếu đặt xe');
        return false;
      }
    } catch (e) {
      showErrorToast(error: (pageType == PageType.CREATE_ORDER || pageType == PageType.COPY_ORDER) ? 'Xảy ra lỗi khi tạo phiếu đặt xe!' : 'Xảy ra lỗi khi cập nhật phiếu đặt xe');
      dismissLoadingDialog();
      return false;
    }
  }

  CreateOrderCarRequest _createCarOrderRequest() {
    final userLogin = DataCenter.shared().getUserInfo();

    // order car info
    LstBookCarDto bookCarDto = LstBookCarDto(
      // todo need remove bookcarid when create car order
      bookCarId: bookCarDtoObj?.bookCarId,
      sysUserId: userLogin.sysUserId,
      loginName: userLogin.loginName,
      fullName: userLogin.fullName,
      email: userLogin.email,
      phoneNumber: userLogin.phoneNumber,
      sysGroupId: userLogin.sysGroupId,
      sysGroupName: userLogin.sysGroupName,
      departmentId: userLogin.departmentId,
      departmentName: userLogin.departmentName,
      carTypeId: carTypeChosen.carTypeId,
      carTypeName: carTypeChosen.carTypeName,
      typeBookCar: typeBookCarChosen.toString(),
      startTime: timeStart.value,
      endTime: timeFinish.value,
      fromAddress: startPoint.value,
      fromProvinceId: startPointProvinceId.value,
      // toAddress: destinationPoint.value,
      bookCarAddress: destinationPoints,
      numPersonTogether: listUserGoWithChosen.length,
      managerStaffId: listUserManagerChosen[0].sysUserId,
      managerStaffEmail: listUserManagerChosen[0].email,
      managerStaffName: listUserManagerChosen[0].fullName,
      carId: typeBookCarChosen == 4 ? carChosen.carId : null,
      licenseCar: typeBookCarChosen == 4 ? carChosen.licenseCar : null,
      driverId: typeBookCarChosen == 4 ? driverChosen.driverId : null,
      driverName: typeBookCarChosen == 4 ? driverChosen.driverName : null,
      driverCode: typeBookCarChosen == 4 ? driverChosen.driverCode : null,
      phoneNumberDriver: typeBookCarChosen == 4 ? driverChosen.phoneNumberDriver : null,
      content: jobDescription.value,
      internalProvince: outOfProvince.value ? 2 : 1,
      estimateDistance: expectDistance.value,
      estimateTime: expectTime.value,
      goodsWeight: weightCar.value,
      truckApproveTtht: int.parse(userQLTSSelected.code),
      truckApproveDtx: int.parse(userLeaderSelected.code),
      truckApproveQlx: int.parse(userCaptainSelected.code),
      code: bookCarDtoObj?.code,
      createdDateView: bookCarDtoObj?.createdDateView,
      serveField: listServeFieldDtoChosen.map((element) => element.code.toString()).toSet().join(','),
      startPoint: startLatLong,
      endPoint: endLatLong,
      routePlans: routePlans,
      listAttachment: listFile,
    );
    // list person go together request
    List<UserLogin> lstPersonTogether = listUserGoWithChosen
        .map((userGoWithObj) => UserLogin(
              defaultSortField: userGoWithObj.defaultSortField,
              email: userGoWithObj.email,
              fullName: userGoWithObj.fullName,
              loginName: userGoWithObj.loginName,
              phoneNumber: userGoWithObj.phoneNumber,
              messageColumn: userGoWithObj.messageColumn,
              sysUserId: userGoWithObj.sysUserId,
            ))
        .toList();
    // sys user request
    SysUserRequest sysUserRequest = SysUserRequest(authenticationInfo: AuthenticationInfo(username: userLogin.loginName));

// add serveField
    return CreateOrderCarRequest(
      bookCarDto: bookCarDto,
      lstPersonTogether: lstPersonTogether,
      sysUserRequest: sysUserRequest,
    );
  }

  fetchUserInfoAndPrepareInfo() async {
    try {
      showLoadingDialog();
      await Future.delayed(const Duration(milliseconds: 500));
      // get user info
      userUnit.value = DataCenter.shared().getUserInfo().departmentName + '-' + DataCenter.shared().getUserInfo().sysGroupName;
      userName.value = DataCenter.shared().getUserInfo().fullName;
      userEmail.value = DataCenter.shared().getUserInfo().email;
      userPhone.value = DataCenter.shared().getUserInfo().phoneNumber;

      // get all user
      await fetchAllUser();
      dismissLoadingDialog();
    } catch (e) {
      showErrorToast(error: 'Xảy ra lỗi khi lấy thông tin!');
      dismissLoadingDialog();
    }
  }

  syncBookCarDtoInfo(int bookCarId) async {
    try {
      showLoadingDialog();
      await Future.delayed(const Duration(milliseconds: 500));
      // sync user info
      userUnit.value = bookCarDtoObj.departmentName + '-' + bookCarDtoObj.sysGroupName ?? '';
      userName.value = bookCarDtoObj.fullName ?? '';
      userEmail.value = bookCarDtoObj.email ?? '';
      userPhone.value = bookCarDtoObj.phoneNumber ?? '';

      // get all user
      await fetchAllUser();

      // sync other info
      if (bookCarDtoObj.startTime != null && bookCarDtoObj.startTime.isNotEmpty) {
        changeTimeStart(StringUtils.parseDateTime24Hour2(bookCarDtoObj.startTime));
        timeStartController.text = bookCarDtoObj.startTime ?? '';
      }

      if (bookCarDtoObj.endTime != null && bookCarDtoObj.endTime.isNotEmpty) {
        changeTimeFinish(StringUtils.parseDateTime24Hour2(bookCarDtoObj.endTime));
        timeFinishController.text = bookCarDtoObj.endTime ?? '';
      }

      changeStartPoint(bookCarDtoObj.fromAddress ?? '');
      startPointProvinceId.value = bookCarDtoObj.fromProvinceId;
      startPointController.text = bookCarDtoObj.fromAddress ?? '';

      changeDestinationPoint(bookCarDtoObj.bookCarAddress);

      changeJobDescription(bookCarDtoObj.content ?? '');
      jobDescriptionController.text = bookCarDtoObj.content ?? '';

      typeBookCarChosen = int.parse(bookCarDtoObj.typeBookCar);
      transitionType.value = typeBookCarChosen == 1
          ? 'Một chiều'
          : typeBookCarChosen == 2
              ? 'Hai chiều'
              : typeBookCarChosen == 3
                  ? 'Phát sinh'
                  : typeBookCarChosen == 4
                      ? 'Đặc biệt'
                      : 'Xe tải';
      transitionTypeController.text = transitionType.value;

      visibilityOfExpectDistance.value = true;
      expectDistance.value = bookCarDtoObj.estimateDistance ?? 0.0;
      expectTime.value = bookCarDtoObj.estimateTime ?? '0';
      outOfProvince.value = bookCarDtoObj.internalProvince == 2;

      if (typeBookCarChosen == 5) {
        // xe tai
        // visible next accept users
        visibilityOfNextUserAccept.value = true;
        // display value on page
        carChosen = LstBookCarDto(
          licenseCar: bookCarDtoObj.licenseCar,
          carId: bookCarDtoObj.carId,
        );

        carTransitionTypeController.text = bookCarDtoObj.licenseCar;

        driverChosen = LstBookCarDto(
          driverId: bookCarDtoObj.driverId,
          driverName: bookCarDtoObj.driverName,
          driverCode: bookCarDtoObj.driverCode,
        );

        driverTransitionTypeController.text = bookCarDtoObj.driverName;
      }

      if (typeBookCarChosen == 4) {
        // dac biet
        // visible car and driver
        visibilityOfCarAndDriver.value = true;
        // display value on page
        carChosen = LstBookCarDto(carId: bookCarDtoObj.carId, licenseCar: bookCarDtoObj.licenseCar);
        driverChosen = LstBookCarDto(driverId: bookCarDtoObj.driverId, driverName: bookCarDtoObj.driverName, driverCode: bookCarDtoObj.driverCode);

        carTransitionTypeController.text = bookCarDtoObj.licenseCar;
        driverTransitionTypeController.text = bookCarDtoObj.driverName;
      }

      final _carTypeChosen = LstBookCarDto(
          carTypeName: bookCarDtoObj.carTypeName,
          toDate: bookCarDtoObj.toDate,
          fromDate: bookCarDtoObj.fromDate,
          carTypeId: bookCarDtoObj.carTypeId,
          weight: bookCarDtoObj.goodsWeight,
          defaultSortField: bookCarDtoObj.defaultSortField,
          messageColumn: bookCarDtoObj.messageColumn);
      changeCarType(bookCarDtoObj.carTypeName, _carTypeChosen);
      carTypeController.text = bookCarDtoObj.carTypeName;

      serveFieldController.text = bookCarDtoObj.serveFieldName;

      changeWeight(bookCarDtoObj.goodsWeight);

      RxList<UserLogin> listUserTogether = await getUserGoTogetherOfUpdateOrder(bookCarId);

      listUserGoWithChosen.addAll(listUserTogether
          .map((userDto) => UserLogin(
              phoneNumber: userDto.phoneNumber,
              email: userDto.email,
              fullName: userDto.fullName,
              loginName: userDto.loginName,
              sysUserId: userDto.sysUserId,
              flag: userDto.flag,
              defaultSortField: userDto.defaultSortField,
              fromDate: userDto.fromDate,
              toDate: userDto.toDate,
              messageColumn: userDto.messageColumn))
          .toList()
          .obs);

      numberUserChosen.value = listUserGoWithChosen.length;

      setInfoOfListAcceptUser();

      dismissLoadingDialog();
    } catch (e) {
      showErrorToast(error: 'Xảy ra lỗi khi đồng bộ thông tin!');
      print('EEEEE: ${e.toString()}');
      dismissLoadingDialog();
    }
  }

  Future<RxList<UserLogin>> getUserGoTogetherOfUpdateOrder(int bookCarId) async {
    try {
      ListUserTogetherRequest request = ListUserTogetherRequest(
          sysUserRequest: SysUserRequest(
              sysUserId: DataCenter.shared().getUserInfo().sysUserId,
              authenticationInfo:
                  AuthenticationInfo(username: DataCenter.shared().getUserInfo().loginName, password: DataCenter.shared().getPassword(), version: '')),
          bookCarDto: bookCarDtoObj);
      final response = await api.getListUserGoTogether(request);

      final result = BaseApiResponse.fromJson(response);
      if (result.success) {
        final data = ListUserTogetherResponse.fromJson(result.data);
        if (data.resultInfo.status == RESULT_OK) {
          return RxList<UserLogin>.from(data.lstBookCarDto);
        } else {
          throw Exception(RESULT_NOK);
        }
      } else {
        throw Exception(result.message);
      }
    } catch (e) {
      throw Exception(e);
    }
  }

  Future<void> getListAttachment(int bookCarId) async {
    try {
      final response = await api.getListAttachment(bookCarId);

      final result = BaseApiResponse.fromJson(response);
      if (result.success) {
        listFile = RxList<FileAttachment>.from(result.data.map((e)=>FileAttachment.fromJson(e)).toList());
        refresh();
      } else {
        throw Exception(result.message);
      }
    } catch (e) {
      throw Exception(e);
    }
  }

  fetchAllUser() async {
    try {
      // get user TTHTQTLS
      listUserQLTS.assignAll(await fetchUser('TRUCK_TTHT_PQLTS'));
      // set default selected qlts
      if (listUserQLTS.isNotEmpty) {
        userQLTSSelected = listUserQLTS.firstWhere((it) => it.code == bookCarDtoObj.truckApproveTtht.toString(), orElse: () => listUserQLTS[0]);
      }

      // get user doi truong xe
      listUserCarLeader.assignAll(await fetchUser('TRUCK_DTX_BXTCT'));
      if (listUserCarLeader.isNotEmpty) {
        userLeaderSelected = listUserCarLeader.firstWhere((it) => it.code == bookCarDtoObj.truckApproveDtx.toString(), orElse: () => listUserCarLeader[0]);
      }
      // get user thu truong quan ly xe
      listUserCarCaptain.assignAll(await fetchUser('TRUCK_QLX'));
      if (listUserCarCaptain.isNotEmpty) {
        userCaptainSelected = listUserCarCaptain.firstWhere((it) => it.code == bookCarDtoObj.truckApproveQlx.toString(), orElse: () => listUserCarCaptain[0]);
      }
    } catch (e) {
      throw Exception(e);
    }
  }

  Future<List<LstApprovePersons>> fetchUser(String roleUser) async {
    try {
      UserRoleRequest request = UserRoleRequest(roleCode: roleUser);
      final response = await api.getListUserByRole(request);

      final result = BaseApiResponse.fromJson(response);
      if (result.success) {
        final data = UserRoleResponse.fromJson(result.data);
        if (data.resultInfo.status == RESULT_OK) {
          return data.lstApprovePersons;
        } else {
          throw Exception(RESULT_NOK);
        }
      } else {
        throw Exception(result.message);
      }
    } catch (e) {
      throw Exception(e);
    }
  }

  fetchListCarType() async {
    try {
      appState.value = AppState.LOADING;
      final response = await api.getListCarType();

      final result = BaseApiResponse.fromJson(response);
      if (result.success) {
        final data = CarTypeResponse.fromJson(result.data);
        if (data.resultInfo.status == RESULT_OK) {
          listCarType = RxList<LstBookCarDto>.from(data.lstBookCarDto);
          backUpListCarType = data.lstBookCarDto;
          appState.value = AppState.DONE;
        } else {
          appState.value = AppState.ERROR;
        }
      } else {
        appState.value = AppState.ERROR;
      }
    } catch (e) {
      appState.value = AppState.ERROR;
    }
  }

  fetchListCar() async {
    try {
      appState.value = AppState.LOADING;
      ListCarRequest request = ListCarRequest(bookCarDto: LstBookCarDto(sysGroupId: DataCenter.shared().getUserInfo().sysGroupId));
      final response = await api.getListCar(request);

      final result = BaseApiResponse.fromJson(response);
      if (result.success) {
        final data = ListCarResponse.fromJson(result.data);
        if (data.resultInfo.status == RESULT_OK) {
          listCar = RxList<LstBookCarDto>.from(data.lstBookCarDto);
          backUpListCar = data.lstBookCarDto;

          appState.value = AppState.DONE;
        } else {
          appState.value = AppState.ERROR;
        }
      } else {
        appState.value = AppState.ERROR;
      }
    } catch (e) {
      appState.value = AppState.ERROR;
    }
  }

  fetchListCarDriver() async {
    try {
      appState.value = AppState.LOADING;
      ListDriverRequest request = ListDriverRequest(
        bookCarDto: LstBookCarDto(sysGroupId: DataCenter.shared().getUserInfo().sysGroupId),
        sysUserRequest: SysUserRequest(
            authenticationInfo: AuthenticationInfo(username: DataCenter.shared().getUserInfo().loginName),
            sysUserId: DataCenter.shared().getUserInfo().sysUserId),
      );
      final response = await api.getListCarDriver(request);

      final result = BaseApiResponse.fromJson(response);
      if (result.success) {
        final data = ListDriverResponse.fromJson(result.data);
        if (data.resultInfo.status == RESULT_OK) {
          listCarDriver = RxList<LstBookCarDto>.from(data.lstBookCarDto);
          backUpListCarDriver = data.lstBookCarDto;

          appState.value = AppState.DONE;
        } else {
          appState.value = AppState.ERROR;
        }
      } else {
        appState.value = AppState.ERROR;
      }
    } catch (e) {
      appState.value = AppState.ERROR;
    }
  }

  fetchListUserGoWith() async {
    try {
      appState.value = AppState.LOADING;
      UserGoWithRequest request = UserGoWithRequest(sysGroupId: DataCenter.shared().getUserInfo().sysGroupId);
      final response = await api.getListUserGoWith(request);

      final result = BaseApiResponse.fromJson(response);
      if (result.success) {
        final data = UserGoWithReponse.fromJson(result.data);
        if (data.resultInfo.status == RESULT_OK) {
          listUserGoWith = RxList<UserLogin>.from(data.lstBookCarDto);

          listUserGoWith.forEach((element) {
            if (listUserGoWithChosen.isNotEmpty) {
              listUserGoWithChosen.forEach((elementChosen) {
                if (element.sysUserId == elementChosen.sysUserId) {
                  element.isChosen = true;
                }
              });
            }
          });

          backUpListUserGoWith = data.lstBookCarDto;
          appState.value = AppState.DONE;
        } else {
          appState.value = AppState.ERROR;
        }
      } else {
        appState.value = AppState.ERROR;
      }
    } catch (e) {
      appState.value = AppState.ERROR;
    }
  }

  fetchListUserManager() async {
    try {
      appState.value = AppState.LOADING;
      UserManagerRequest request =
          UserManagerRequest(sysGroupId: DataCenter.shared().getUserInfo().sysGroupId, departmentId: DataCenter.shared().getUserInfo().departmentId);
      final response = await api.getListUserMananger(request);

      final result = BaseApiResponse.fromJson(response);
      if (result.success) {
        final data = ListUserManagerResponse.fromJson(result.data);
        if (data.resultInfo.status == RESULT_OK) {
          listUserManager = RxList<UserLogin>.from(data.lstBookCarDto);

          listUserManager.forEach((element) {
            if (listUserManagerChosen.isNotEmpty) {
              listUserManagerChosen.forEach((elementChosen) {
                if (element.sysUserId == elementChosen.sysUserId) {
                  element.isChosen = true;
                }
              });
            }
          });

          backUpListUserManager = data.lstBookCarDto;
          appState.value = AppState.DONE;
        } else {
          appState.value = AppState.ERROR;
        }
      } else {
        appState.value = AppState.ERROR;
      }
    } catch (e) {
      appState.value = AppState.ERROR;
    }
  }

  /// ================================
  /// action
  void searchCarType(String value) {
    listCarType.clear();
    listCarType.addAll(backUpListCarType.where((element) => element.carTypeName.toLowerCase().contains(value.toLowerCase())).toList());
  }

  void searchCar(String value) {
    listCar.clear();
    listCar.addAll(backUpListCar.where((element) => element.licenseCar.toLowerCase().contains(value.toLowerCase())).toList());
  }

  void searchCarDriver(String value) {
    listCarDriver.clear();
    listCarDriver.addAll(backUpListCarDriver.where((element) => element.driverName.toLowerCase().contains(value.toLowerCase())).toList());
  }

  void searchUserGoWith(String value) {
    listUserGoWith.clear();
    listUserGoWith.addAll(backUpListUserGoWith
        .where((element) => element.fullName.toLowerCase().contains(value.toLowerCase()) || element.email.toLowerCase().contains(value.toLowerCase()))
        .toList());

    listUserGoWith.forEach((element) {
      if (listUserGoWithChosen.isNotEmpty) {
        listUserGoWithChosen.forEach((elementChosen) {
          if (element.sysUserId == elementChosen.sysUserId) {
            element.isChosen = true;
          }
        });
      }
    });
  }

  void searchUserManager(String value) {
    listUserManager.clear();
    listUserManager.addAll(backUpListUserManager
        .where((element) => element.fullName.toLowerCase().contains(value.toLowerCase()) || element.email.toLowerCase().contains(value.toLowerCase()))
        .toList());

    listUserManager.forEach((element) {
      if (listUserManagerChosen.isNotEmpty) {
        listUserManagerChosen.forEach((elementChosen) {
          if (element.sysUserId == elementChosen.sysUserId) {
            element.isChosen = true;
          }
        });
      }
    });
  }

  void addUserManagerTagged(UserLogin manager) {
    listUserManagerChosen.clear();
    listUserManagerChosen.add(manager);
  }

  void deleteManagerTagged() {
    listUserManagerChosen.clear();
  }

  void deleteUserTagged(int userId) {
    if (listUserGoWithChosen.isNotEmpty) {
      listUserGoWithChosen.removeWhere((element) => element.sysUserId == userId);
      numberUserChosen.value = listUserGoWithChosen.length;
    }
  }

  void addUserManagerTaggedWithList(UserLogin manager) {
    listUserManagerChosen.add(manager);
  }

  void addUserTagged(UserLogin user) {
    listUserGoWithChosen.add(user);
    numberUserChosen.value = listUserGoWithChosen.length;
  }

  void setInfoOfListAcceptUser() {
    // set accept user
    if (bookCarDtoObj.managerStaffName != null && bookCarDtoObj.managerStaffName.isNotEmpty) {
      addUserManagerTagged(UserLogin(
          fullName: bookCarDtoObj.managerStaffName,
          status: bookCarDtoObj.statusManage,
          sysUserId: bookCarDtoObj.managerStaffId,
          email: bookCarDtoObj.managerStaffEmail));
    }
  }

  bool get startPointIsValid => !(startPoint.value.isEmpty && validate.value);

  bool get fileAttachmentIsValid => !((listFile.isEmpty && validate.value) &&
      destinationPoints.any(
          (element) => element.toProvinceId != startPointProvinceId.value));

  // bool get destinationPointIsValid => !(destinationPoint.value.isEmpty && validate.value);
  bool get destinationPointIsValid {
    if (destinationPoints.isEmpty) {
      return false;
    }

    if ((destinationPoints.firstWhere((element) => (element.toAddress ?? '').isEmpty, orElse: () => BookCarAddress(toAddress: '')).toAddress ?? '')
        .isNotEmpty) {
      return false;
    }

    return true;
  }

  bool get timeStartIsValid => !(timeStart.value.isEmpty && validate.value);

  bool get timeFinishIsValid => !(timeFinish.value.isEmpty && validate.value);

  bool get jobDescriptionIsValid => !(jobDescription.value.isEmpty && validate.value);

  bool get transitionTypeIsValid => !(transitionType.value.isEmpty && validate.value);

  bool get carTypeIsValid {
    if (carType.value.isEmpty && validate.value) return false;
    return true;
  }

  bool get serveFieldDtoValid => !(listServeFieldDtoChosen.isEmpty && validate.value);

  bool get weightIsValid => !(weight.value.isEmpty && validate.value);

  bool get userGoWithIsValid => !(listUserGoWithChosen.isEmpty && validate.value);

  bool get userAcceptIsValid => !(listUserManagerChosen.isEmpty && validate.value);

  bool get userCarLicenceIsValid => !(carLicence.value.isEmpty && validate.value);

  bool get userCarDriverIsValid => !(carDriver.value.isEmpty && validate.value);

  bool get timeFromToIsValid {
    if (timeStart.value.isNotEmpty && timeFinish.value.isNotEmpty) {
      DateTime toDate = StringUtils.isEmpty(timeStart.value) ? null : DateFormat('dd/MM/yyyy HH:mm').parse(timeStart.value);

      DateTime fromDate = StringUtils.isEmpty(timeFinish.value) ? null : DateFormat('dd/MM/yyyy HH:mm').parse(timeFinish.value);
      if (toDate != null && fromDate != null && fromDate.compareTo(toDate) >= 0) return true;
    }
    return false;
  }

  bool get formCarOrderCreationIsValid {
    if (startPointIsValid &&
        destinationPointIsValid &&
        timeFinishIsValid &&
        timeStartIsValid &&
        jobDescriptionIsValid &&
        transitionTypeIsValid &&
        carTypeIsValid &&
        userGoWithIsValid &&
        userAcceptIsValid &&
        timeFromToIsValid &&
        fileAttachmentIsValid &&
        serveFieldDtoValid) {
      if (transitionCode.value == 4) {
        // validate
        if (userCarLicenceIsValid && userCarDriverIsValid) {
          return true;
        } else {
          return false;
        }
      } else {
        return true;
      }
    } else {
      return false;
    }
  }

  Future<void> makeAction(String url) async {
    if (await canLaunch(url)) {
      await launch(url);
    } else {
      showErrorToast(error: 'Không thể thực hiện cuộc gọi');
    }
  }

  Future clear() async {
    startPointController.clear();
    timeStartController.clear();
    timeFinishController.clear();
    jobDescriptionController.clear();
    transitionTypeController.clear();
    carTypeController.clear();
    weightController.clear();
    userGoWithController.clear();
    userAcceptController.clear();
    carTransitionTypeController.clear();
    driverTransitionTypeController.clear();
    userUnit = ''.obs;
    userName = ''.obs;
    userEmail = ''.obs;
    userPhone = ''.obs;

    startPoint = ''.obs;
    destinationPoints.clear();
    timeStart = ''.obs;
    timeFinish = ''.obs;
    jobDescription = ''.obs;
    transitionType = ''.obs;
    transitionCode = 0.obs;
    carType = ''.obs;
    weight = ''.obs;
    userGoWith = ''.obs;
    userAccept = ''.obs;

    carLicence = ''.obs;
    carDriver = ''.obs;

    validate = false.obs;
    visibilityOfCarAndDriver = false.obs;
    visibilityOfNextUserAccept = false.obs;

    visibilityOfExpectDistance = false.obs;

    outOfProvince = false.obs;

    startProvince = '';
    destinationProvince = '';
    listUserQLTS.clear();
    userQLTSSelected = LstApprovePersons();

    listUserCarLeader.clear();
    userLeaderSelected = LstApprovePersons();

    listUserCarCaptain.clear();
    userCaptainSelected = LstApprovePersons();
    listCarType.clear();
    carTypeChosen = LstBookCarDto();
    backUpListCarType.clear();

    listCar.clear();
    carChosen = LstBookCarDto();
    backUpListCar.clear();

    listCarDriver.clear();
    driverChosen = LstBookCarDto();
    backUpListCarDriver.clear();

    listUserGoWith.clear();
    backUpListUserGoWith.clear();

    listUserManager.clear();
    backUpListUserManager.clear();
    listUserManagerChosen.clear();

    listUserGoWithChosen.clear();

    numberUserChosen = RxInt(0);
    weightCar = RxDouble(0.0);

    typeBookCarChosen = 0;

    expectDistance = 0.0.obs;
    expectTime = ''.obs;

    bookCarDtoObj = LstBookCarDto();
    listFile.clear();
  }

  // for planned
  Location location = Location();

  Future checkPermissions() async {
    final PermissionStatus permissionGrantedResult = await location.hasPermission();
    if (permissionGrantedResult == PermissionStatus.denied || permissionGrantedResult == PermissionStatus.deniedForever) {
      await location.requestPermission();
    }
  }

  LatLongDto startLatLong = LatLongDto();

  _getStart(String originAddress) async {
    List<geocoding.Location> locations = await geocoding.locationFromAddress(originAddress);
    startLatLong.latitude = locations.first.latitude;
    startLatLong.longitude = locations.first.longitude;
  }

  LatLongDto endLatLong = LatLongDto();

  _getEnd(String destAddress) async {
    List<geocoding.Location> locations = await geocoding.locationFromAddress(destAddress);
    endLatLong.latitude = locations.first.latitude;
    endLatLong.longitude = locations.first.longitude;
  }

  List<LatLongDto> routePlans = <LatLongDto>[];

  _getPolyline() async {
    // List<LatLng> polylineCoordinates = [];
    // PolylineResult result = await PolylinePoints().getRouteBetweenCoordinates(
    //   "AIzaSyArTdhzQmN1HTzNumYvNaKd_foe0MMRgqQ",
    //   PointLatLng(startLatLong.latitude, startLatLong.longitude),
    //   PointLatLng(endLatLong.latitude, endLatLong.longitude),
    //   // travelMode: TravelMode.driving,
    //   // optimizeWaypoints: true,
    // );
    //
    // if (result.points.isNotEmpty) {
    //   result.points.forEach((PointLatLng point) {
    //     polylineCoordinates.add(LatLng(point.latitude, point.longitude));
    //   });
    // } else {
    //   return;
    // }
    //
    // routePlans.addAll(polylineCoordinates.map((e) => LatLongDto(latitude: e.latitude, longitude: e.longitude)).toList());
  }

  RxList<BookCarAddress> destinationPoints = <BookCarAddress>[].obs;

  addDestinationPoints() {
    if (destinationPoints.length >= 5) {
      showErrorToast(error: 'Chỉ được chọn tối đa 5 điểm đến');
      return;
    }
    destinationPoints.add(BookCarAddress());
    _setPositionDestinationPoints();
  }

  removeDestinationPoints(int index) {
    destinationPoints.removeAt(index);
    _setPositionDestinationPoints();
  }

  _setPositionDestinationPoints() {
    for (var item in destinationPoints) {
      item.position = destinationPoints.indexOf(item) + 1;
    }
  }

  setAddressPositionDestinationPoints(int provinceId, String address, int index) {
    destinationPoints[index].toAddress = address;
    destinationPoints[index].toProvinceId = provinceId;
    refresh();
    // refreshGroup(destinationPoints);
    // update(destinationPoints);
    // destinationPoints = List<BookCarAddress>.from(destinationPoints);
  }

  removeAddressPositionDestinationPoints(int index) {
    destinationPoints.removeAt(index);
    refresh();
    // refreshGroup(destinationPoints);
    // update(destinationPoints);
    // destinationPoints = List<BookCarAddress>.from(destinationPoints);
  }

  Future<void> postFile(File file) async {
    try {
      showLoadingDialog();
      final response = await api.postAttachFile(file);
      final result = BaseApiResponse.fromJson(response);
      if (result.success) {
        FileAttachment fileData = FileAttachment.fromJson(result.data);
        listFile.value = List<FileAttachment>.from(listFile)..add(fileData);
        refresh();
        dismissLoadingDialog();
      } else {
        showErrorToast(error: 'Tải lên file thất bại!');
        dismissLoadingDialog();
      }
    } catch (e) {
      showErrorToast(error: 'Tải lên file thất bại!');
      dismissLoadingDialog();
    }
  }

  Future<void> downloadFile({String filePath, String fileName}) async {
    try {
      showLoadingDialog();
      final response = await api.downloadAttachFile(filePath);
      final result = BaseApiResponse.fromJson(response);
      if (result.success) {
        File file = await exportFile(fileName ?? '', result.data["base64String"]);
        if(file != null){
          dismissLoadingDialog();
          Get.snackbar(
            'Lưu thành công',
            'Đường dẫn file: ${file.path}',
            backgroundColor: Colors.green,
            colorText: Colors.white,
            duration: const Duration(seconds: 3),
            borderRadius: 5,
          );
        }else{
          showErrorToast(error: 'Tải file thất bại!');
          dismissLoadingDialog();
        }
      } else {
        showErrorToast(error: 'Tải file thất bại!');
        dismissLoadingDialog();
      }
    } catch (e) {
      showErrorToast(error: 'Tải lên file thất bại!');
      dismissLoadingDialog();
    }
  }

  Future<File> exportFile(String fileName, String base64Str) async {
    try {
      await DownloadUtil.permissionServicesImage();
      String dir = await DownloadUtil.getPath();
      if ((dir ?? '').isNotEmpty) {
        final localPath = path.join(dir, fileName);
        final bytes = base64.decode(base64Str);
        final file = File(localPath);
        await file.writeAsBytes(
          bytes,
        );
        return file;
      }
    } catch (e) {
      showErrorToast(error: 'Tải file thất bại!');
      dismissLoadingDialog();
    }
  }
}
