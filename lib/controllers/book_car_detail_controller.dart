import 'dart:convert';
import 'dart:io';
import 'dart:math';

import 'package:android_intent/android_intent.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:trackcarvcc/constants/constants.dart';
import 'package:trackcarvcc/helpers/extensions.dart';
import 'package:trackcarvcc/helpers/string_utils.dart';
import 'package:trackcarvcc/models/base/file_attachment.dart';
import 'package:trackcarvcc/models/models.dart';
import 'package:trackcarvcc/models/request/close_book_car_request.dart';
import 'package:trackcarvcc/models/request/create_order_car_request.dart';
import 'package:trackcarvcc/models/request/list_car_request.dart';
import 'package:trackcarvcc/models/request/list_driver_request.dart';
import 'package:trackcarvcc/models/request/list_user_together_request.dart';
import 'package:trackcarvcc/models/response/approve_reject_book_car_response.dart';
import 'package:trackcarvcc/models/response/list_car_response.dart';
import 'package:trackcarvcc/models/response/list_driver_repsonse.dart';
import 'package:trackcarvcc/models/response/list_user_together_response.dart';
import 'package:trackcarvcc/repository/api/api.dart';
import 'package:trackcarvcc/repository/preferences/data_center.dart';
import 'package:trackcarvcc/utils/download_util.dart';
import 'package:trackcarvcc/widget/input_working_hour_dialog.dart';
import 'package:trackcarvcc/widget/rate_dialog.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:path/path.dart' as path;

class BookCarDetailController extends GetxController {
  BookCarDetailController(this.api);

  final LIMIT_DISTANCE = 3;

  Api api;
  final appState = Rx<AppState>();
  final bottomSheetState = Rx<AppState>();
  LstBookCarDto bookCar;
  int typeMenu;
  UserLogin userInfo;

  bool hideBottom = false;
  bool approveStatus = true;
  bool denyStatus = true;
  bool editStatus = true;
  bool closeStatus = false;
  bool openStatus = false;

  RxList<UserLogin> listUserManager = RxList<UserLogin>([]);
  RxList<UserLogin> listUserTogeter = RxList<UserLogin>([]);

  RxList<LstBookCarDto> listCar = RxList<LstBookCarDto>([]);
  LstBookCarDto carChosen;
  List<LstBookCarDto> backUpListCar = List<LstBookCarDto>();

  RxList<LstBookCarDto> listCarDriver = RxList<LstBookCarDto>([]);
  LstBookCarDto driverChosen;
  List<LstBookCarDto> backUpListCarDriver = List<LstBookCarDto>();
  RxString carLicence = ''.obs;
  RxString carDriver = ''.obs;
  RxString driverPhoneNumber = ''.obs;
  int carId = 0;
  int carTypeId = 0;
  int driverId = 0;
  String driverCode = '';
  RxInt pairingCar = 1.obs;
  String longtitudeCar = "";
  String latitudeCar = "";
  RxList<FileAttachment> listFile = RxList<FileAttachment>();
  String approveText = 'Duyệt phiếu';

  @override
  void onInit() {
    super.onInit();
    userInfo = DataCenter.shared().getUserInfo();
  }

  Future<void> makeAction(String url) async {
    if (await canLaunch(url)) {
      await launch(url);
    } else {
      showErrorToast(error: 'Không thể thực hiện cuộc gọi');
    }
  }

  init() async {
    appState.value = AppState.LOADING;
    carLicence.value = bookCar.licenseCar;
    carDriver.value = bookCar.driverName;
    driverPhoneNumber.value = bookCar.phoneNumberDriver;
    carId = bookCar.carId;
    driverId = bookCar.driverId;
    driverCode = bookCar.driverCode;
    pairingCar.value = bookCar.pairingCar;
    _setInfoOfListAcceptUser();
    _checkStatus();
    await _getUserGoTogether();
  }

  _getUserGoTogether() async {
    try {
      ListUserTogetherRequest request = ListUserTogetherRequest(
          sysUserRequest: SysUserRequest(
              sysUserId: DataCenter.shared().getUserInfo().sysUserId,
              authenticationInfo: AuthenticationInfo(
                  username: DataCenter.shared().getUserInfo().loginName,
                  password: DataCenter.shared().getPassword(),
                  version: '')),
          bookCarDto: bookCar);
      final response = await api.getListUserGoTogether(request);

      final result = BaseApiResponse.fromJson(response);
      if (result.success) {
        final data = ListUserTogetherResponse.fromJson(result.data);
        if (data.resultInfo.status == RESULT_OK) {
          listUserTogeter = RxList<UserLogin>.from(data.lstBookCarDto);
          appState.value = AppState.DONE;
        } else {
          appState.value = AppState.ERROR;
        }
      } else {
        appState.value = AppState.ERROR;
      }
    } catch (e) {
      appState.value = AppState.ERROR;
    }
  }

  void _setInfoOfListAcceptUser() {
    if (bookCar.managerStaffName != null &&
        bookCar.managerStaffName.isNotEmpty) {
      if (bookCar.statusAdministrative != null &&
          bookCar.statusAdministrative.isNotEmpty &&
          bookCar.statusViceManager == "2") {
        // statusAdministrative da duyet lenh
        _addUserManagerTaggedWithList(UserLogin(
            fullName: bookCar.managerStaffName,
            status: bookCar.statusManage == "5" ? bookCar.statusManage : "6"));
      } else {
        _addUserManagerTaggedWithList(UserLogin(
            fullName: bookCar.managerStaffName, status: bookCar.statusManage));
      }
    }
    if (bookCar.tthtPqltsName != null && bookCar.tthtPqltsName.isNotEmpty) {
      _addUserManagerTaggedWithList(UserLogin(
          fullName: bookCar.tthtPqltsName, status: bookCar.statusTthtPqlts));
    }
    if (bookCar.captainCarName != null && bookCar.captainCarName.isNotEmpty) {
      _addUserManagerTaggedWithList(UserLogin(
          fullName: bookCar.captainCarName, status: bookCar.statusCaptainCar));
    }
    if (bookCar.managerCarName != null && bookCar.managerCarName.isNotEmpty) {
      if (bookCar.typeBookCar == "3") {
        // kieu di phat sinh
        if (bookCar.statusCaptainCar == "2") {
// hien thi nv
          _addUserManagerTaggedWithList(UserLogin(
              fullName: bookCar.fullName,
              status: bookCar.status == "5" ? bookCar.status : "6"));
        }

        if (bookCar.status == "5") {
// hien thi thu truong
          _addUserManagerTaggedWithList(UserLogin(
              fullName: bookCar.managerCarName,
              status: bookCar.statusManagerCar));
        }
      } else {
        _addUserManagerTaggedWithList(UserLogin(
            fullName: bookCar.managerCarName,
            status: bookCar.statusManagerCar));
      }
    }
    if (bookCar.driverBoardName != null && bookCar.driverBoardName.isNotEmpty) {
      _addUserManagerTaggedWithList(UserLogin(
          fullName: bookCar.driverBoardName,
          status: bookCar.statusDriverBoard));
    }

    if (bookCar.administrativeName != null &&
        bookCar.administrativeName.isNotEmpty) {
      _addUserManagerTaggedWithList(UserLogin(
          fullName: bookCar.administrativeName,
          status: bookCar.statusAdministrative));
    }
    if (bookCar.viceManagerName != null && bookCar.viceManagerName.isNotEmpty) {
      _addUserManagerTaggedWithList(UserLogin(
          fullName: bookCar.viceManagerName,
          status: bookCar.statusViceManager));
    }
    if (bookCar.typeBookCar == "2") {
      // kieu di 2 chieu
      if (bookCar.managerCarName != null &&
          bookCar.managerCarName.isNotEmpty &&
          bookCar.statusManagerCar == "2") {
        // managerCarName da phe duyet
        _addUserManagerTaggedWithList(UserLogin(
            fullName: bookCar.fullName,
            status: bookCar.status == "5" ? bookCar.status : "6"));
      }
    } else if (bookCar.typeBookCar == "1") {
      // kieu di 1 chieu
      if (bookCar.managerCarName != null &&
          bookCar.managerCarName.isNotEmpty &&
          bookCar.statusManagerCar == "2") {
        // managerCarName da phe duyet
        _addUserManagerTaggedWithList(UserLogin(
            fullName: bookCar.fullName,
            status: bookCar.status == "5" ? bookCar.status : "6"));
      }
      if (bookCar.status != null &&
          bookCar.status.isNotEmpty &&
          bookCar.status == "5") {
        // nhan vien dong lenh
        _addUserManagerTaggedWithList(UserLogin(
            fullName: bookCar.driverName,
            status: (bookCar.statusDriver != null &&
                    bookCar.statusDriver.isNotEmpty &&
                    bookCar.statusDriver == "5")
                ? bookCar.statusDriver
                : "6"));
      }
    } else if (bookCar.typeBookCar == "5") {
      // kieu di luong xe tai.
      if (bookCar.managerCarName != null &&
          bookCar.managerCarName.isNotEmpty &&
          bookCar.statusManagerCar == "2") {
        // managerCarName da phe duyet
        _addUserManagerTaggedWithList(UserLogin(
            fullName: bookCar.driverName,
            status: bookCar.status == "5" ? bookCar.status : "6"));
      }
      if (bookCar.status != null &&
          bookCar.status.isNotEmpty &&
          bookCar.status == "5") {
        // nhan vien dong lenh
        _addUserManagerTaggedWithList(UserLogin(
            fullName: bookCar.driverName,
            status: (bookCar.statusDriver != null &&
                    bookCar.statusDriver.isNotEmpty &&
                    bookCar.statusDriver == "5")
                ? bookCar.statusDriver
                : "6"));
      }
    }
  }

  void _addUserManagerTaggedWithList(UserLogin manager) {
    listUserManager.add(manager);
  }

  void _checkStatus() {
    if (typeMenu == Constants.TYPE_PTGD) {
      hideBottom =
          !(bookCar?.typeBookCar == "4" && bookCar.statusViceManager == "1");
    }
    if (typeMenu == Constants.TYPE_TTHT_QLTS) {
      hideBottom =
          !(bookCar?.typeBookCar == "5" && bookCar.statusTthtPqlts == "1");
    }
    if (typeMenu == Constants.TYPE_LIST_BOOK_CAR) {
      if ((bookCar.typeBookCar == "1" || bookCar.typeBookCar == "2") &&
          bookCar.status == "2" &&
          bookCar.statusCaptainCar == "2" &&
          bookCar.statusManagerCar == "2") {
        _closeBookCar();
      } else if (bookCar.typeBookCar == "3" &&
          bookCar.status == "2" &&
          bookCar.statusCaptainCar == "2") {
        _closeBookCar();
      } else if (bookCar.typeBookCar == "5" &&
          bookCar.status == "2" &&
          bookCar.statusManagerCar == "2" &&
          bookCar.statusCaptainCar == "2") {
        _closeBookCar();
      } else {
        hideBottom = true;
      }
    }

    if (typeMenu == Constants.TYPE_BOOK_CAR_APPROVAL) {
      if (bookCar.statusManage == "1") {
        // duyet tu cho sua
      } else if (bookCar.statusManage == "3" ||
          bookCar.statusManage == "4" ||
          bookCar.statusManage == "5" ||
          bookCar.statusManage == "6") {
        hideBottom = true;
      } else if (bookCar.statusManage == "2" &&
          (bookCar.statusAdministrative != "2")) {
        hideBottom = true;
      } else if (bookCar.statusManage == "2" &&
          bookCar.statusViceManager == "1") {
        hideBottom = true;
      } else if (bookCar.statusManage == "2" &&
          bookCar.statusViceManager == "2") {
        _closeBookCar();
      }
    }

    if (typeMenu == Constants.TYPE_BROWSING_CAR_MANAGER) {
      if (bookCar.statusManagerCar == "1" &&
          (bookCar.typeBookCar == "1" || bookCar.typeBookCar == "2")) {
        // duyet, tu choi, yeu cau sua
        approveText = 'Duyệt lệnh';
      } else if (bookCar.statusManagerCar == "1" &&
          bookCar.typeBookCar == "3" &&
          bookCar.status == "5") {
        // duyet lenh
        hideBottom = false;
        approveStatus = true;
        approveText = 'Duyệt lệnh';
        denyStatus = false;
        editStatus = false;
        closeStatus = false;
        openStatus = false;
      } else if (bookCar.statusManagerCar == "1" &&
          bookCar.typeBookCar == "3" &&
          bookCar.status != "5") {
        // view lenh
        hideBottom = true;
      } else if (bookCar.statusManagerCar != "1") {
        // view lenh
        hideBottom = true;
      }
    }

    if (typeMenu == Constants.TYPE_ARRANGE_CAR_TCT) {
      if (bookCar.statusDriverBoard != "1") {
        hideBottom = true;
      }
    }

    if (typeMenu == Constants.TYPE_BROWSING_CAR_TCT) {
      if (bookCar.statusAdministrative != "1") {
        // view lenh
        hideBottom = true;
      } else {
        approveText = 'Duyệt lệnh';
      }
    }

    if (typeMenu == Constants.TYPE_ACCEPT_WORK) {
      if (bookCar.typeBookCar == "1" &&
          bookCar.status == "5" &&
          bookCar.statusDriver != "5") {
        hideBottom = false;
        approveStatus = false;
        denyStatus = false;
        editStatus = false;
        openStatus = false;
        closeStatus = true;
      } else if (bookCar.typeBookCar == "5" &&
          bookCar.status == "2" &&
          bookCar.statusManagerCar == "2" &&
          bookCar.statusDriver != "5") {
        hideBottom = false;
        approveStatus = false;
        denyStatus = false;
        editStatus = false;
        openStatus = false;
        closeStatus = true;
      } else {
        hideBottom = true;
      }
    }

    if (typeMenu == Constants.TYPE_ARRANGE_CAR_MANAGER) {
      if (bookCar.statusCaptainCar != "1") {
        hideBottom = true;
      }
    }
  }

  void _closeBookCar() {
    hideBottom = false;
    openStatus = bookCar.typeBookCar != "4" && bookCar.toAddressExtend == null;
    approveStatus = false;
    denyStatus = false;
    editStatus = false;
    closeStatus = bookCar.typeBookCar != "5";
  }

  swichFetchApi(int flag, String reason) {
    switch (typeMenu) {
      case Constants.TYPE_BOOK_CAR_APPROVAL:
        fetchManageApproveRejectBookCar(flag, reason);
        break;
      case Constants.TYPE_BROWSING_CAR_MANAGER:
        fetchManagerCarApproveRejectBookCar(flag, reason);
        break;
      case Constants.TYPE_ARRANGE_CAR_TCT:
        fetchDriverBoardApproveRejectBookCar(flag, reason);
        break;
      case Constants.TYPE_BROWSING_CAR_TCT:
        fetchAdministrativeApproveRejectBookCar(flag, reason);
        break;
      case Constants.TYPE_PTGD:
        viceManagerApproveRejectBookCar(flag, reason);
        break;
      case Constants.TYPE_TTHT_QLTS:
        tthtPqltsApproveRejectBookCar(flag, reason);
        break;
      case Constants.TYPE_ARRANGE_CAR_MANAGER:
        fetchCaptainCarApproveRejectBookCar(flag, reason);
        break;
    }
  }

  fetchManageApproveRejectBookCar(int flag, String reason) async {
    showLoadingDialog();
    final LstBookCarDto bookCarDto = bookCar;
    if (bookCarDto.typeBookCar == "5" && flag == 2) {
      bookCarDto.nextPersionId = reason;
    } else {
      bookCarDto.reasonManage = reason;
    }
    final userInfo = DataCenter.shared().getUserInfo();
    final request = ListUserTogetherRequest(
      bookCarDto: bookCarDto,
      sysUserRequest: SysUserRequest(
        authenticationInfo: AuthenticationInfo(
          username: userInfo.loginName,
          password: DataCenter.shared().getPassword(),
        ),
        sysUserId: userInfo.sysUserId,
        flag: flag,
        email: userInfo.email,
        loginName: userInfo.loginName,
      ),
    );

    try {
      final response = await api.manageApproveRejectBookCar(request);

      final result = BaseApiResponse.fromJson(response);
      dismissLoadingDialog();
      if (result.success) {
        final data = ApproveRejectBookCarResponse.fromJson(result.data);
        showErrorToast(error: data.resultInfo.message);
        if (data.resultInfo.status == RESULT_OK) {
          Get.back<bool>(result: true);
        }
      } else {
        showErrorToast();
      }
    } catch (e) {
      showErrorToast();
      dismissLoadingDialog();
    }
  }

  fetchManagerCarApproveRejectBookCar(int flag, String reason) async {
    showLoadingDialog();
    final LstBookCarDto bookCarDto = bookCar;
    bookCarDto.reasonManagerCar = reason;
    final userInfo = DataCenter.shared().getUserInfo();
    final request = ListUserTogetherRequest(
      bookCarDto: bookCarDto,
      sysUserRequest: SysUserRequest(
        authenticationInfo: AuthenticationInfo(
          username: userInfo.loginName,
          password: DataCenter.shared().getPassword(),
        ),
        sysUserId: userInfo.sysUserId,
        flag: flag,
        email: userInfo.email,
        loginName: userInfo.loginName,
      ),
    );

    try {
      final response = await api.managerCarApproveRejectBookCar(request);

      final result = BaseApiResponse.fromJson(response);
      dismissLoadingDialog();
      if (result.success) {
        final data = ApproveRejectBookCarResponse.fromJson(result.data);
        showErrorToast(error: data.resultInfo.message);
        if (data.resultInfo.status == RESULT_OK) {
          Get.back<bool>(result: true);
        }
      } else {
        showErrorToast();
      }
    } catch (e) {
      showErrorToast();
      dismissLoadingDialog();
    }
  }

  fetchDriverBoardApproveRejectBookCar(int flag, String reason) async {
    showLoadingDialog();
    final LstBookCarDto bookCarDto = bookCar;
    bookCarDto.reasonDriverBoard = reason;
    final userInfo = DataCenter.shared().getUserInfo();
    final request = ListUserTogetherRequest(
      bookCarDto: bookCarDto,
      sysUserRequest: SysUserRequest(
        authenticationInfo: AuthenticationInfo(
          username: userInfo.loginName,
          password: DataCenter.shared().getPassword(),
        ),
        sysUserId: userInfo.sysUserId,
        flag: flag,
        email: userInfo.email,
        loginName: userInfo.loginName,
      ),
    );

    try {
      final response = await api.driverBoardApproveRejectBookCar(request);

      final result = BaseApiResponse.fromJson(response);
      dismissLoadingDialog();
      if (result.success) {
        final data = ApproveRejectBookCarResponse.fromJson(result.data);
        showErrorToast(error: data.resultInfo.message);
        if (data.resultInfo.status == RESULT_OK) {
          Get.back<bool>(result: true);
        }
      } else {
        showErrorToast();
      }
    } catch (e) {
      showErrorToast();
      dismissLoadingDialog();
    }
  }

  fetchAdministrativeApproveRejectBookCar(int flag, String reason) async {
    showLoadingDialog();
    final LstBookCarDto bookCarDto = bookCar;
    bookCarDto.reasonAdministrative = reason;
    final userInfo = DataCenter.shared().getUserInfo();
    final request = ListUserTogetherRequest(
      bookCarDto: bookCarDto,
      sysUserRequest: SysUserRequest(
        authenticationInfo: AuthenticationInfo(
          username: userInfo.loginName,
          password: DataCenter.shared().getPassword(),
        ),
        sysUserId: userInfo.sysUserId,
        flag: flag,
        email: userInfo.email,
        loginName: userInfo.loginName,
      ),
    );

    try {
      final response = await api.administrativeApproveRejectBookCar(request);

      final result = BaseApiResponse.fromJson(response);
      dismissLoadingDialog();
      if (result.success) {
        final data = ApproveRejectBookCarResponse.fromJson(result.data);
        if (data.resultInfo.status == RESULT_OK) {
          showErrorToast(error: data.resultInfo.message);
          Get.back<bool>(result: true);
        } else {
          showErrorToast();
        }
      } else {
        showErrorToast();
      }
    } catch (e) {
      showErrorToast();
      dismissLoadingDialog();
    }
  }

  viceManagerApproveRejectBookCar(int flag, String reason) async {
    showLoadingDialog();
    final LstBookCarDto bookCarDto = bookCar;
    bookCarDto.reasonViceManager = reason;
    final userInfo = DataCenter.shared().getUserInfo();
    final request = ListUserTogetherRequest(
      bookCarDto: bookCarDto,
      sysUserRequest: SysUserRequest(
        authenticationInfo: AuthenticationInfo(
          username: userInfo.loginName,
          password: DataCenter.shared().getPassword(),
        ),
        sysUserId: userInfo.sysUserId,
        flag: flag,
        email: userInfo.email,
        loginName: userInfo.loginName,
      ),
    );

    try {
      final response = await api.viceManagerApproveRejectBookCar(request);

      final result = BaseApiResponse.fromJson(response);
      dismissLoadingDialog();
      if (result.success) {
        final data = ApproveRejectBookCarResponse.fromJson(result.data);
        if (data.resultInfo.status == RESULT_OK) {
          showErrorToast(error: data.resultInfo.message);
          Get.back<bool>(result: true);
        } else {
          showErrorToast();
        }
      } else {
        showErrorToast();
      }
    } catch (e) {
      showErrorToast();
      dismissLoadingDialog();
    }
  }

  tthtPqltsApproveRejectBookCar(int flag, String reason) async {
    showLoadingDialog();
    final LstBookCarDto bookCarDto = bookCar;
    bookCarDto.reasonTthtPqlts = reason;
    final userInfo = DataCenter.shared().getUserInfo();
    final request = ListUserTogetherRequest(
      bookCarDto: bookCarDto,
      sysUserRequest: SysUserRequest(
        authenticationInfo: AuthenticationInfo(
          username: userInfo.loginName,
          password: DataCenter.shared().getPassword(),
        ),
        sysUserId: userInfo.sysUserId,
        flag: flag,
        email: userInfo.email,
        loginName: userInfo.loginName,
      ),
    );

    try {
      final response = await api.tthtPqltsApproveRejectBookCar(request);

      final result = BaseApiResponse.fromJson(response);
      dismissLoadingDialog();
      if (result.success) {
        final data = ApproveRejectBookCarResponse.fromJson(result.data);
        if (data.resultInfo.status == RESULT_OK) {
          showErrorToast(error: data.resultInfo.message);
          Get.back<bool>(result: true);
        } else {
          showErrorToast();
        }
      } else {
        showErrorToast();
      }
    } catch (e) {
      showErrorToast();
      dismissLoadingDialog();
    }
  }

  fetchCaptainCarApproveRejectBookCar(int flag, String reason) async {
    showLoadingDialog();
    final LstBookCarDto bookCarDto = bookCar;
    bookCarDto.reasonCaptainCar = reason;
    if (flag == 2) {
      bookCarDto.carId = carId;
      bookCarDto.carTypeId = carTypeId;
      bookCarDto.latitudeCar = latitudeCar;
      bookCarDto.longtitudeCar = longtitudeCar;
      bookCarDto.licenseCar = carLicence();
      bookCarDto.driverId = driverId;
      bookCarDto.driverName = carDriver();
      bookCarDto.driverCode = driverCode;
      bookCarDto.phoneNumberDriver = driverPhoneNumber();
      bookCarDto.pairingCar = pairingCar() ?? 0;
    }

    final userInfo = DataCenter.shared().getUserInfo();
    final request = ListUserTogetherRequest(
      bookCarDto: bookCarDto,
      sysUserRequest: SysUserRequest(
        authenticationInfo: AuthenticationInfo(
          username: userInfo.loginName,
          password: DataCenter.shared().getPassword(),
        ),
        sysUserId: userInfo.sysUserId,
        flag: flag,
        email: userInfo.email,
        loginName: userInfo.loginName,
      ),
    );

    try {
      final response = await api.captainCarApproveRejectBookCar(request);

      final result = BaseApiResponse.fromJson(response);
      dismissLoadingDialog();
      if (result.success) {
        final data = ApproveRejectBookCarResponse.fromJson(result.data);
        showErrorToast(error: data.resultInfo.message);
        if (data.resultInfo.status == RESULT_OK) {
          Get.back<bool>(result: true);
        }
      } else {
        showErrorToast();
      }
    } catch (e) {
      showErrorToast();
      dismissLoadingDialog();
    }
  }

  closeBookCar() async {
    // carTypeId == 8 ||
    // carTypeId == 9
    var timeDiffInDays = _getTimeBetweenStartAndEndInDay() + 1;
    if (bookCar?.carTypeId == 8 || bookCar?.carTypeId == 9) {
      Get.dialog(InputWorkingHereDialog(
        timeDiffInDays: timeDiffInDays,
        onOKClick: (time) {
          Get.back();
          var timeNumb = double.parse(time);
          bookCar.workingHere = timeNumb;
          _requestCloseBookCar();
        },
      ));
    } else {
      _requestCloseBookCar();
    }
  }

  _requestCloseBookCar() async {
    try {
      showLoadingDialog();
      final userInfo = DataCenter.shared().getUserInfo();
      final request = CloseBookCarRequest(
        bookCarDto: bookCar,
        sysUserRequest: SysUserRequest(
          authenticationInfo: AuthenticationInfo(
            username: userInfo.loginName,
            password: DataCenter.shared().getPassword(),
          ),
          sysUserId: userInfo.sysUserId,
          email: userInfo.email,
          loginName: userInfo.loginName,
        ),
      );
      dynamic response;
      if (typeMenu == Constants.TYPE_ACCEPT_WORK) {
        response = await api.closeDriverBookCar(request);
      } else {
        if (typeMenu == Constants.TYPE_BOOK_CAR_APPROVAL &&
            bookCar.typeBookCar == "4") {
          response = await api.closeManagerBookCar(request);
        } else {
          response = await api.closeBookCar(request);
        }
      }

      final result = BaseApiResponse.fromJson(response);
      dismissLoadingDialog();
      if (result.success) {
        final data = ApproveRejectBookCarResponse.fromJson(result.data);
        if (data.resultInfo.status == RESULT_OK) {
          showErrorToast(error: data.resultInfo.message);
          if (typeMenu != Constants.TYPE_ACCEPT_WORK) {
            Get.dialog(RateDialog(
              onRating: (score, scoreText) {
                _rateBookCar(score, scoreText);
              },
            ));
          } else {
            Get.back<bool>(result: true);
          }
        } else {
          showErrorToast();
        }
      } else {
        showErrorToast();
      }
    } catch (e) {
      showErrorToast();
      dismissLoadingDialog();
    }
  }

  _getTimeBetweenStartAndEndInDay() {
    try {
      if (bookCar.endTimeExtend != null &&
          bookCar.endTimeExtend.isNotEmpty &&
          bookCar.startTime != null &&
          bookCar.startTime.isNotEmpty) {
        var dateTimeStart = StringUtils.formatDateTime(bookCar.startTime);
        var dateTimeEnd = StringUtils.formatDateTime(bookCar.endTimeExtend);
        var timeInDay = dateTimeEnd.difference(dateTimeStart).inDays;
        return timeInDay;
      }
      if (bookCar.startTime != null &&
          bookCar.startTime.isNotEmpty &&
          bookCar.endTime != null &&
          bookCar.endTime.isNotEmpty) {
        var dateTimeStart = StringUtils.formatDateTime(bookCar.startTime);
        var dateTimeEnd = StringUtils.formatDateTime(bookCar.endTime);
        var timeInDay = dateTimeEnd.difference(dateTimeStart).inDays;
        return timeInDay;
      }
    } catch (e) {
      showErrorToast(error: e.toString());
      return 0;
    }
  }

  _rateBookCar(int score, String scoreText) async {
    showLoadingDialog();
    LstBookCarDto bookCarDto = LstBookCarDto(
      bookCarId: bookCar.bookCarId,
      score: score,
      scoreText: scoreText,
    );
    final request = CreateOrderCarRequest(
      bookCarDto: bookCarDto,
      sysUserRequest: SysUserRequest(
        authenticationInfo: AuthenticationInfo(
          username: DataCenter.shared().getUserInfo().loginName,
          password: DataCenter.shared().getPassword(),
        ),
      ),
      lstPersonTogether: listUserTogeter,
    );

    try {
      final response = await api.rateBookCar(request);
      final result = BaseApiResponse.fromJson(response);
      dismissLoadingDialog();
      if (result.success) {
        final data = ApproveRejectBookCarResponse.fromJson(result.data);
        if (data.resultInfo.status == RESULT_OK) {
          showErrorToast(error: result.message);
          Get.back<bool>(result: true);
        } else {
          showErrorToast();
        }
      } else {
        showErrorToast();
      }
    } catch (e) {
      showErrorToast();
      dismissLoadingDialog();
    }
  }

  getApiMatricTest() async {
    if (GetPlatform.isAndroid) {
      final AndroidIntent intent = AndroidIntent(
          action: 'action_view',
          data: Uri.encodeFull(
              "https://www.google.com/maps/dir/?api=1&origin=" +
                  bookCar.fromAddress +
                  "&destination=" +
                  bookCar.toAddress +
                  "&travelmode=driving"),
          package: 'com.google.android.apps.maps');
      intent.launch();
    } else {
      String url = "https://www.google.com/maps/dir/?api=1&origin=" +
          bookCar.fromAddress +
          "&destination=" +
          bookCar.toAddress +
          "&travelmode=driving";
      if (await canLaunch(url)) {
        await launch(url);
      } else {
        showErrorToast();
      }
    }
  }

  openNavigationCurrent() async {
    if (GetPlatform.isAndroid) {
      final AndroidIntent intent = AndroidIntent(
          action: 'action_view',
          data: Uri.encodeFull("google.navigation:q=" + bookCar.toAddress),
          package: 'com.google.android.apps.maps');
      intent.launch();
    } else {
      String url = "google.navigation:q=" + bookCar.toAddress;
      if (await canLaunch(url)) {
        await launch(url);
      } else {
        showErrorToast();
      }
    }
  }

  fetchListCar() async {
    try {
      bottomSheetState.value = AppState.LOADING;
      ListCarRequest request = ListCarRequest(
          bookCarDto: LstBookCarDto(
              sysGroupId: DataCenter.shared().getUserInfo().sysGroupId));
      final response = await api.getListCar(request);

      final result = BaseApiResponse.fromJson(response);
      if (result.success) {
        final data = ListCarResponse.fromJson(result.data);
        if (data.resultInfo.status == RESULT_OK) {
          listCar = RxList<LstBookCarDto>.from(data.lstBookCarDto);
          backUpListCar = data.lstBookCarDto;

          bottomSheetState.value = AppState.DONE;
        } else {
          bottomSheetState.value = AppState.ERROR;
        }
      } else {
        bottomSheetState.value = AppState.ERROR;
      }
    } catch (e) {
      bottomSheetState.value = AppState.ERROR;
    }
  }

  fetchListCarDriver() async {
    try {
      bottomSheetState.value = AppState.LOADING;
      ListDriverRequest request = ListDriverRequest(
        bookCarDto: LstBookCarDto(
            sysGroupId: DataCenter.shared().getUserInfo().sysGroupId),
        sysUserRequest: SysUserRequest(
            authenticationInfo: AuthenticationInfo(
                username: DataCenter.shared().getUserInfo().loginName),
            sysUserId: DataCenter.shared().getUserInfo().sysUserId),
      );
      final response = await api.getListCarDriver(request);

      final result = BaseApiResponse.fromJson(response);
      if (result.success) {
        final data = ListDriverResponse.fromJson(result.data);
        if (data.resultInfo.status == RESULT_OK) {
          listCarDriver = RxList<LstBookCarDto>.from(data.lstBookCarDto);
          backUpListCarDriver = data.lstBookCarDto;

          bottomSheetState.value = AppState.DONE;
        } else {
          bottomSheetState.value = AppState.ERROR;
        }
      } else {
        bottomSheetState.value = AppState.ERROR;
      }
    } catch (e) {
      bottomSheetState.value = AppState.ERROR;
    }
  }

  void searchCar(String value) {
    listCar.clear();
    listCar.addAll(backUpListCar
        .where((element) =>
            element.licenseCar.toLowerCase().contains(value.toLowerCase()))
        .toList());
  }

  void searchCarDriver(String value) {
    listCarDriver.clear();
    listCarDriver.addAll(backUpListCarDriver
        .where((element) =>
            element.driverName.toLowerCase().contains(value.toLowerCase()))
        .toList());
  }

  void changeCarLicence(LstBookCarDto car) {
    this.carLicence.value = car.licenseCar;
    this.carId = car.carId;
    this.carTypeId = car.carTypeId;
    this.latitudeCar = car.latitudeCar;
    this.longtitudeCar = car.longtitudeCar;

    try {
      final lat1 = double.parse(car.latitudeCar);
      final long1 = double.parse(car.longtitudeCar);
      final lat2 = double.parse(car.latitudeDriver);
      final long2 = double.parse(car.longtitudeDriver);

      if (car.onCommandDriver == "0" &&
          car.latitudeDriver != null &&
          calculateDistance(lat1, long1, lat2, long2) <= LIMIT_DISTANCE) {
        this.driverId = car.driverId;
        carDriver.value = car.driverName;
        this.driverCode = car.driverCode;
        driverPhoneNumber.value = car.phoneNumberDriver;
      }
    } catch (e) {
      return;
    }
  }

  void changeCarDriver(
      String name, String phone, int driverId, String driverCode) {
    carDriver.value = name;
    driverPhoneNumber.value = phone;
    this.driverId = driverId;
    this.driverCode = driverCode;
  }

  double calculateDistance(
      latitudeCar, longtitudeCar, latitudeDriver, longtitudeDriver) {
    if ((latitudeCar == latitudeDriver) &&
        (longtitudeCar == longtitudeDriver)) {
      return 0;
    } else {
      double theta = longtitudeCar - longtitudeDriver;
      double dist =
          sin(toRadians(latitudeCar)) * sin(toRadians(latitudeDriver)) +
              cos(toRadians(latitudeCar)) *
                  cos(toRadians(latitudeDriver)) *
                  cos(toRadians(theta));
      dist = acos(dist);
      dist = toDegrees(dist);
      dist = dist * 60 * 1.1515;
      dist = dist * 1.609344;
      return (dist);
    }
  }

  num toRadians(num deg) {
    return (deg * pi) / 180.0;
  }

  num toDegrees(num rad) {
    return (rad * 180.0) / pi;
  }

  Future<void> getListAttachment(int bookCarId) async {
    try {
      final response = await api.getListAttachment(bookCarId);

      final result = BaseApiResponse.fromJson(response);
      if (result.success) {
        listFile = RxList<FileAttachment>.from(result.data.map((e)=>FileAttachment.fromJson(e)).toList());
        refresh();
      } else {
        throw Exception(result.message);
      }
    } catch (e) {
      throw Exception(e);
    }
  }

  Future<void> downloadFile({String filePath, String fileName}) async {
    try {
      showLoadingDialog();
      final response = await api.downloadAttachFile(filePath);
      final result = BaseApiResponse.fromJson(response);
      if (result.success) {
        File file = await exportFile(fileName ?? '', result.data["base64String"]);
        if(file != null){
          dismissLoadingDialog();
          Get.snackbar(
            'Lưu thành công',
            'Đường dẫn file: ${file.path}',
            backgroundColor: Colors.green,
            colorText: Colors.white,
            duration: const Duration(seconds: 3),
            borderRadius: 5,
          );
        }else{
          showErrorToast(error: 'Tải file thất bại!');
          dismissLoadingDialog();
        }
      } else {
        showErrorToast(error: 'Tải file thất bại!');
        dismissLoadingDialog();
      }
    } catch (e) {
      showErrorToast(error: 'Tải lên file thất bại!');
      dismissLoadingDialog();
    }
  }

  Future<File> exportFile(String fileName, String base64Str) async {
    try {
      await DownloadUtil.permissionServicesImage();
      String dir = await DownloadUtil.getPath();
      if ((dir ?? '').isNotEmpty) {
        final localPath = path.join(dir, fileName);
        final bytes = base64.decode(base64Str);
        final file = File(localPath);
        await file.writeAsBytes(
          bytes,
        );
        return file;
      }
    } catch (e) {
      showErrorToast(error: 'Tải file thất bại!');
      dismissLoadingDialog();
    }
  }
}
