import 'package:bottom_sheet_bar/bottom_sheet_bar.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
// import 'package:google_maps_controller/google_maps_controller.dart';
import 'package:location/location.dart';
import 'package:trackcarvcc/constants/app_state.dart';
import 'package:trackcarvcc/constants/constants.dart';
import 'package:trackcarvcc/helpers/extensions.dart';
import 'package:trackcarvcc/helpers/string_utils.dart';
import 'package:trackcarvcc/models/base/base_api_response.dart';
import 'package:trackcarvcc/models/models.dart';
import 'package:trackcarvcc/models/request/get_vehicle_monitoring_request.dart';
import 'package:trackcarvcc/models/request/list_car_by_unit_request.dart';
import 'package:trackcarvcc/models/response/list_car_by_unit_response.dart';
import 'package:trackcarvcc/models/response/list_car_history_response.dart';
import 'package:trackcarvcc/models/response/list_unit_response.dart';
import 'package:trackcarvcc/models/response/vehilce_monitoring_response.dart';
import 'package:trackcarvcc/repository/api/api.dart';
import 'package:trackcarvcc/repository/preferences/pref.dart';
import 'package:vtmap_gl/vtmap_gl.dart';

enum MapState { IDLE, LOADING, DONE, ERROR, UN_DEFINED }

class MonitoringMapController extends GetxController {
  Api api;

  MonitoringMapController(this.api);

  final appState = Rx<AppState>();
  final mapState = Rx<MapState>();

  BottomSheetBarController bottomSheetController = BottomSheetBarController();

  TextEditingController unitController = TextEditingController();
  TextEditingController searchCarController = TextEditingController();
  TextEditingController timeStartController = TextEditingController(
      text: StringUtils.parseStartDateTimeDDMMYYYY(
          DateTime(DateTime.now().year, DateTime.now().month, 1)));
  TextEditingController timeFinishController = TextEditingController(
      text: StringUtils.parseFinishDateTimeDDMMYYYY(
          DateTime(DateTime.now().year, DateTime.now().month + 1, 0)));

  RxList<LstBookCarDtoUnit> listUnit = RxList<LstBookCarDtoUnit>([]);
  List<LstBookCarDtoUnit> backUpListUnit = List<LstBookCarDtoUnit>();

  RxList<CatVehicleDTO> listCarByUnit = RxList<CatVehicleDTO>([]);
  List<CatVehicleDTO> backUpListCarByUnit = List<CatVehicleDTO>();

  RxList<LstCarLocationCurrents> listCurrentCarLocation =
      RxList<LstCarLocationCurrents>([]);

  RxList<LstBookCarDtoHistory> listCarHistory =
      RxList<LstBookCarDtoHistory>([]);

  int idChosenUnit = -1;
  LstBookCarDtoUnit chosenUnit;
  CatVehicleDTO chosenCar;
  RxBool isRunning = false.obs;
  RxBool isStopping = false.obs;
  RxBool isParking = false.obs;
  RxBool isLostGps = false.obs;

  bool isValidated = false;

  // GoogleMapsController googleMapController;
  MapboxMapController mapboxMapController;

  Location location = Location();
  LocationData _locationData;

  fetchListUnit() async {
    try {
      appState.value = AppState.LOADING;
      final response = await api.getListUnit();

      final result = BaseApiResponse.fromJson(response);
      if (result.success) {
        final data = ListUnitResponse.fromJson(result.data);
        if (data.resultInfo.status == RESULT_OK) {
          listUnit = RxList<LstBookCarDtoUnit>.from(data.lstBookCarDto);
          backUpListUnit = data.lstBookCarDto;
          appState.value = AppState.DONE;
        } else {
          appState.value = AppState.ERROR;
        }
      } else {
        appState.value = AppState.ERROR;
      }
    } catch (e) {
      appState.value = AppState.ERROR;
    }
  }

  fetchListCarByUnit(int sysGroupId) async {
    try {
      appState.value = AppState.LOADING;
      ListCarByUnitRequest request =
          ListCarByUnitRequest(sysGroupId: sysGroupId);
      final response = await api.getListCarByUnit(request);

      final result = BaseApiResponse.fromJson(response);
      if (result.success) {
        final data = ListCarByUnitResponse.fromJson(result.data);
        if (data.resultInfo.status == RESULT_OK) {
          listCarByUnit = RxList<CatVehicleDTO>.from(data.catVehicleDTO);
          backUpListCarByUnit = data.catVehicleDTO;
          appState.value = AppState.DONE;
        } else {
          appState.value = AppState.ERROR;
        }
      } else {
        appState.value = AppState.ERROR;
      }
    } catch (e) {
      appState.value = AppState.ERROR;
    }
  }

  void searchUnit(String value) {
    listUnit.clear();
    listUnit.addAll(backUpListUnit
        .where((element) =>
            element.sysGroupName.toLowerCase().contains(value.toLowerCase()))
        .toList());
  }

  void searchCarByUnit(String value) {
    listCarByUnit.clear();
    listCarByUnit.addAll(backUpListCarByUnit
        .where((element) =>
            element.licenseCar.toLowerCase().contains(value.toLowerCase()))
        .toList());
  }

  // Future initMap() async {
  //   googleMapController = GoogleMapsController(
  //     mapType: MapType.normal,
  //     onMapCreated: _onMapCreated,
  //     myLocationEnabled: true,
  //     compassEnabled: true,
  //     myLocationButtonEnabled: false,
  //     zoomControlsEnabled: false,
  //   );
  // }

  // void onMapCreated(GoogleMapController controller) {
  //   _getLocation();
  // }
  void onMapCreated(MapboxMapController controller) {
    mapboxMapController = controller;
    _getLocation();
  }

  Future _getLocation() async {
    try {
      _locationData = await location.getLocation();
      await mapboxMapController.moveCamera(
        CameraUpdate.newCameraPosition(
          CameraPosition(
            target: LatLng(
              _locationData.latitude,
              _locationData.longitude,
            ),
            zoom: 17,
          ),
        ),
      );
      addMarker(_locationData.latitude, _locationData.longitude);
    } on PlatformException catch (err) {
      print('$err');
    }
  }

  Future checkPermissions() async {
    final PermissionStatus permissionGrantedResult =
        await location.hasPermission();
    if (permissionGrantedResult == PermissionStatus.granted ||
        permissionGrantedResult == PermissionStatus.grantedLimited) {
      await _getLocation();
    } else {
      await _requestPermission();
    }
  }

  Future _requestPermission() async {
    await location.requestPermission();
  }

  Future addPolyline() async {
    mapboxMapController.clearSymbols();
    List<LatLng> listLocation = listCurrentCarLocation
        .where((it) => it.latitude != null)
        .map((item) => LatLng(item.latitude, item.longtitude))
        .toList();
    List<String> listLicenseCar = listCurrentCarLocation
        .where((it) => it.latitude != null)
        .map((item) => item.licenseCar)
        .toList();
    List<String> listStatusCar = listCurrentCarLocation
        .where((it) => it.latitude != null)
        .map((item) => item.carState)
        .toList();

    await moveCamera(listLocation.last.latitude, listLocation.last.longitude);
    // final iconMarker = await BitmapDescriptor.fromAssetImage(
    //     ImageConfiguration(), car_connection);
    // List<Marker> markers = List<Marker>();

    var wayPoints = List<WayPoint>();

    for (var i = 0; i < listLocation.length; i++) {
      // Marker markerDiem = Marker(
      //   position: listLocation[i],
      //   icon: iconMarker,
      //   markerId: MarkerId(i.toString()),
      //   infoWindow:
      //   InfoWindow(title: listLicenseCar[i], snippet: listStatusCar[i]),
      // );
      // markers.add(markerDiem);
      wayPoints.add(
        WayPoint(
          latitude: listLocation[i].latitude,
          longitude: listLocation[i].longitude,
        ),
      );
    }

    mapboxMapController.buildRoute(wayPoints: wayPoints);
  }

  Future moveCamera(double lat, double lng) async {
    await mapboxMapController.moveCamera(
      CameraUpdate.newCameraPosition(
        CameraPosition(
          target: LatLng(lat, lng),
          zoom: 17,
        ),
      ),
    );
    // addMarker(lat, lng);
  }

  void addMarkerDiemDiDiemDen(LatLng diemDi, LatLng diemDen) async {
    // final String markerIdVal = '1';
    // final MarkerId markerId = MarkerId(markerIdVal);
    // final String markerIdVal2 = '2';
    // final MarkerId markerId2 = MarkerId(markerIdVal2);
    //
    // final iconMarkerDiemDi = await BitmapDescriptor.fromAssetImage(
    //     ImageConfiguration(), ic_location_maker);
    // final Marker markerDiemDi = Marker(
    //   position: diemDi,
    //   icon: iconMarkerDiemDi,
    //   markerId: markerId,
    // );
    //
    // final iconMarkerDiemDen = await BitmapDescriptor.fromAssetImage(
    //     ImageConfiguration(), car_connection);
    // final Marker markerDiemDen =
    //     Marker(position: diemDi, icon: iconMarkerDiemDen, markerId: markerId2);
    //
    // googleMapController.clearMarkers();
    // googleMapController.addMarker(markerDiemDi);
    // googleMapController.addMarker(markerDiemDen);

    final SymbolOptions markerDiemDi = _getSymbolOptions(diemDi.latitude, diemDi.longitude, iconImage: ic_location_maker);
    final SymbolOptions markerDiemDen = _getSymbolOptions(diemDen.latitude, diemDen.longitude, iconImage: car_connection);

    mapboxMapController.clearSymbols();
    mapboxMapController.addSymbol(markerDiemDi);
    mapboxMapController.addSymbol(markerDiemDen);
  }

  void addMarker(double lat, double lng) {
    // final String markerIdVal = '1';
    // final MarkerId markerId = MarkerId(markerIdVal);
    //
    // final Marker marker = Marker(
    //   markerId: markerId,
    //   position: LatLng(lat, lng),
    // );
    // googleMapController.clearMarkers();
    // googleMapController.addMarker(marker);

    final SymbolOptions marker = _getSymbolOptions(lat, lng);
    mapboxMapController.clearSymbols();
    mapboxMapController.addSymbol(marker);
  }

  SymbolOptions _getSymbolOptions(double lat, double lng, {String iconImage = ic_location_maker}) {
    LatLng center = LatLng(lat, lng);
    LatLng geometry = LatLng(center.latitude, center.longitude);
    return SymbolOptions(
      geometry: geometry,
      iconImage: iconImage,
    );
  }

  fetchListCarMonitoring(bool hasRoleCodeBANXETCT) async {
    try {
      List<StatusCarObj> listStatusCar = [];
      listStatusCar.add(StatusCarObj(isRunning.value, 0));
      listStatusCar.add(StatusCarObj(isStopping.value, 1));
      listStatusCar.add(StatusCarObj(isParking.value, 2));
      listStatusCar.add(StatusCarObj(isLostGps.value, 3));
      List<String> listStatus = listStatusCar
          .where((item) => item.status == true)
          .map((item) => item.value.toString())
          .toList();
      if (chosenUnit != null) {
        isValidated = true;
      } else {
        isValidated = false;
      }
      if (isValidated || !hasRoleCodeBANXETCT) {
        showLoadingDialog();
        final user = DataCenter.shared().getUserInfo();
        var licenseCar = "";
        if (chosenCar != null) licenseCar = chosenCar.licenseCar;
        GetVehicleMonitoringRequest request = GetVehicleMonitoringRequest(
            sysUserRequest: SysUserRequest(
                authenticationInfo:
                    AuthenticationInfo(username: user.loginName),
                sysUserId: user.sysUserId),
            bookCarDto: BookCarDtoObj(
                licenseCar: licenseCar,
                sysGroupId: hasRoleCodeBANXETCT ? idChosenUnit : user.sysGroupId,
                listStatus: listStatus));
        final response = await api.getVehicleMonitoring(request);

        final result = BaseApiResponse.fromJson(response);
        if (result.success) {
          final data = CarMonitoringResponse.fromJson(result.data);
          if (data.resultInfo.status == RESULT_OK) {
            listCurrentCarLocation = RxList<LstCarLocationCurrents>.from(
                data.lstCarLocationCurrents);
            dismissLoadingDialog();
            await bottomSheetController.collapse();
            if (listCurrentCarLocation.isNotEmpty) {
              await addPolyline();
            } else {
              showErrorToast(error: 'Không tìm thấy xe nào trên hệ thống.');
            }
          } else {
            dismissLoadingDialog();
            showErrorToast(error: 'Có lỗi xảy ra trong quá trình tìm kiếm');
          }
        } else {
          dismissLoadingDialog();
          showErrorToast(error: 'Có lỗi xảy ra trong quá trình tìm kiếm');
        }
      } else {
        showErrorToast(error: 'Bạn chưa chọn đơn vị');
      }
    } catch (e) {
      print(e.toString());
      dismissLoadingDialog();
      showErrorToast(error: 'Có lỗi xảy ra trong quá trình tìm kiếm');
    }
  }
}

class StatusCarObj {
  StatusCarObj(this.status, this.value);

  bool status;
  int value;
}
