import 'package:get/get.dart';
import 'package:trackcarvcc/constants/constants.dart';
import 'package:trackcarvcc/models/base/base_api_response.dart';
import 'package:trackcarvcc/models/response/get_app_version_response.dart';
import 'package:trackcarvcc/repository/api/api.dart';
import 'package:trackcarvcc/repository/preferences/data_center.dart';
import 'package:trackcarvcc/helpers/extensions.dart';
import 'package:trackcarvcc/routes/routes.dart';
import 'package:trackcarvcc/ui/splash/widget/notify_update_version_dialog.dart';

class SplashController extends GetxController {
  Api api;

  SplashController(this.api);

  bool hasLogin() {
    final token = DataCenter.shared().getAccessToken();
    return !(token == null || token.isEmpty);
  }

  // getAppVersion() async {
  //   try {
  //     final response = await api.getAppVersion();
  //     final result = BaseApiResponse.fromJson(response);
  //     if (result.success) {
  //       final data = GetAppVersionResponse.fromJson(result.data);
  //       if (data.resultInfo.status == RESULT_OK) {
  //         // get version
  //         if (double.parse(data.lstBookCarDto[0].version) >
  //             Constants.APP_VERSION) {
  //           Get.dialog(
  //             NotifyUpdateVersionAppDialog(
  //               versionApp: data.lstBookCarDto[0].version,
  //               onSuccess: () {
  //                 navigate();
  //               },
  //             ),
  //             barrierDismissible: false,
  //           );
  //         } else {
  //           navigate();
  //         }
  //         // save version
  //       } else {
  //         showErrorToast(error: data.resultInfo.status);
  //       }
  //     } else {
  //       showErrorToast();
  //     }
  //   } catch (e) {
  //     showErrorToast();
  //     dismissLoadingDialog();
  //   }
  // }

  navigate() {
    Future.delayed(const Duration(milliseconds: 500), () {
      if (hasLogin()) {
        // To Nav page
        Get.offAndToNamed(Routers.nav);
      } else {
        // To Login page
        Get.offAndToNamed(Routers.login);
      }
    });
  }
}
