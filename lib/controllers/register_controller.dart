import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:trackcarvcc/constants/app_state.dart';
import 'package:trackcarvcc/constants/constants.dart';
import 'package:trackcarvcc/models/models.dart';
import 'package:trackcarvcc/models/request/update_firebase_token_request.dart';
import 'package:trackcarvcc/repository/api/api.dart';
import 'package:trackcarvcc/repository/preferences/data_center.dart';
import 'package:trackcarvcc/routes/routes.dart';
import 'package:trackcarvcc/helpers/extensions.dart';

class RegisterController extends GetxController {
  Api api;
  final appState = Rx<AppState>();
  TextEditingController userController = TextEditingController();
  TextEditingController passController = TextEditingController();
  TextEditingController pass2Controller = TextEditingController();
  final username = ''.obs;
  final password = ''.obs;
  final password2 = ''.obs;
  final showPassword = false.obs;
  final validate = false.obs;

  void changeUsername(String value) => username.value = value;

  void changePassword(String value) => password.value = value;

  void changePassword2(String value) => password2.value = value;

  RegisterController(this.api);

  login() async {
    try {
      showLoadingDialog();
      final request = LoginRequest(
          authenticationInfo: AuthenticationInfo(
              username: username.value, password: password.value));
      final response = await api.login(request);
      dismissLoadingDialog();
      final result = BaseApiResponse.fromJson(response);
      if (result.success) {
        final data = AuthResponse.fromJson(result.data);
        if (data.resultInfo.status == RESULT_OK) {
          // Save info
          await DataCenter.shared()
              .saveSession(accessToken: data.userLogin.token);
          await DataCenter.shared().saveUserInfo(data.userLogin);
          await DataCenter.shared().savePassword(password.value);

          // update token
          await updateFirebaseToken();
          // To Nav page
          Get.offAndToNamed(Routers.nav);
        } else {
          showErrorToast(error: data.resultInfo.status);
        }
      } else {
        showErrorToast();
      }
    } catch (e) {
      showErrorToast();
      dismissLoadingDialog();
    }
  }

  updateFirebaseToken() async {
    try {
      await FirebaseMessaging.instance.getToken().then((value) async {
        print("bacnd4 FirebaseMessaging.instance.getToken: $value");
        await DataCenter.shared().saveFirebaseToken(value);
      });
      final userInfo = DataCenter.shared().getUserInfo();
      final request = UpdateFirebaseTokenRequest(
          authenticationInfo: AuthenticationInfo(
            username: userInfo.loginName,
            password: DataCenter.shared().getPassword(),
          ),
          sysUserId: userInfo.sysUserId,
          token: DataCenter.shared().getFirebaseToken());
      await api.updateFirebaseToken(request);
    } catch (e) {
      print('updateFirebaseToken err: $e');
    }
  }

  changeShowPassword() {
    showPassword.value = !showPassword.value;
  }

  bool get usernameIsValid {
    if (username.value.isEmpty && validate.value) return false;
    return true;
  }

  bool get passwordIsValid {
    if (password.value.isEmpty && validate.value) return false;
    return true;
  }

  bool get password2IsValid {
    if (password2.value.isEmpty && validate.value) return false;
    return true;
  }

  bool get matchPasswordIsValid {
    if ((password.value != password2.value) && validate.value) {
      showErrorToast(error: "Mật khẩu không khớp");
      return false;
    }
    return true;
  }

  bool get formLoginIsValid =>
      usernameIsValid &&
      passwordIsValid &&
      password2IsValid &&
      matchPasswordIsValid &&
      username.value.isNotEmpty &&
      password.value.isNotEmpty;
}
