import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:trackcarvcc/constants/constants.dart';
import 'package:trackcarvcc/helpers/extensions.dart';
import 'package:trackcarvcc/helpers/string_utils.dart';
import 'package:trackcarvcc/models/models.dart';
import 'package:trackcarvcc/models/request/create_order_car_request.dart';
import 'package:trackcarvcc/models/response/create_car_response.dart';
import 'package:trackcarvcc/repository/api/api.dart';
import 'package:trackcarvcc/repository/preferences/data_center.dart';

class OpenCommandController extends GetxController {
  OpenCommandController(this.api);

  Api api;

  LstBookCarDto bookCar;
  List<UserLogin> listTogether;

  TextEditingController destinationPointController = TextEditingController();
  TextEditingController timeFinishController = TextEditingController();
  TextEditingController jobDescriptionController = TextEditingController();

  RxString destinationPoint = ''.obs;
  String destinationProvince = '';
  RxBool validate = false.obs;
  RxString timeFinish = ''.obs;
  RxString jobDescription = ''.obs;

  bool get destinationPointIsValid =>
      !(destinationPoint.value.isEmpty && validate.value);

  bool get timeFinishIsValid => !(timeFinish.value.isEmpty && validate.value);

  bool get timeFromToIsValid {
    if (bookCar.endTime.isNotEmpty && timeFinish.value.isNotEmpty) {
      DateTime toDate = StringUtils.isEmpty(bookCar.endTime)
          ? null
          : DateFormat('dd/MM/yyyy HH:mm').parse(bookCar.endTime);

      DateTime fromDate = StringUtils.isEmpty(timeFinish.value)
          ? null
          : DateFormat('dd/MM/yyyy HH:mm').parse(timeFinish.value);
      if (toDate != null && fromDate != null && fromDate.compareTo(toDate) >= 0)
        return true;
    }
    return false;
  }

  bool get jobDescriptionIsValid =>
      !(jobDescription.value.isEmpty && validate.value);

  bool get formIsValid =>
      destinationPointIsValid &&
      timeFinishIsValid &&
      timeFromToIsValid &&
      jobDescriptionIsValid;

  void changeDestinationPoint(String value) => destinationPoint.value = value;

  void changeTimeFinish(String value) => timeFinish.value = value;

  void changeJobDescription(String value) => jobDescription.value = value;

  openCommand() async {
    try {
      int internalProvince;
      if (bookCar.internalProvince == 2) {
        internalProvince = 2;
      } else {
        int intTo = bookCar.toAddress.lastIndexOf(" ");
        int intFrom = destinationPoint.value.lastIndexOf(" ");
        String strTo = bookCar.toAddress.substring(intTo);
        String strFrom = destinationPoint.value.substring(intFrom);
        internalProvince = strTo == strFrom ? 1 : 2;
      }

      LstBookCarDto bookCarDto = LstBookCarDto(
        bookCarId: bookCar.bookCarId,
        internalProvince: internalProvince,
        toAddressExtend: destinationPoint.value,
        contentExtend: jobDescription.value,
        endTimeExtend: timeFinish.value,
      );
      CreateOrderCarRequest request = CreateOrderCarRequest(
        bookCarDto: bookCarDto,
        lstPersonTogether: listTogether,
        sysUserRequest: SysUserRequest(
          authenticationInfo: AuthenticationInfo(
            username: DataCenter.shared().getUserInfo().loginName,
            password: DataCenter.shared().getPassword(),
          ),
        ),
      );
      final response = await api.openCommand(request);

      final result = BaseApiResponse.fromJson(response);
      dismissLoadingDialog();
      if (result.success) {
        final data = CreateCarOrderResponse.fromJson(result.data);
        showErrorToast(error: data.resultInfo.message);
        if (data.resultInfo.status == RESULT_OK) {
          Get.back(result: true);
        }
      } else {
        showErrorToast();
      }
    } catch (e) {
      showErrorToast();
      dismissLoadingDialog();
    }
  }
}
