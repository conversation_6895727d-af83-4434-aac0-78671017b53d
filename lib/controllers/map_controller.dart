import 'dart:ffi';
import 'dart:typed_data';

import 'package:bottom_sheet_bar/bottom_sheet_bar.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

// import 'package:google_maps_controller/google_maps_controller.dart';
import 'package:location/location.dart';
import 'package:trackcarvcc/constants/app_state.dart';
import 'package:trackcarvcc/constants/constants.dart';
import 'package:trackcarvcc/constants/style/style.dart';
import 'package:trackcarvcc/helpers/string_utils.dart';
import 'package:trackcarvcc/models/base/base_api_response.dart';
import 'package:trackcarvcc/models/request/get_history_car_detail_request.dart';
import 'package:trackcarvcc/models/request/get_vehicle_monitoring_request.dart';
import 'package:trackcarvcc/models/request/list_car_by_unit_request.dart';
import 'package:trackcarvcc/models/request/list_car_history_request.dart';
import 'package:trackcarvcc/models/response/get_app_version_response.dart';
import 'package:trackcarvcc/models/response/get_history_detail_car_response.dart';
import 'package:trackcarvcc/models/response/list_car_by_unit_response.dart';
import 'package:trackcarvcc/models/response/list_car_history_response.dart';
import 'package:trackcarvcc/models/response/list_unit_response.dart';
import 'package:trackcarvcc/models/response/vehilce_monitoring_response.dart';
import 'package:trackcarvcc/repository/api/api.dart';
import 'package:trackcarvcc/helpers/extensions.dart';
import 'package:trackcarvcc/repository/preferences/pref.dart';
import 'package:trackcarvcc/ui/home/<USER>/history_of_car_dialog.dart';
import 'package:trackcarvcc/models/models.dart';
import 'package:trackcarvcc/ui/home/<USER>/info_window_marker_dialog.dart';
import 'package:trackcarvcc/ui/splash/widget/notify_update_version_dialog.dart';
import 'package:vtmap_gl/vtmap_gl.dart';

enum MapState { IDLE, LOADING, DONE, ERROR, UN_DEFINED }

class MapController extends GetxController {
  Api api;

  MapController(this.api);

  final appState = Rx<AppState>();
  final mapState = Rx<MapState>();

  BottomSheetBarController bottomSheetController = BottomSheetBarController();

  TextEditingController unitController = TextEditingController();
  TextEditingController searchCarController = TextEditingController();
  TextEditingController timeStartController = TextEditingController(text: StringUtils.parseStartDateTimeDDMMYYYY(DateTime(DateTime.now().year, DateTime.now().month, 1)));
  TextEditingController timeFinishController = TextEditingController(text: StringUtils.parseFinishDateTimeDDMMYYYY(DateTime(DateTime.now().year, DateTime.now().month + 1, 0)));

  RxList<LstBookCarDtoUnit> listUnit = RxList<LstBookCarDtoUnit>([]);
  List<LstBookCarDtoUnit> backUpListUnit = List<LstBookCarDtoUnit>();

  RxList<CatVehicleDTO> listCarByUnit = RxList<CatVehicleDTO>([]);
  List<CatVehicleDTO> backUpListCarByUnit = List<CatVehicleDTO>();

  RxList<LstCarLocationCurrents> listCurrentCarLocation = RxList<LstCarLocationCurrents>([]);

  RxList<LstBookCarDtoHistory> listCarHistory = RxList<LstBookCarDtoHistory>([]);

  List<LstBookCarDtoDetail> lstBookCarDtoDetail;

  int idChosenUnit = -1;
  LstBookCarDtoUnit chosenUnit;
  CatVehicleDTO chosenCar;
  RxBool isRunning = false.obs;
  RxBool isStopping = false.obs;
  RxBool isParking = false.obs;
  RxBool isLostGps = false.obs;

  bool isValidated = false;

  MapboxMapController mapboxMapController;
  Location location = Location();
  LocationData _locationData;

  final successVersion = false.obs;
  var version = '';

  bool isHasRoleCodeBANXETCT() {
    List<String> listRoleCode = DataCenter.shared().getUserInfo().roleCode == null ? ["NHANVIEN"] : DataCenter.shared().getUserInfo().roleCode.split(";");
    for (String roleCode in listRoleCode) {
      if (roleCode == "BANXETCT") return true;
    }
    return false;
  }

  fetchListUnit() async {
    try {
      appState.value = AppState.LOADING;
      final response = await api.getListUnit();

      final result = BaseApiResponse.fromJson(response);
      if (result.success) {
        final data = ListUnitResponse.fromJson(result.data);
        if (data.resultInfo.status == RESULT_OK) {
          backUpListUnit = data.lstBookCarDto;
          appState.value = AppState.DONE;
        } else {
          appState.value = AppState.ERROR;
        }
      } else {
        appState.value = AppState.ERROR;
      }
    } catch (e) {
      appState.value = AppState.ERROR;
    }
  }

  fetchListCarByUnit(int sysGroupId) async {
    try {
      appState.value = AppState.LOADING;
      ListCarByUnitRequest request = ListCarByUnitRequest(sysGroupId: sysGroupId);
      final response = await api.getListCarByUnit(request);

      final result = BaseApiResponse.fromJson(response);
      if (result.success) {
        final data = ListCarByUnitResponse.fromJson(result.data);
        if (data.resultInfo.status == RESULT_OK) {
          listCarByUnit = RxList<CatVehicleDTO>.from(data.catVehicleDTO);
          backUpListCarByUnit = data.catVehicleDTO;
          appState.value = AppState.DONE;
        } else {
          appState.value = AppState.ERROR;
        }
      } else {
        appState.value = AppState.ERROR;
      }
    } catch (e) {
      appState.value = AppState.ERROR;
    }
  }

  fetchListCarHistory(bool search) async {
    try {
      // if (chosenUnit != null && chosenCar != null) {
      isValidated = true;
      // } else {
      //   isValidated = false;
      // }
      if (isValidated) {
        showLoadingDialog();
        ListCarHistoryRequest request = ListCarHistoryRequest(bookCarDto: BookCarDto(sysGroupId: search ? DataCenter.shared().getUserInfo().sysGroupId : chosenUnit.sysGroupId, licenseCar: chosenCar.licenseCar, carId: chosenCar.carId, fromTimeSearch: timeStartController.text, toTimeSearch: timeFinishController.text));

        /*ListCarHistoryRequest request =
      ListCarHistoryRequest(bookCarDto: BookCarDto(
        sysGroupId: 270266,
        licenseCar: '29KT-108.33',
        carId: 831,
        fromTimeSearch: '01/03/2021 13:45',
        toTimeSearch: '30/04/2021 23:59'
      ));*/
        final response = await api.getListCarHistory(request);

        final result = BaseApiResponse.fromJson(response);
        if (result.success) {
          final data = ListCarHistoryResponse.fromJson(result.data);
          if (data.resultInfo.status == RESULT_OK) {
            listCarHistory = RxList<LstBookCarDtoHistory>.from(data.lstBookCarDto);
            dismissLoadingDialog();
            await bottomSheetController.collapse();
            if (listCarHistory.isNotEmpty) {
              Get.defaultDialog(
                title: 'Danh sách phiếu đặt của ${chosenCar.licenseCar}',
                content: HistoryOfCarDialog(listBookCar: data.lstBookCarDto),
                barrierDismissible: true,
                backgroundColor: AppThemes.colorViettelGray3,
                // barrierColor: Colors.grey.withOpacity(0.3),
              );
            } else {
              showErrorToast(error: 'Không có lịch sử đặt xe đã chọn.');
            }
          } else {
            dismissLoadingDialog();
            showErrorToast(error: 'Có lỗi xảy ra trong quá trình tìm kiếm');
          }
        } else {
          dismissLoadingDialog();
          showErrorToast(error: 'Có lỗi xảy ra trong quá trình tìm kiếm');
        }
      } else {
        showErrorToast(error: 'Bạn chưa điền đủ thông tin');
      }
    } catch (e) {
      dismissLoadingDialog();
      showErrorToast(error: 'Có lỗi xảy ra trong quá trình tìm kiếm');
    }
  }

  fetchHistoryDetailCar(String licenseCar) async {
    try {
      showLoadingDialog();
      final user = DataCenter.shared().getUserInfo();
      GetHistoryCarDetailRequest request = GetHistoryCarDetailRequest(bookCarDto: BookCarDtoHistoryCar(code: licenseCar), sysUserRequest: SysUserRequest(authenticationInfo: AuthenticationInfo(username: user.loginName), sysUserId: user.sysUserId));
      final response = await api.getHistoryDetailCar(request);

      final result = BaseApiResponse.fromJson(response);
      if (result.success) {
        final data = GetHistoryCarDetailResponse.fromJson(result.data);
        if (data.resultInfo.status == RESULT_OK) {
          dismissLoadingDialog();
          lstBookCarDtoDetail = data.lstBookCarDto;
          if (lstBookCarDtoDetail.isNotEmpty) {
            showErrorToast(error: 'Lấy chi tiết lịch sử xe thành công.');
            await addPolyline();
          } else {
            showErrorToast(error: 'Không có lịch sử đặt xe đã chọn.');
          }
        } else {
          dismissLoadingDialog();
          showErrorToast(error: 'Có lỗi xảy ra khi lấy thông tin xe.');
        }
      } else {
        dismissLoadingDialog();
        showErrorToast(error: 'Có lỗi xảy ra khi lấy thông tin xe.');
      }
    } catch (e) {
      dismissLoadingDialog();
      showErrorToast(error: 'Có lỗi xảy ra khi lấy thông tin xe.');
    }
  }

  void searchUnit(String value) {
    listUnit.clear();
    listUnit.addAll(backUpListUnit.where((element) => element.sysGroupName.toLowerCase().contains(value.toLowerCase())).toList());
  }

  void searchCarByUnit(String value) {
    listCarByUnit.clear();
    listCarByUnit.addAll(backUpListCarByUnit.where((element) => element.licenseCar.toLowerCase().contains(value.toLowerCase())).toList());
  }

  // Future initMap() async {
  //   googleMapController = GoogleMapsController(
  //     mapType: MapType.normal,
  //     onMapCreated: _onMapCreated,
  //     myLocationEnabled: true,
  //     compassEnabled: true,
  //     myLocationButtonEnabled: false,
  //     zoomControlsEnabled: false,
  //   );
  // }

  void onMapCreated(MapboxMapController controller) {
    try {
      print('MapController: onMapCreated called');
      mapboxMapController = controller;
      _getLocation();
    } catch (e) {
      print('MapController: onMapCreated error: $e');
    }
  }

  Future _getLocation() async {
    try {
      print('MapController: _getLocation called');
      if (mapboxMapController == null) {
        print('MapController: mapboxMapController is null, skipping location');
        return;
      }

      _locationData = await location.getLocation();
      print('MapController: Got location: ${_locationData.latitude}, ${_locationData.longitude}');

      await mapboxMapController.moveCamera(
        CameraUpdate.newCameraPosition(
          CameraPosition(
            target: LatLng(
              _locationData.latitude,
              _locationData.longitude,
            ),
            zoom: 17,
          ),
        ),
      );
      addMarker(_locationData.latitude, _locationData.longitude);
    } on PlatformException catch (err) {
      print('MapController: PlatformException in _getLocation: $err');
    } catch (e) {
      print('MapController: General error in _getLocation: $e');
    }
  }

  Future checkPermissions() async {
    final PermissionStatus permissionGrantedResult = await location.hasPermission();
    if (permissionGrantedResult == PermissionStatus.granted || permissionGrantedResult == PermissionStatus.grantedLimited) {
      await _getLocation();
    } else {
      await _requestPermission();
    }
  }

  Future _requestPermission() async {
    await location.requestPermission();
  }

  Future addPolyline() async {
    List<LatLng> listLocation = lstBookCarDtoDetail.map((item) => LatLng(double.parse(item.latitude), double.parse(item.longtitude))).toList();
    for (var item in listLocation) {
      print('-------: item ---: ${item.latitude} - ${item.longitude}');
    }

    // List<Polyline> polylines = List<Polyline>();
    // polylines.add(Polyline(
    //   polylineId: PolylineId("line 1"),
    //   visible: true,
    //   points: listLocation,
    //   width: 2,
    //   color: Colors.deepOrangeAccent,
    // ));
    // googleMapController.addPolylines(polylines);
    var wayPoints = List<WayPoint>();
    wayPoints.add(
      WayPoint(
        latitude: listLocation.first.latitude,
        longitude: listLocation.first.longitude,
      ),
    );
    wayPoints.addAll(wayPoints);

    mapboxMapController.buildRoute(wayPoints: wayPoints);

    await moveCamera(listLocation.last.latitude, listLocation.last.longitude);
    addMarkerDiemDiDiemDen(LatLng(listLocation.first.latitude, listLocation.first.longitude), LatLng(listLocation.last.latitude, listLocation.last.longitude));
  }

  Future moveCamera(double lat, double lng) async {
    await mapboxMapController.moveCamera(
      CameraUpdate.newCameraPosition(
        CameraPosition(
          target: LatLng(lat, lng),
          zoom: 17,
        ),
      ),
    );
    // addMarker(lat, lng);
  }

  void addMarkerDiemDiDiemDen(LatLng diemDi, LatLng diemDen) async {
    // final String markerIdVal = '1';
    // final MarkerId markerId = MarkerId(markerIdVal);
    // final String markerIdVal2 = '2';
    // final MarkerId markerId2 = MarkerId(markerIdVal2);
    //
    // final iconMarkerDiemDi = await BitmapDescriptor.fromAssetImage(ImageConfiguration(), ic_location_maker);
    // final Marker markerDiemDi = Marker(
    //   position: diemDi,
    //   icon: iconMarkerDiemDi,
    //   markerId: markerId,
    // );
    //
    // final iconMarkerDiemDen = await BitmapDescriptor.fromAssetImage(ImageConfiguration(), car_connection);
    // final Marker markerDiemDen = Marker(
    //   position: diemDi,
    //   icon: iconMarkerDiemDen,
    //   markerId: markerId2,
    //   infoWindow: InfoWindow(title: "Click để xem thông tin"),
    //   onTap: () {
    //     Get.defaultDialog(
    //       title: 'Phiếu ${lstBookCarDtoDetail.last.code}',
    //       content: InfoWindowMarkerDialog(lstBookCarDtoDetail.first, lstBookCarDtoDetail.last),
    //       barrierDismissible: true,
    //       backgroundColor: AppThemes.colorViettelGray3,
    //       // barrierColor: Colors.grey.withOpacity(0.3),
    //     );
    //   },
    // );

    final SymbolOptions markerDiemDi = _getSymbolOptions(diemDi.latitude, diemDi.longitude, iconImage: ic_location_maker);
    final SymbolOptions markerDiemDen = _getSymbolOptions(diemDen.latitude, diemDen.longitude, iconImage: car_connection);

    mapboxMapController.clearSymbols();
    mapboxMapController.addSymbol(markerDiemDi);
    mapboxMapController.addSymbol(markerDiemDen);
  }

  // void addMarker(double lat, double lng) {
  //   final String markerIdVal = '1';
  //   final MarkerId markerId = MarkerId(markerIdVal);
  //
  //   final Marker marker = Marker(
  //     markerId: markerId,
  //     position: LatLng(lat, lng),
  //   );
  //   googleMapController.clearMarkers();
  //   googleMapController.addMarker(marker);
  // }

  void addMarker(double lat, double lng) {
    // List<int> availableNumbers = Iterable<int>.generate(12).toList();
    // googleMapController.symbols.forEach(
    //         (s) => availableNumbers.removeWhere((i) => i == s.data['count'])
    // );

    // if (availableNumbers.isNotEmpty) {
    //   googleMapController.addSymbol(
    //       _getSymbolOptions(ic_location_maker, availableNumbers.first),
    //       {'count': availableNumbers.first}
    //   );
    // }

    final SymbolOptions marker = _getSymbolOptions(lat, lng);
    mapboxMapController.clearSymbols();
    mapboxMapController.addSymbol(marker);
  }

  SymbolOptions _getSymbolOptions(double lat, double lng, {String iconImage = ic_location_maker}) {
    LatLng center = LatLng(lat, lng);
    LatLng geometry = LatLng(center.latitude, center.longitude);
    return SymbolOptions(
      geometry: geometry,
      iconImage: iconImage,
    );
  }

  Future<void> addImageFromAsset(String name, String assetName) async {
    final ByteData bytes = await rootBundle.load(assetName);
    final Uint8List list = bytes.buffer.asUint8List();
    return mapboxMapController.addImage(name, list);
  }

  fetchListCarMonitoring() async {
    try {
      List<StatusCarObj> listStatusCar = [];
      listStatusCar.add(StatusCarObj(isRunning.value, 0));
      listStatusCar.add(StatusCarObj(isStopping.value, 1));
      listStatusCar.add(StatusCarObj(isParking.value, 2));
      listStatusCar.add(StatusCarObj(isLostGps.value, 3));
      List<String> listStatus = listStatusCar.where((item) => item.status == true).map((item) => item.value.toString()).toList();
      if (chosenUnit != null && chosenCar != null && listStatus.isNotEmpty) {
        isValidated = true;
      } else {
        isValidated = false;
      }
      if (isValidated) {
        showLoadingDialog();
        final user = DataCenter.shared().getUserInfo();
        GetVehicleMonitoringRequest request = GetVehicleMonitoringRequest(sysUserRequest: SysUserRequest(authenticationInfo: AuthenticationInfo(username: user.loginName), sysUserId: user.sysUserId), bookCarDto: BookCarDtoObj(licenseCar: chosenCar.licenseCar, sysGroupId: idChosenUnit, listStatus: listStatus));
        final response = await api.getVehicleMonitoring(request);

        final result = BaseApiResponse.fromJson(response);
        if (result.success) {
          final data = CarMonitoringResponse.fromJson(result.data);
          if (data.resultInfo.status == RESULT_OK) {
            listCurrentCarLocation = RxList<LstCarLocationCurrents>.from(data.lstCarLocationCurrents);
            dismissLoadingDialog();
            await bottomSheetController.collapse();
            if (listCurrentCarLocation.isNotEmpty) {
              showErrorToast(error: 'Lấy lịch sử đặt xe đã chọn thành công');
              // await addPolyline();
            } else {
              showErrorToast(error: 'Không có vị trí của xe đã chọn.');
            }
          } else {
            dismissLoadingDialog();
            showErrorToast(error: 'Có lỗi xảy ra trong quá trình tìm kiếm');
          }
        } else {
          dismissLoadingDialog();
          showErrorToast(error: 'Có lỗi xảy ra trong quá trình tìm kiếm');
        }
      } else {
        showErrorToast(error: 'Bạn chưa điền đủ thông tin');
      }
    } catch (e) {
      dismissLoadingDialog();
      showErrorToast(error: 'Có lỗi xảy ra trong quá trình tìm kiếm');
    }
  }

  getAppVersion() async {
    try {
      successVersion.value = false;
      final response = await api.getAppVersion();
      final result = BaseApiResponse.fromJson(response);
      if (result.success) {
        final data = GetAppVersionResponse.fromJson(result.data);
        if (data.resultInfo.status == RESULT_OK) {
          version = data.appVersionWorkItemDTO?.version ?? '';
          if (version.isNotEmpty) {
            successVersion.value = true;
          }
        } else {
          showErrorToast(error: data.resultInfo.status);
        }
      } else {
        showErrorToast();
      }
    } catch (e) {
      showErrorToast();
      dismissLoadingDialog();
    }
  }
}

class StatusCarObj {
  StatusCarObj(this.status, this.value);

  bool status;
  int value;
}
