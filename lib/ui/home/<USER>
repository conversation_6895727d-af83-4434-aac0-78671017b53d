import 'package:bottom_sheet_bar/bottom_sheet_bar.dart';
import 'package:flutter/material.dart';
import 'package:get/get_state_manager/get_state_manager.dart';
import 'package:trackcarvcc/constants/style/style.dart';
import 'package:trackcarvcc/controllers/monitoring_map_controller.dart';
import 'monitoring_maps.dart';
import 'widget/collapsed_bottom_sheet_widget.dart';
import 'widget/expanded_bottom_sheet_monitoring.dart';

class CarMonitoringPage extends GetView<MonitoringMapController> {
  @override
  Widget build(BuildContext context) {
    return GetBuilder<MonitoringMapController>(
      builder: (_) {
        return Scaffold(
          appBar: AppBar(
            backgroundColor: AppThemes.colorViettelRed,
            brightness: Brightness.light,
            elevation: 1.0,
            leading: IconButton(
              icon: const Icon(
                Icons.menu,
                color: Colors.white,
              ),
              onPressed: () {
                Scaffold.of(context).openDrawer();
              },
            ),
            centerTitle: true,
            title: Text(
              'Giám sát xe',
              style: CommonTextStyle.textStyleFontLatoLargeBoldWhite,
            ),
          ),
          backgroundColor: Colors.white,
          body: _body(),
        );
      },
    );
  }

  _body() => BottomSheetBar(
    controller: controller.bottomSheetController,
    borderRadius: BorderRadius.circular(mRadiusMedium),
    borderRadiusExpanded: BorderRadius.circular(mRadiusMedium),
    expandedBuilder: (scrollController) => _expandedWidget(),
    collapsed: _collapsedWidget(),
    locked: false,
    color: Colors.transparent,
    backdropColor: Colors.grey.shade400.withOpacity(0.5),
    height: 45.0,
    body: MonitoringMapScreen(),
  );

  _collapsedWidget() => CollapsedBottomSheet(
    controller: controller.bottomSheetController,
  );

  _expandedWidget() => ExpandedBottomSheetMonitoring(
    bottomController: controller.bottomSheetController,
  );
}
