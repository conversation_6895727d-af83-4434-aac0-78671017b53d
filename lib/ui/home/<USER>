import 'package:flutter/material.dart';
import 'package:get/get.dart';

// import 'package:google_maps_controller/google_maps_controller.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:trackcarvcc/controllers/map_controller.dart';
import 'package:trackcarvcc/ui/splash/widget/notify_update_version_dialog.dart';
import 'package:vtmap_gl/vtmap_gl.dart' ;
import 'package:trackcarvcc/constants/constant_value.dart';

// ignore: must_be_immutable
class HomeMapScreen extends GetView<MapController> {
  AnimationController _animationController;
  String _currentVersion = '';

  // Map<MarkerId, Marker> markers = <MarkerId, Marker>{};

  // VTMap.MapboxMapController mapboxMapController;

  @override
  Widget build(BuildContext context) {
    return _body(context);
  }

  Widget _body(BuildContext context) {
    return GetBuilder<MapController>(
      initState: (_) {
        _getPackageInfo();
        // controller.initMap();
      },
      didChangeDependencies: (_) {
        controller.checkPermissions();
      },
      builder: (_) {
        return Material(
          child: _content(context),
        );
      },
    );
  }

  Widget _content(BuildContext context) {
    return Column(
      children: <Widget>[
        Expanded(
            // child: GoogleMaps(
            //   controller: controller.googleMapController,
            // ),
            child: _buildMap()),
        Obx(
          () => controller.successVersion.value && controller.version.isNotEmpty ? _navigate(context) : SizedBox(),
        ),
      ],
    );
  }

  Widget _buildMap() {
    try {
      return VTMap(
        accessToken: Constants.ACCESS_TOKEN,
        onMapCreated: (c) {
          try {
            print('HomeMapScreen: VTMap onMapCreated called');
            controller.onMapCreated(c);
          } catch (e) {
            print('HomeMapScreen: Error in onMapCreated: $e');
          }
        },
        initialCameraPosition: CameraPosition(
          target: LatLng(15.96130913439005, 108.1949226475952),
          zoom: 4
        ),
      );
    } catch (e) {
      print('HomeMapScreen: Error creating VTMap: $e');
      return Container(
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.error, size: 64, color: Colors.red),
              SizedBox(height: 16),
              Text('Không thể tải bản đồ', style: TextStyle(fontSize: 18)),
              SizedBox(height: 8),
              Text('Vui lòng thử lại sau', style: TextStyle(fontSize: 14, color: Colors.grey)),
            ],
          ),
        ),
      );
    }
  }

  // Lấy thông tin phiên bản đang chạy trên thiết bị
  Future<void> _getPackageInfo() async {
    final PackageInfo packageInfo = await PackageInfo.fromPlatform();
    _currentVersion = packageInfo.version;
  }

  Widget _navigate(BuildContext context) {
    controller.successVersion.value = false;
    Future.delayed(
      Duration(milliseconds: 0),
      () {
        if (controller.version != null && controller.version.compareTo(_currentVersion) > 0) {
          Get.dialog(
            NotifyUpdateVersionAppDialog(
              versionApp: controller.version,
              currentVersion: _currentVersion,
              onSuccess: () {},
            ),
            barrierDismissible: false,
          );
        }
      },
    );

    return Container(
      width: 0,
    );
  }
}
