
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:trackcarvcc/constants/constant_value.dart';
// import 'package:google_maps_controller/google_maps_controller.dart';
import 'package:trackcarvcc/controllers/monitoring_map_controller.dart';
import 'package:vtmap_gl/vtmap_gl.dart';

// ignore: must_be_immutable
class MonitoringMapScreen extends GetView<MonitoringMapController> {
  AnimationController _animationController;

  // Map<MarkerId, Marker> markers = <MarkerId, Marker>{};


  @override
  Widget build(BuildContext context) {
    return _body(context);
  }

  Widget _body(BuildContext context) {
    return GetBuilder<MonitoringMapController>(
      initState: (_) {
        // controller.initMap();
      },
      didChangeDependencies: (_) {
        controller.checkPermissions();
      },
      builder: (_) {
        return Material(
          child: _content(),
        );
      },
    );
  }

  Widget _content() {
    return Column(
      children: <Widget>[
        Expanded(
          // child: GoogleMaps(
          //   controller: controller.googleMapController,
          // ),
          child: _buildMap(),
        )
      ],
    );
  }

  Widget _buildMap() {
    try {
      return VTMap(
        accessToken: Constants.ACCESS_TOKEN,
        onMapCreated: (c) {
          try {
            print('MonitoringMapScreen: VTMap onMapCreated called');
            controller.onMapCreated(c);
          } catch (e) {
            print('MonitoringMapScreen: Error in onMapCreated: $e');
          }
        },
        initialCameraPosition: CameraPosition(
          target: LatLng(15.96130913439005, 108.1949226475952),
          zoom: 4
        ),
      );
    } catch (e) {
      print('MonitoringMapScreen: Error creating VTMap: $e');
      return Container(
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.error, size: 64, color: Colors.red),
              SizedBox(height: 16),
              Text('Không thể tải bản đồ', style: TextStyle(fontSize: 18)),
              SizedBox(height: 8),
              Text('Vui lòng thử lại sau', style: TextStyle(fontSize: 14, color: Colors.grey)),
            ],
          ),
        ),
      );
    }
  }
}
