import 'package:location/location.dart';
import 'package:flutter/material.dart';

class CurrentLocationWidget extends InheritedWidget {
  // 1
  CurrentLocationWidget({
    Widget child,
    this.location,
  }) : super(child: child);

  // 2
  final LocationData location;

  // 3
  @override
  bool updateShouldNotify(CurrentLocationWidget oldWidget) {
    return oldWidget.location != location;
  }

  // 4
  static CurrentLocationWidget of(BuildContext context) {
    // 5
    return context.dependOnInheritedWidgetOfExactType<CurrentLocationWidget>();
  }
}
