import 'package:flutter/material.dart';
import 'package:trackcarvcc/constants/style/style.dart';

class CommonTextField extends StatelessWidget {
  final String label;
  final VoidCallback onPress;
  final IconData icon;
  final bool isValidate;
  final TextEditingController controller;
  final String hinText;

  const CommonTextField(
      {Key key,
      this.label,
      this.onPress,
      this.icon,
      this.isValidate = true,
      this.controller,
      this.hinText})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return _body(context);
  }

  _body(BuildContext context) => Padding(
        padding: const EdgeInsets.only(top: mPaddingLarge),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  icon,
                  color: AppThemes.colorViettelRed,
                ),
                const SizedBox(
                  width: mPadding,
                ),
                Text(
                  label ?? '',
                  style: CommonTextStyle.textStyleFontLatoNormal,
                ),
              ],
            ),
            const SizedBox(height: mPadding),
            GestureDetector(
              onTap: () {
                onPress();
              },
              child: Container(
                decoration: BoxDecoration(
                  color: AppThemes.colorViettelGray3,
                  borderRadius: const BorderRadius.all(
                    Radius.circular(mRadiusSmall),
                  ),
                  border: Border.all(
                      color: isValidate
                          ? AppThemes.colorViettelGray2
                          : AppThemes.colorViettelRed),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: TextField(
                        controller: controller ?? TextEditingController(),
                        enabled: false,
                        readOnly: true,
                        decoration: InputDecoration(
                          hintText: hinText ?? 'Tìm kiếm',
                          border: InputBorder.none,
                          contentPadding: const EdgeInsets.all(mPadding),
                          hintStyle:
                              CommonTextStyle.textStyleFontLatoNormalHint,
                          isDense: true,
                        ),
                        autofocus: false,
                        style: CommonTextStyle.textStyleFontLatoNormal,
                        onChanged: (_) {},
                      ),
                    ),
                    Container(
                      width: 50.0,
                      height: 50.0,
                      padding: const EdgeInsets.all(mPadding),
                      margin: const EdgeInsets.only(bottom: 0),
                      //check password field type
                      child: Icon(Icons.arrow_forward_ios),
                    )
                  ],
                ),
              ),
            ),
          ],
        ),
      );
}
