import 'package:bottom_sheet_bar/bottom_sheet_bar.dart';
import 'package:flutter/material.dart';
import 'package:trackcarvcc/constants/style/style.dart';

class CollapsedBottomSheet extends StatelessWidget {
  final BottomSheetBarController controller;

  const CollapsedBottomSheet({Key key, this.controller}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return _body(context);
  }

  _body(BuildContext context) =>  Container(
    decoration: BoxDecoration(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(mRadiusMedium),
          topRight: Radius.circular(mRadiusMedium),),
        color: Colors.grey.shade300),
    child: Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Row(
          children: [
            const SizedBox(
              width: mPadding,
            ),
            Icon(Icons.timeline,
                color: AppThemes.colorViettelRed, size: 25),
            const SizedBox(
              width: mPadding,
            ),
            Text(
              '<PERSON><PERSON><PERSON> kiếm thông tin xe',
              style: CommonTextStyle.textStyleFontLatoNormal,
            ),
          ],
        ),
        InkResponse(
            onTap: () {
              controller.expand();
            },
            child: Padding(
              padding: const EdgeInsets.all(mPadding),
              child: Icon(
                Icons.arrow_drop_up,
                size: 35,
              ),
            ))
      ],
    ),
  );
}
