import 'package:bottom_sheet_bar/bottom_sheet_bar.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:trackcarvcc/constants/constant_value.dart';
import 'package:trackcarvcc/constants/style/style.dart';
import 'package:trackcarvcc/controllers/monitoring_map_controller.dart';
import 'package:trackcarvcc/helpers/extensions.dart';
import 'package:trackcarvcc/repository/preferences/data_center.dart';
import 'package:trackcarvcc/ui/home/<USER>/bottom_sheet/search_unit_bottom_sheet.dart';
import 'package:trackcarvcc/widget/common_button.dart';

import 'bottom_sheet/item_choose_status_car.dart';
import 'bottom_sheet/search_car_by_unit_bottom_sheet.dart';
import 'common_text_field_widget.dart';

// ignore: must_be_immutable
class ExpandedBottomSheetMonitoring extends GetView<MonitoringMapController> {
  final BottomSheetBarController bottomController;

  ExpandedBottomSheetMonitoring({Key key, this.bottomController})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return _body(context);
  }

  _body(BuildContext context) => Container(
        height: MediaQuery.of(context).size.height * 0.82,
        padding: const EdgeInsets.only(
            left: mPaddingLarge, right: mPadding, bottom: mPaddingLarge),
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius:
              BorderRadius.vertical(top: Radius.circular(mRadiusMedium)),
        ),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _header(),
              _divider(),
              isHasRoleCodeBANXETCT() ? _unitSearch(context) : SizedBox(),
              _carSearch(context),
              _labelCarStatus(),
              Obx(() => ItemChooseStatusCar(
                    value: controller.isRunning.value,
                    label: 'Đang chạy',
                    onChanged: (value) {
                      controller.isRunning.value = value;
                    },
                  )),
              Obx(() => ItemChooseStatusCar(
                    value: controller.isStopping.value,
                    label: 'Dừng',
                    onChanged: (value) {
                      controller.isStopping.value = value;
                    },
                  )),
              Obx(() => ItemChooseStatusCar(
                    value: controller.isParking.value,
                    label: 'Đỗ',
                    onChanged: (value) {
                      controller.isParking.value = value;
                    },
                  )),
              Obx(() => ItemChooseStatusCar(
                    value: controller.isLostGps.value,
                    label: 'Mất GPRS',
                    onChanged: (value) {
                      controller.isLostGps.value = value;
                    },
                  )),
              _buttonSearch()
            ],
          ),
        ),
      );

  _header() => Container(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                const SizedBox(
                  width: mPadding,
                ),
                Icon(Icons.search_sharp,
                    color: AppThemes.colorViettelRed, size: 25),
                Text(
                  '  Tìm kiếm thông tin',
                  style: CommonTextStyle.textStyleFontLatoNormal,
                ),
              ],
            ),
            InkResponse(
              onTap: () {
                bottomController.collapse();
              },
              child: Padding(
                padding: const EdgeInsets.all(mPadding),
                child: Icon(
                  Icons.arrow_drop_down,
                  size: 35,
                ),
              ),
            ),
          ],
        ),
      );

  _divider() => Divider(
        color: AppThemes.colorViettelGray3,
        thickness: 1,
        height: 1,
      );

  _unitSearch(BuildContext context) => CommonTextField(
        icon: Icons.ad_units,
        label: 'Đơn vị',
        controller: controller.unitController,
        onPress: () {
          showModalBottomSheet(
            context: context,
            isScrollControlled: true,
            backgroundColor: Colors.transparent,
            builder: (BuildContext context) {
              return SearchUnit(
                onChanged: (unit) {
                  controller.unitController.text = unit.sysGroupName;
                  controller.idChosenUnit = unit.sysGroupId;
                  controller.chosenUnit = unit;
                },
              );
            },
          );
        },
      );

  _carSearch(BuildContext context) => CommonTextField(
        icon: Icons.time_to_leave,
        label: 'Tìm kiếm xe',
        controller: controller.searchCarController,
        onPress: () {
          if(isHasRoleCodeBANXETCT() && controller.idChosenUnit == -1) {
            showErrorToast(error: 'Bạn phải điền thông tin đơn vị trước');
            return;
          }

          showModalBottomSheet(
            context: context,
            isScrollControlled: true,
            backgroundColor: Colors.transparent,
            builder: (BuildContext context) {
              return SearchCarByUnit(
                unitId: isHasRoleCodeBANXETCT() ? controller.idChosenUnit : DataCenter.shared().getUserInfo().sysGroupId,
                onChanged: (car) {
                  controller.searchCarController.text = car.licenseCar;
                  controller.chosenCar = car;
                },
              );
            },
          );
        },
      );

  _labelCarStatus() => Padding(
        padding: const EdgeInsets.only(top: mPaddingLarge),
        child: Text(
          'Trạng thái xe',
          style: CommonTextStyle.textStyleFontLatoNormal,
        ),
      );

  _buttonSearch() => Padding(
        padding: const EdgeInsets.only(top: mPaddingLarge),
        child: CommonButton(
          title: 'Tìm kiếm',
          onButtonClick: () {
            if (!isHasRoleCode()) {
              showErrorToast(error: 'Bạn không có quyền xem chức năng này !');
              return;
            }
            controller.fetchListCarMonitoring(isHasRoleCodeBANXETCT());
          },
        ),
      );

  bool isHasRoleCode() {
    final userProfile = DataCenter.shared().getUserInfo();
    List<String> _roles =
        userProfile.roleCode != null ? userProfile.roleCode.split(";") : [];
    for (var roleCode in _roles) {
      if (roleCode == Constants.THUTRUONGXE ||
          roleCode == Constants.DOITRUONGXE ||
          roleCode == Constants.BANXETCT) return true;
    }
    return false;
  }

  bool isHasRoleCodeBANXETCT() {
    final userProfile = DataCenter.shared().getUserInfo();
    List<String> _roles =
    userProfile.roleCode != null ? userProfile.roleCode.split(";") : [];
    for (var roleCode in _roles) {
      if (roleCode == Constants.BANXETCT) return true;
    }
    return false;
  }
}
