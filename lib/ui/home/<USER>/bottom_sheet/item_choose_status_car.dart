import 'package:flutter/material.dart';
import 'package:trackcarvcc/constants/style/style.dart';

class ItemChooseStatusCar extends StatelessWidget {
  final String label;
  final ValueChanged<bool> onChanged;
  final bool value;

  const ItemChooseStatusCar(
      {Key key, this.value = false, this.label, this.onChanged})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return _body(context);
  }

  _body(BuildContext context) => GestureDetector(
        child: Padding(
          padding: const EdgeInsets.only(top: mPadding),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                label,
                style: CommonTextStyle.textStyleFontLatoNormal,
              ),
              Checkbox(
                  value: value,
                  onChanged: onChanged,
                  checkColor: Colors.white,
                  activeColor: AppThemes.colorViettelRed)
            ],
          ),
        ),
      );
}
