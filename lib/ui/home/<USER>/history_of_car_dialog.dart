import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:trackcarvcc/constants/style/style.dart';
import 'package:trackcarvcc/controllers/map_controller.dart';
import 'package:trackcarvcc/models/response/list_car_history_response.dart';

class HistoryOfCarDialog extends GetView<MapController> {
  final List<LstBookCarDtoHistory> listBookCar;

  const HistoryOfCarDialog({Key key, this.listBookCar}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return _body(context);
  }

  _body(BuildContext context) => Container(
        decoration: BoxDecoration(
          color: AppThemes.colorViettelGray3,
          borderRadius: const BorderRadius.all(
            Radius.circular(mRadiusSmall),
          ),
        ),
        height: MediaQuery.of(context).size.height * 0.6,
        width: MediaQuery.of(context).size.width * 0.8,
        child: ListView.builder(
          itemCount: listBookCar.length,
          itemBuilder: (context, index) {
            return InkWell(
              onTap: () {
                Get.close(1);
                controller.fetchHistoryDetailCar(listBookCar[index].code);
              },
              child: Card(
                  elevation: 5.0,
                child: Padding(
                  padding: const EdgeInsets.all(mPadding),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        listBookCar[index].code,
                        style: CommonTextStyle.textStyleFontLatoNormal,
                      ),
                      const SizedBox(
                        height: mPaddingMedium,
                      ),
                      Divider(
                        height: 1,
                        color: AppThemes.colorViettelGray1,
                      ),
                      const SizedBox(
                        height: mPaddingMedium,
                      ),
                      _itemTime(
                          'Điểm đi:  ', listBookCar[index].fromAddress ?? ''),
                      const SizedBox(
                        height: mPaddingMedium,
                      ),
                      _itemTime(
                          'Điểm đến: ', listBookCar[index].toAddress ?? ''),
                    ],
                  ),
                ),
              ),
            );
          },
        ),
      );

  _itemTime(String label, String address) => Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: CommonTextStyle.textStyleFontLatoNormalHint,
          ),
          Flexible(
            child: Text(
              address,
              textAlign: TextAlign.right,
              style: CommonTextStyle.textStyleFontLatoNormal,
            ),
          ),
        ],
      );
}
