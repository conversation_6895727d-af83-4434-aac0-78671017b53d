import 'package:flutter/material.dart';
import 'package:trackcarvcc/constants/style/style.dart';
import 'package:trackcarvcc/helpers/extensions.dart';
import 'package:trackcarvcc/helpers/string_utils.dart';
import 'package:trackcarvcc/models/response/get_history_detail_car_response.dart';

// ignore: must_be_immutable
class InfoWindowMarkerDialog extends StatelessWidget {
  LstBookCarDtoDetail last;
  LstBookCarDtoDetail first;

  InfoWindowMarkerDialog(this.first, this.last);

  @override
  Widget build(BuildContext context) {
    return _body(context);
  }

  _body(BuildContext context) => Container(
        decoration: BoxDecoration(
          color: AppThemes.colorViettelGray3,
          borderRadius: const BorderRadius.all(
            Radius.circular(mRadiusSmall),
          ),
        ),
        height: MediaQuery.of(context).size.height * 0.6,
        width: MediaQuery.of(context).size.width * 0.8,
        child: Card(
          elevation: 5.0,
          child: Padding(
            padding: const EdgeInsets.all(mPadding),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(
                  height: mPaddingMedium,
                ),
                _itemTime('Người lái:  ', last.driverName ?? ''),
                const SizedBox(
                  height: mPaddingMedium,
                ),
                Divider(
                  height: 1,
                  color: AppThemes.colorViettelGray1,
                ),
                const SizedBox(
                  height: mPaddingMedium,
                ),
                _itemTime('Người tạo: ', last.fullName ?? ''),
                const SizedBox(
                  height: mPaddingMedium,
                ),
                Divider(
                  height: 1,
                  color: AppThemes.colorViettelGray1,
                ),
                const SizedBox(
                  height: mPaddingMedium,
                ),
                _itemTime('Nội dung công việc: ', last.content ?? ''),
                const SizedBox(
                  height: mPaddingMedium,
                ),
                Divider(
                  height: 1,
                  color: AppThemes.colorViettelGray1,
                ),
                const SizedBox(
                  height: mPaddingMedium,
                ),
                _itemTime('Thời gian đã đi: ', tinhThoiGianDaDi(first.utcTime, last.utcTime) ?? ''),
              ],
            ),
          ),
        ),
      );

  _itemTime(String label, String address) => Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: CommonTextStyle.textStyleFontLatoNormalHint,
          ),
          Flexible(
            child: Text(
              address,
              textAlign: TextAlign.right,
              style: CommonTextStyle.textStyleFontLatoNormal,
            ),
          ),
        ],
      );

  String tinhThoiGianDaDi(String utcTime, String utcTime2) {
    print(utcTime);
    print(utcTime2);
    var dateTimeStart = StringUtils.formatDateTime(utcTime);
    var dateTimeEnd = StringUtils.formatDateTime(utcTime2);
    var d = dateTimeEnd.difference(dateTimeStart);

    var seconds = d.inSeconds;
    final days = seconds~/Duration.secondsPerDay;
    seconds -= days*Duration.secondsPerDay;
    final hours = seconds~/Duration.secondsPerHour;
    seconds -= hours*Duration.secondsPerHour;
    final minutes = seconds~/Duration.secondsPerMinute;
    seconds -= minutes*Duration.secondsPerMinute;

    final List<String> tokens = [];
    if (days != 0) {
      tokens.add("${days} ngày");
    }
    if (tokens.isNotEmpty || hours != 0){
      tokens.add('${hours} giờ');
    }
    if (tokens.isNotEmpty || minutes != 0) {
      tokens.add('${minutes} phút');
    }
    tokens.add('${seconds} giây');

    return tokens.join(' ');
  }
}
