import 'package:bottom_sheet_bar/bottom_sheet_bar.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:trackcarvcc/constants/constant_value.dart';
import 'package:trackcarvcc/constants/style/style.dart';
import 'package:trackcarvcc/controllers/map_controller.dart';
import 'package:trackcarvcc/helpers/extensions.dart';
import 'package:trackcarvcc/helpers/string_utils.dart';
import 'package:trackcarvcc/repository/preferences/data_center.dart';
import 'package:trackcarvcc/ui/home/<USER>/bottom_sheet/search_unit_bottom_sheet.dart';
import 'package:trackcarvcc/widget/calendar_dialog.dart';
import 'package:trackcarvcc/widget/common_button.dart';

import 'bottom_sheet/search_car_by_unit_bottom_sheet.dart';
import 'common_text_field_widget.dart';

// ignore: must_be_immutable
class ExpandedBottomSheet extends GetView<MapController> {
  final BottomSheetBarController bottomController;

  ExpandedBottomSheet({Key key, this.bottomController}) : super(key: key);

  TextEditingController _timeStartController = TextEditingController();
  TextEditingController _timeFinistController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return _body(context);
  }

  _body(BuildContext context) => Container(
        height: MediaQuery.of(context).size.height * 0.82,
        padding: const EdgeInsets.only(left: mPaddingLarge, right: mPadding, bottom: mPaddingLarge),
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(mRadiusMedium)),
        ),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _header(),
              _divider(),
              _unitSearch(context),
              _carSearch(context),
              _labelChooseTime(),
              _startTime(context),
              _finishTime(context),
              _buttonSearch()
            ],
          ),
        ),
      );

  _header() => Container(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                const SizedBox(
                  width: mPadding,
                ),
                Icon(Icons.search_sharp, color: AppThemes.colorViettelRed, size: 25),
                Text(
                  '  Tìm kiếm thông tin',
                  style: CommonTextStyle.textStyleFontLatoNormal,
                ),
              ],
            ),
            InkResponse(
              onTap: () {
                bottomController.collapse();
              },
              child: Padding(
                padding: const EdgeInsets.all(mPadding),
                child: Icon(
                  Icons.arrow_drop_down,
                  size: 35,
                ),
              ),
            ),
          ],
        ),
      );

  _divider() => Divider(
        color: AppThemes.colorViettelGray3,
        thickness: 1,
        height: 1,
      );

  _unitSearch(BuildContext context) => Visibility(
      visible: controller.isHasRoleCodeBANXETCT(),
      child: CommonTextField(
        icon: Icons.ad_units,
        label: 'Đơn vị',
        controller: controller.unitController,
        onPress: () {
          showModalBottomSheet(
            context: context,
            isScrollControlled: true,
            backgroundColor: Colors.transparent,
            builder: (BuildContext context) {
              return SearchUnit(
                onChanged: (unit) {
                  controller.unitController.text = unit.sysGroupName;
                  controller.idChosenUnit = unit.sysGroupId;
                  controller.chosenUnit = unit;
                },
              );
            },
          );
        },
      ));

  bool isSearch() {
    final userProfile = DataCenter.shared().getUserInfo();
    List<String> _roles = userProfile.roleCode != null ? userProfile.roleCode.split(";") : [];
    for (var roleCode in _roles) {
      if (roleCode == Constants.THUTRUONGXE || roleCode == Constants.DOITRUONGXE) return true;
    }
    return false;
  }

  _carSearch(BuildContext context) => CommonTextField(
        icon: Icons.time_to_leave,
        label: 'Tìm kiếm xe',
        controller: controller.searchCarController,
        onPress: () {
          if (isSearch() || controller.idChosenUnit != -1) {
            showModalBottomSheet(
              context: context,
              isScrollControlled: true,
              backgroundColor: Colors.transparent,
              builder: (BuildContext context) {
                return SearchCarByUnit(
                  unitId: isSearch() ? DataCenter.shared().getUserInfo().sysGroupId : controller.idChosenUnit,
                  onChanged: (car) {
                    controller.searchCarController.text = car.licenseCar;
                    controller.chosenCar = car;
                  },
                );
              },
            );
          } else {
            showErrorToast(error: 'Bạn phải điền thông tin đơn vị trước');
          }
        },
      );

  _labelChooseTime() => Padding(
        padding: const EdgeInsets.only(top: mPaddingLarge),
        child: Text(
          'Chọn khoảng thời gian xe chạy',
          style: CommonTextStyle.textStyleFontLatoNormal,
        ),
      );

  _startTime(BuildContext context) => CommonTextField(
        icon: Icons.timer_outlined,
        label: 'Khởi hành lúc',
        hinText: 'Chọn thời gian khởi hành',
        controller: controller.timeStartController,
        onPress: () {
          showDialog(
            context: context,
            builder: (context) => CalendarDialog(
              isEditTimer: true,
              textTimerController: _timeStartController,
              startDate: DateTime(2020),
              initialSelectedDay: DateTime.now(),
            ),
          ).then(
            (value) {
              if (StringUtils.isEmpty(_timeStartController.text)) {
                _timeStartController.text = Constants.TIMER_DEFAULT;
              }
              controller.timeStartController.text = StringUtils.eventDateFormat(value, _timeStartController.text);
              // onChanged(controller.text);
            },
          );
        },
      );

  _finishTime(BuildContext context) => CommonTextField(
        icon: Icons.lock_clock,
        label: 'Kết thúc lúc',
        hinText: 'Chọn thời gian kết thúc',
        controller: controller.timeFinishController,
        onPress: () {
          showDialog(
            context: context,
            builder: (context) => CalendarDialog(
              isEditTimer: true,
              textTimerController: _timeFinistController,
              startDate: DateTime(2020),
              initialSelectedDay: DateTime.now(),
            ),
          ).then(
            (value) {
              if (StringUtils.isEmpty(_timeFinistController.text)) {
                _timeFinistController.text = Constants.TIMER_END_DEFAULT;
              }
              controller.timeFinishController.text = StringUtils.eventDateFormat(value, _timeFinistController.text);
              // onChanged(controller.text);
            },
          );
        },
      );

  _buttonSearch() => Padding(
        padding: const EdgeInsets.only(top: mPaddingLarge),
        child: CommonButton(
          title: 'Tìm kiếm',
          onButtonClick: () {
            if (!isHasRoleCodeHanhTrinh()) {
              showErrorToast(error: 'Bạn không có quyền xem chức năng này !');
              return;
            }
            if ((isSearch() && controller.searchCarController.text.isNotEmpty) ||
                (!isSearch() &&
                    controller.unitController.text.isNotEmpty &&
                    controller.searchCarController.text.isNotEmpty)) {
              controller.fetchListCarHistory(isSearch());
            } else {
              showErrorToast(error: 'Bạn chưa điền đẩy đủ thông tin');
            }
          },
        ),
      );

  bool isHasRoleCodeHanhTrinh() {
    final userProfile = DataCenter.shared().getUserInfo();
    List<String> _roles = userProfile.roleCode != null ? userProfile.roleCode.split(";") : [];
    for (var roleCode in _roles) {
      if (roleCode == Constants.HANHTRINH) return true;
    }
    return false;
  }
}
