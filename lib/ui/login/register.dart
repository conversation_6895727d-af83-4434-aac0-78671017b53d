import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:trackcarvcc/constants/constants.dart';
import 'package:trackcarvcc/constants/style/style.dart';
import 'package:trackcarvcc/controllers/controllers.dart';
import 'package:trackcarvcc/controllers/register_controller.dart';
import 'package:trackcarvcc/routes/routes.dart';
import 'package:trackcarvcc/ui/login/widget/custom_flat_button.dart';
import 'package:trackcarvcc/ui/login/widget/custom_flat_button_white.dart';
import 'package:trackcarvcc/ui/login/widget/custom_text_form_field.dart';

class RegisterPage extends GetView<RegisterController> {
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).requestFocus(FocusNode());
      },
      child: Scaffold(
        appBar: AppBar(
          backgroundColor: AppThemes.colorViettelRed,
          title: Text('<PERSON><PERSON>ng ký tài khoản'),
        ),
        resizeToAvoidBottomInset: false,
        body: Stack(
          children: [
            Positioned.fill(
              child: Image.asset(mImgLogin, fit: BoxFit.cover),
            ),
            Center(
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 32),
                child: Form(
                  // key: key,
                  child: Column(
                    children: <Widget>[
                      const SizedBox(height: 70.0),
                      Image.asset(
                        mLogo,
                        width: MediaQuery.of(context).size.width / 4,
                      ),
                      const SizedBox(height: 8.0),
                      Text(
                        "",
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                        ),
                      ),
                      SizedBox(
                        height: 36,
                      ),
                      _buildCardLogin(context),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCardLogin(BuildContext context) {
    return Card(
      color: Colors.white,
      elevation: 8.0,
      child: Padding(
        padding: const EdgeInsets.fromLTRB(24, 36, 24, 24),
        child: Obx(
          () => Column(
            children: [
              CustomTextFormField(
                controller: controller.userController,
                hintText: 'Nhập tên đăng nhập',
                autocorrect: false,
                prefixIcon: Image.asset(mIconUser),
                onChanged: controller.changeUsername,
                errorText: controller.usernameIsValid
                    ? null
                    : 'Tên đăng nhập không hợp lệ',
              ),
              SizedBox(
                height: 20,
              ),
              CustomTextFormField(
                controller: controller.passController,
                hintText: 'Nhập mật khẩu',
                autocorrect: false,
                prefixIcon: Image.asset(mIconPassword),
                suffixIcon: GestureDetector(
                  onTap: controller.changeShowPassword,
                  child: Icon(
                    controller.showPassword.value
                        ? Icons.visibility_off
                        : Icons.visibility,
                    color: Color(0xFFEE0033),
                  ),
                ),
                obscureText: !controller.showPassword.value,
                onChanged: controller.changePassword,
                errorText:
                    controller.passwordIsValid ? null : 'Mật khẩu không hợp lệ',
              ),
              SizedBox(
                height: 20,
              ),
              CustomTextFormField(
                controller: controller.pass2Controller,
                hintText: 'Nhập lại mật khẩu',
                autocorrect: false,
                prefixIcon: Image.asset(mIconPassword),
                suffixIcon: GestureDetector(
                  onTap: controller.changeShowPassword,
                  child: Icon(
                    controller.showPassword.value
                        ? Icons.visibility_off
                        : Icons.visibility,
                    color: Color(0xFFEE0033),
                  ),
                ),
                obscureText: !controller.showPassword.value,
                onChanged: controller.changePassword2,
                errorText: controller.password2IsValid
                    ? null
                    : 'Mật khẩu không hợp lệ khong trung nhau',
              ),
              SizedBox(
                height: 20,
              ),
              CustomFlatButton(
                child: Text(
                  'Đăng ký',
                  style: TextStyle(color: Colors.white, fontSize: 18),
                ),
                onPressed: () {
                  controller.validate.value = true;
                  if (controller.formLoginIsValid) {
                    FocusScope.of(context).requestFocus(FocusNode());
                    controller.login();
                  }
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}
