import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:trackcarvcc/constants/constants.dart';
import 'package:trackcarvcc/controllers/controllers.dart';
import 'package:trackcarvcc/routes/routes.dart';
import 'package:trackcarvcc/ui/login/widget/custom_flat_button.dart';
import 'package:trackcarvcc/ui/login/widget/custom_text_form_field.dart';
import 'package:trackcarvcc/ui/splash/widget/notify_update_version_dialog.dart';

class LoginPage extends GetView<LoginController> {
  String _currentVersion = '';
  @override
  Widget build(BuildContext context) {
    return GetBuilder<LoginController>(
      initState: (_) {
        _getPackageInfo();
        controller.getAppVersion();
      },
      builder: (_) {
        return GestureDetector(
          onTap: () {
            FocusScope.of(context).requestFocus(FocusNode());
          },
          child: Scaffold(
            resizeToAvoidBottomInset: false,
            body: Stack(
              children: [
                Positioned.fill(
                  child: Image.asset(mImgLogin, fit: BoxFit.cover),
                ),
                Center(
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 32),
                    child: Form(
                      // key: key,
                      child: Column(
                        children: <Widget>[
                          const SizedBox(height: 70.0),
                          Image.asset(
                            mLogo,
                            width: MediaQuery.of(context).size.width / 4,
                          ),
                          const SizedBox(height: 8.0),
                          Text(
                            "",
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 16,
                            ),
                          ),
                          SizedBox(
                            height: 36,
                          ),
                          _buildCardLogin(context),
                        ],
                      ),
                    ),
                  ),
                ),
                Align(
                  alignment: Alignment.bottomCenter,
                  child: Padding(
                    padding: const EdgeInsets.only(bottom: 36.0),
                    child: Text(
                      "Tổng Công ty CP Công trình Viettel",
                      style: TextStyle(color: Colors.white, fontSize: 18),
                    ),
                  ),
                ),
                Obx(
                  () => controller.successVersion.value && controller.version.isNotEmpty ? _navigate(context) : SizedBox(),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  // Lấy thông tin phiên bản đang chạy trên thiết bị
  Future<void> _getPackageInfo() async {
    final PackageInfo packageInfo = await PackageInfo.fromPlatform();
    _currentVersion = packageInfo.version;
  }

  Widget _navigate(BuildContext context) {
    controller.successVersion.value = false;
    Future.delayed(
      Duration(milliseconds: 0),
      () {
        if (controller.version != null && controller.version.compareTo(_currentVersion) > 0){
          Get.dialog(
            NotifyUpdateVersionAppDialog(
              versionApp: controller.version,
              currentVersion: _currentVersion,
              onSuccess: () {},
            ),
            barrierDismissible: false,
          );
        }
      },
    );

    return Container(
      width: 0,
    );
  }

  Widget _buildCardLogin(BuildContext context) {
    return Card(
      color: Colors.white,
      elevation: 8.0,
      child: Padding(
        padding: const EdgeInsets.fromLTRB(24, 36, 24, 24),
        child: Obx(
          () => Column(
            children: [
              CustomTextFormField(
                controller: controller.userController,
                hintText: 'Tên đăng nhập',
                autocorrect: false,
                prefixIcon: Image.asset(mIconUser),
                onChanged: controller.changeUsername,
                errorText: controller.usernameIsValid
                    ? null
                    : 'Tên đăng nhập không hợp lệ',
              ),
              SizedBox(
                height: 20,
              ),
              CustomTextFormField(
                controller: controller.passController,
                hintText: 'Mật khẩu',
                autocorrect: false,
                prefixIcon: Image.asset(mIconPassword),
                suffixIcon: GestureDetector(
                  onTap: controller.changeShowPassword,
                  child: Icon(
                    controller.showPassword.value
                        ? Icons.visibility_off
                        : Icons.visibility,
                    color: Color(0xFFEE0033),
                  ),
                ),
                obscureText: !controller.showPassword.value,
                onChanged: controller.changePassword,
                errorText:
                    controller.passwordIsValid ? null : 'Mật khẩu không hợp lệ',
              ),
              SizedBox(
                height: 20,
              ),
              CustomFlatButton(
                child: Text(
                  'Đăng nhập',
                  style: TextStyle(color: Colors.white, fontSize: 18),
                ),
                onPressed: () {
                  controller.validate.value = true;
                  if (controller.formLoginIsValid) {
                    FocusScope.of(context).requestFocus(FocusNode());
                    controller.login();
                  }
                },
              ),
              SizedBox(
                height: 20,
              ),
              GestureDetector(
                onTap: () {
                  // Navigator.of(context).pushNamed(Routes.register);
                  Get.toNamed(Routers.register);
                },
                child: RichText(
                  text: TextSpan(
                    style: TextStyle(color: Colors.black),
                    children: [
                      TextSpan(
                        text: 'Bạn chưa có tài khoản? ',
                      ),
                      TextSpan(
                        text: 'Đăng ký ngay',
                        style: TextStyle(
                          color: Colors.red,
                          decoration: TextDecoration.underline,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
