import 'package:flutter/material.dart';
import 'package:trackcarvcc/constants/style/style.dart';

class ThonNhaField extends StatefulWidget {
  ThonNhaField(
      {Key key,
      this.controller,
      this.onFocus,
      this.onChanged})
      : super(key: key);

  final TextEditingController controller;

  final VoidCallback onFocus;
  final ValueChanged<String> onChanged;

  @override
  _ThonNhaFieldState createState() => _ThonNhaFieldState();
}

class _ThonNhaFieldState extends State<ThonNhaField> {
  final FocusNode _focus = FocusNode();

  @override
  void initState() {
    super.initState();
    _focus.addListener(() {
      widget.onFocus();
    });
  }

  @override
  Widget build(BuildContext context) {
    return _itemDestinationPoint();
  }

  _itemDestinationPoint() => Padding(
        padding: const EdgeInsets.only(top: mPaddingLarge),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.location_city_outlined,
                  color: AppThemes.colorViettelRed,
                ),
                const SizedBox(
                  width: mPadding,
                ),
                Text(
                  'Thôn, xóm, số nhà',
                  style: CommonTextStyle.textStyleFontLatoNormal,
                ),
              ],
            ),
            const SizedBox(height: mPadding),
            GestureDetector(
              onTap: () {},
              child: Container(
                decoration: BoxDecoration(
                  color: AppThemes.colorViettelGray3,
                  borderRadius: const BorderRadius.all(
                    Radius.circular(mRadiusSmall),
                  ),
                  border: Border.all(
                      color: AppThemes.colorViettelGray2),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: TextField(
                        controller: widget.controller,
                        enabled: true,
                        readOnly: false,
                        focusNode: _focus,
                        decoration: InputDecoration(
                          hintText: 'Nhập địa chỉ nhà',
                          border: InputBorder.none,
                          contentPadding: const EdgeInsets.all(mPadding),
                          hintStyle:
                              CommonTextStyle.textStyleFontLatoNormalHint,
                          isDense: true,
                        ),
                        autofocus: false,
                        style: CommonTextStyle.textStyleFontLatoNormal,
                        onChanged: (value) {
                          widget.onChanged(value);
                        },
                      ),
                    ),
                    Container(
                      width: 50.0,
                      height: 50.0,
                      padding: const EdgeInsets.all(mPadding),
                      margin: const EdgeInsets.only(bottom: 0),
                      //check password field type
                      child: Icon(Icons.keyboard_arrow_down_outlined),
                    )
                  ],
                ),
              ),
            ),
          ],
        ),
      );
}
