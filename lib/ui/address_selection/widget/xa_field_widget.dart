import 'package:flutter/material.dart';
import 'package:trackcarvcc/constants/style/style.dart';
import 'bottom_sheet/search_xa_widget.dart';

class XaField extends StatelessWidget {
  const XaField({Key key, this.controller, this.isValidated, this.onChanged}) : super(key: key);

  final TextEditingController controller;
  final bool isValidated;
  final ValueChanged<String> onChanged;
  @override
  Widget build(BuildContext context) {
    return _itemDestinationPoint(context);
  }

  _itemDestinationPoint(BuildContext context) => Padding(
    padding: const EdgeInsets.only(top: mPaddingLarge),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              Icons.location_city_outlined,
              color: AppThemes.colorViettelRed,
            ),
            const SizedBox(
              width: mPadding,
            ),
            Text(
              'Xã/ Phường',
              style: CommonTextStyle.textStyleFontLatoNormal,
            ),
          ],
        ),
        const SizedBox(height: mPadding),
        GestureDetector(
          onTap: () {
            showModalBottomSheet(
              context: context,
              isScrollControlled: true,
              backgroundColor: Colors.transparent,
              builder: (BuildContext context) {
                return SearchXa(
                  onChanged: (xaPhuong) {
                    controller.text = xaPhuong.nameLocation;
                    onChanged(xaPhuong.nameLocation);
                  },
                );
              },
            );
          },
          child: Container(
            decoration: BoxDecoration(
              color: AppThemes.colorViettelGray3,
              borderRadius: const BorderRadius.all(
                Radius.circular(mRadiusSmall),
              ),
              border: Border.all(
                  color: isValidated
                      ? AppThemes.colorViettelGray2
                      : AppThemes.colorViettelRed),
            ),
            child: Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: controller,
                    enabled: false,
                    readOnly: true,
                    decoration: InputDecoration(
                      hintText: 'Chọn xã/ phường',
                      border: InputBorder.none,
                      contentPadding: const EdgeInsets.all(mPadding),
                      hintStyle:
                      CommonTextStyle.textStyleFontLatoNormalHint,
                      isDense: true,
                    ),
                    autofocus: false,
                    style: CommonTextStyle.textStyleFontLatoNormal,
                    onChanged: (value) {},
                  ),
                ),
                Container(
                  width: 50.0,
                  height: 50.0,
                  padding: const EdgeInsets.all(mPadding),
                  margin: const EdgeInsets.only(bottom: 0),
                  //check password field type
                  child: Icon(Icons.keyboard_arrow_down_outlined),
                )
              ],
            ),
          ),
        ),
      ],
    ),
  );
}
