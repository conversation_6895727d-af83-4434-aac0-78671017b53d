import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:trackcarvcc/constants/style/style.dart';
import 'package:trackcarvcc/controllers/controllers.dart';
import 'package:trackcarvcc/ui/address_selection/data/address_info.dart';
import 'package:trackcarvcc/ui/address_selection/widget/xa_field_widget.dart';
import 'package:trackcarvcc/widget/common_button.dart';
import 'widget/city_field_widget.dart';
import 'widget/huyen_field_widget.dart';
import 'widget/thon_field_widget.dart';
import 'package:trackcarvcc/helpers/extensions.dart';

class AddressSelectionPage extends GetView<AddressController> {
  final ScrollController _scrollController = ScrollController();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: AppThemes.colorViettelRed,
        brightness: Brightness.light,
        elevation: 1.0,
        leading: IconButton(
          icon: const Icon(
            Icons.arrow_back,
            color: Colors.white,
          ),
          onPressed: () {
            Get.back();
          },
        ),
        centerTitle: true,
        title: Text(
          'Chọn địa chỉ',
          style: CommonTextStyle.textStyleFontLatoLargeBoldWhite,
        ),
      ),
      backgroundColor: Colors.white,
      body: _body(context),
    );
  }

  _body(BuildContext context) => GestureDetector(
        onTap: () {
          FocusScope.of(context).requestFocus(FocusNode());
        },
        child: Padding(
          padding: const EdgeInsets.all(mPaddingLarge),
          child: Stack(
            children: [
              Container(
                height: double.infinity,
                width: double.infinity,
                child: SingleChildScrollView(
                  controller: _scrollController,
                  child: Flex(
                    direction: Axis.horizontal,
                    children: [
                      Expanded(
                        child: Column(
                          children: [
                            _city(),
                            _quanHuyen(),
                            _xaPhuong(),
                            _thonXom(),
                            const SizedBox(
                              height: 80,
                            )
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              _buttonChoose()
            ],
          ),
        ),
      );

  _buttonChoose() => Positioned(
        bottom: 0,
        left: 0,
        right: 0,
        child: Padding(
          padding: const EdgeInsets.only(top: mPaddingXLarge),
          child: CommonButton(
            title: 'Lưu địa chỉ',
            textColor: Colors.white,
            onButtonClick: () {
              controller.validate.value = true;
              if (controller.formIsValid) {
                final AddressInfo addressInfo = AddressInfo(
                    controller.cityTextFieldController.text,
                    controller.quanHuyenTextFieldController.text,
                    controller.xaPhuongTextFieldController.text,
                    controller.thonXomTextFieldController.text,
                    controller.provinceId.value,
                );
                Get.back(
                    result: addressInfo);
              } else {
                showErrorToast(error: 'Bạn phải điền đầy đủ thông tin');
              }
            },
          ),
        ),
      );

  _city() => Obx(
        () => CityField(
            controller: controller.cityTextFieldController,
            isValidated: controller.cityIsValid,
            onChangedProvinceId:(id){
              controller.provinceId.value = id;
            },
            onChanged: controller.changeCity),
      );

  _quanHuyen() => Obx(
        () => HuyenField(
            controller: controller.quanHuyenTextFieldController,
            isValidated: controller.quanHuyenIsValid,
            onChanged: controller.changeQuanHuyen),
      );

  _xaPhuong() => Obx(() => XaField(
      controller: controller.xaPhuongTextFieldController,
      isValidated: controller.xaPhuongIsValid,
      onChanged: controller.changeXaPhuong));

  _thonXom() => ThonNhaField(
      onFocus: () {
        _scrollController?.animateTo(
          _scrollController?.position?.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.fastOutSlowIn,
        );
      },
      controller: controller.thonXomTextFieldController,
      onChanged: controller.changeThonXom);
}
