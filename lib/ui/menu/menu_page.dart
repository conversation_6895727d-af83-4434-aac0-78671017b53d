import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:trackcarvcc/constants/style/style.dart';
import 'package:trackcarvcc/repository/preferences/pref.dart';
import 'package:trackcarvcc/routes/routes.dart';
import 'package:trackcarvcc/ui/app_nav/nav_manager.dart';
import 'package:trackcarvcc/ui/menu/widget/menu_page_nav_item.dart';
import 'package:trackcarvcc/ui/menu/widget/menu_page_nav_profile_header.dart';

class MenuPage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Drawer(
      child: ListView(
        children: [
          DrawerHeader(
            decoration: BoxDecoration(
              color: AppThemes.colorViettelRed,
            ),
            child: MenuPageNavProfileHeaderWidget(),
          ),
          <PERSON>umn(
            children: NavAppPageManager.shared.menus,
          ),
          MenuPageNavItem(
            icon: Icon(Icons.logout),
            title: "Đăng xuất",
            onTap: () {
              _handleLogout();
            },
            color: Colors.black,
          )
        ],
      ),
    );
  }

  _handleLogout() {
    Get.dialog(AlertDialog(
      title: Text(
        'Đăng xuất',
        style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
      ),
      content: Text('Bạn có muốn đăng xuất không?'),
      actions: <Widget>[
        FlatButton(
          minWidth: 80,
          onPressed: () {
            Get.back();
          },
          textColor: Color(0xffd58431),
          child: Text('Hủy'),
        ),
        FlatButton(
          minWidth: 120,
          height: 40,
          onPressed: () {
            DataCenter.shared().clearAllData();
            NavAppPageManager.shared.moveToPage(NavAppPageScreen.HOME_PAGE,
                shouldPopNavDrawer: true);
            Get.offAllNamed(Routers.login);
          },
          color: Color(0xffd58431),
          textColor: Colors.white,
          child: Text('Xác nhận'),
        ),
      ],
    ));
  }
}
