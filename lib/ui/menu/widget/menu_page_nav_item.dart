import 'package:flutter/material.dart';
import 'package:trackcarvcc/constants/style/style.dart';

const double _sizeHomeNavMenu = 48.0;
const double _paddingNavIconTopBot = 12.0;
const double _paddingNavIconRight = 16.0;

typedef OnMenuPageNavItemTap = void Function();

class MenuPageNavItem extends StatelessWidget {
  const MenuPageNavItem({
    this.icon,
    this.title,
    this.onTap,
    this.color,
    this.isSelected = false,
  });

  final Widget icon;
  final String title;
  final OnMenuPageNavItemTap onTap;
  final Color color;
  final bool isSelected;

  @override
  Widget build(BuildContext context) {
    return Ink(
      color: isSelected
          ? AppThemes.colorViettelGray2.withOpacity(0.3)
          : Colors.transparent,
      child: ListTile(
        onTap: onTap,
        selected: true,
        title: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Expanded(
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.only(
                        top: _paddingNavIconTopBot,
                        bottom: _paddingNavIconTopBot,
                        right: _paddingNavIconRight),
                    width: _sizeHomeNavMenu,
                    height: _sizeHomeNavMenu,
                    child: icon,
                  ),
                  Expanded(
                    child: FittedBox(
                      fit: BoxFit.scaleDown,
                      alignment : Alignment.centerLeft,
                      child: Text(
                        title,
                        style: TextStyle(
                          color: isSelected ? AppThemes.colorViettelRed : color,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
