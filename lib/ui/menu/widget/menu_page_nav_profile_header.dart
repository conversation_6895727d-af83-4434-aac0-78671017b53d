import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:trackcarvcc/constants/images.dart';
import 'package:trackcarvcc/constants/constants.dart';
import 'package:trackcarvcc/controllers/menu_controller.dart';
import 'package:trackcarvcc/repository/preferences/pref.dart';

class MenuPageNavProfileHeaderWidget extends GetView<MenuController> {
  @override
  Widget build(BuildContext context) {
    final user = DataCenter.shared().getUserInfo();
    return Stack(
      children: [
        Positioned(
          right: 0,
          top: 0,
          child: Offstage(
            offstage: !user.roleCode.split(";").contains(Constants.LAIXE),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Text(
                  'Cập nhật vị trí: ',
                  style: TextStyle(
                      color: Colors.white.withAlpha(178), fontSize: 14),
                ),
                Obx(
                  () => Switch(
                    activeColor: Colors.white,
                    activeTrackColor: Colors.white,
                    inactiveThumbColor: Colors.white,
                    inactiveTrackColor: Colors.grey.withOpacity(0.6),
                    onChanged: (value) {
                      controller.switchValue.value = value;
                      if(value) {
                        controller.updateLocation();
                      }
                    },
                    value: controller.switchValue.value,
                  ),
                ),
              ],
            ),
          ),
        ),
        Positioned(
          left: 0,
          right: 0,
          bottom: 0,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Image.asset(mIconAvatar,
                  color: Colors.white, width: 80, height: 80),
              const SizedBox(height: 8),
              Text(
                user?.fullName ?? '',
                style: TextStyle(color: Colors.white, fontSize: 16),
              ),
              const SizedBox(height: 4),
              Text(
                user?.email ?? '',
                style:
                    TextStyle(color: Colors.white.withAlpha(178), fontSize: 14),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
