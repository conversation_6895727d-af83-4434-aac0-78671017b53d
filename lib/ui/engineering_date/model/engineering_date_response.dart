import 'package:trackcarvcc/models/base/result_info.dart';
import 'package:trackcarvcc/ui/images/image_info.dart';

import 'engineering_date_request.dart';

class EngineeringDateResponse {
  ResultInfo resultInfo;
  List<EngineeringDateDTO> listTechnicalDTO;
  List<EngineeringDateDTO> listLicenseCar;
  List<ListTechnicalDetailDTO> listTechnicalDetailDTO;

  EngineeringDateResponse({this.resultInfo, this.listTechnicalDTO, this.listLicenseCar, this.listTechnicalDetailDTO});

  EngineeringDateResponse.fromJson(Map<String, dynamic> json) {
    resultInfo = json['resultInfo'] != null ? new ResultInfo.fromJson(json['resultInfo']) : null;
    if (json['listTechnicalDTO'] != null) {
      listTechnicalDTO = new List<EngineeringDateDTO>();
      json['listTechnicalDTO'].forEach((v) {
        listTechnicalDTO.add(new EngineeringDateDTO.fromJson(v));
      });
    }
    if (json['listLicenseCar'] != null) {
      listLicenseCar = new List<EngineeringDateDTO>();
      json['listLicenseCar'].forEach((v) {
        listLicenseCar.add(new EngineeringDateDTO.fromJson(v));
      });
    }
    if (json['listTechnicalDetailDTO'] != null) {
      listTechnicalDetailDTO = new List<ListTechnicalDetailDTO>();
      json['listTechnicalDetailDTO'].forEach((v) {
        listTechnicalDetailDTO.add(new ListTechnicalDetailDTO.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.resultInfo != null) {
      data['resultInfo'] = this.resultInfo.toJson();
    }
    if (this.listTechnicalDTO != null) {
      data['listTechnicalDTO'] = this.listTechnicalDTO.map((v) => v.toJson()).toList();
    }
    if (this.listLicenseCar != null) {
      data['listLicenseCar'] = this.listLicenseCar.map((v) => v.toJson()).toList();
    }
    if (this.listTechnicalDetailDTO != null) {
      data['listTechnicalDetailDTO'] = this.listTechnicalDetailDTO.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class EngineeringDateDTO {
  String defaultSortField;
  int messageColumn;
  bool isSize;
  int totalRecord;
  int technicalDateId;
  int sysGroupId;
  String sysGroupName;
  int sysUserId;
  String createdDate;
  int carId;
  String licenseCar;
  int driverId;
  String driverCode;
  String employeeCode;
  String fullName;
  int status;
  String propose;
  int createdBy;
  String createByName;
  String driverName;
  String rejectReson;
  int fwmodelId;

  EngineeringDateDTO(
      {this.defaultSortField,
      this.messageColumn,
      this.isSize,
      this.totalRecord,
      this.technicalDateId,
      this.sysGroupId,
      this.sysGroupName,
      this.sysUserId,
      this.createdDate,
      this.carId,
      this.licenseCar,
      this.driverId,
      this.employeeCode,
      this.fullName,
      this.driverCode,
      this.status,
      this.propose,
      this.createdBy,
      this.createByName,
      this.driverName,
      this.rejectReson,
      this.fwmodelId});

  EngineeringDateDTO.fromJson(Map<String, dynamic> json) {
    defaultSortField = json['defaultSortField'];
    messageColumn = json['messageColumn'];
    isSize = json['isSize'];
    totalRecord = json['totalRecord'];
    technicalDateId = json['technicalDateId'];
    sysGroupId = json['sysGroupId'];
    sysGroupName = json['sysGroupName'];
    sysUserId = json['sysUserId'];
    createdDate = json['createdDate'];
    carId = json['carId'];
    licenseCar = json['licenseCar'];
    driverId = json['driverId'];
    employeeCode = json['employeeCode'];
    fullName = json['fullName'];
    driverCode = json['driverCode'];
    status = json['status'];
    propose = json['propose'];
    createdBy = json['createdBy'];
    createByName = json['createByName'];
    driverName = json['driverName'];
    fwmodelId = json['fwmodelId'];
    rejectReson = json['rejectReson'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['defaultSortField'] = this.defaultSortField;
    data['messageColumn'] = this.messageColumn;
    data['isSize'] = this.isSize;
    data['totalRecord'] = this.totalRecord;
    data['technicalDateId'] = this.technicalDateId;
    data['sysGroupId'] = this.sysGroupId;
    data['sysGroupName'] = this.sysGroupName;
    data['sysUserId'] = this.sysUserId;
    data['createdDate'] = this.createdDate;
    data['carId'] = this.carId;
    data['licenseCar'] = this.licenseCar;
    data['driverId'] = this.driverId;
    data['employeeCode'] = this.employeeCode;
    data['fullName'] = this.fullName;
    data['driverCode'] = this.driverCode;
    data['status'] = this.status;
    data['propose'] = this.propose;
    data['createdBy'] = this.createdBy;
    data['createByName'] = this.createByName;
    data['driverName'] = this.driverName;
    data['fwmodelId'] = this.fwmodelId;
    data['rejectReson'] = this.rejectReson;
    return data;
  }
}
