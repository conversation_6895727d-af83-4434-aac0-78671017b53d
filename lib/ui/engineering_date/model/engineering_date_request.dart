import 'package:trackcarvcc/ui/images/image_info.dart';

import 'engineering_date_response.dart';

class EngineeringDateRequest {
  EngineeringDateDTO technicalDateDTO;
  List<ListTechnicalDetailDTO> listTechnicalDetailDTO;

  EngineeringDateRequest({this.technicalDateDTO, this.listTechnicalDetailDTO});

  EngineeringDateRequest.fromJson(Map<String, dynamic> json) {
    technicalDateDTO =
        json['technicalDateDTO'] != null ? new EngineeringDateDTO.fromJson(json['technicalDateDTO']) : null;
    if (json['listTechnicalDetailDTO'] != null) {
      listTechnicalDetailDTO = new List<ListTechnicalDetailDTO>();
      json['listTechnicalDetailDTO'].forEach((v) {
        listTechnicalDetailDTO.add(new ListTechnicalDetailDTO.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.technicalDateDTO != null) {
      data['technicalDateDTO'] = this.technicalDateDTO.toJson();
    }
    if (this.listTechnicalDetailDTO != null) {
      data['listTechnicalDetailDTO'] = this.listTechnicalDetailDTO.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class ListTechnicalDetailDTO {
  String defaultSortField;
  int messageColumn;
  bool isSize;
  int totalRecord;
  int technicalDateDetailId;
  int technicalDateId;
  int type;
  List<ImageInformation> listImageInformation;
  int fwmodelId;

  ListTechnicalDetailDTO(
      {this.defaultSortField,
        this.messageColumn,
        this.isSize,
        this.totalRecord,
        this.technicalDateDetailId,
        this.technicalDateId,
        this.type,
        this.listImageInformation,
        this.fwmodelId});

  ListTechnicalDetailDTO.fromJson(Map<String, dynamic> json) {
    defaultSortField = json['defaultSortField'];
    messageColumn = json['messageColumn'];
    isSize = json['isSize'];
    totalRecord = json['totalRecord'];
    technicalDateDetailId = json['technicalDateDetailId'];
    technicalDateId = json['technicalDateId'];
    type = json['type'];
    if (json['listImageInformation'] != null) {
      listImageInformation = new List<ImageInformation>();
      json['listImageInformation'].forEach((v) {
        listImageInformation.add(new ImageInformation.fromJson(v));
      });
    }
    fwmodelId = json['fwmodelId'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['defaultSortField'] = this.defaultSortField;
    data['messageColumn'] = this.messageColumn;
    data['isSize'] = this.isSize;
    data['totalRecord'] = this.totalRecord;
    data['technicalDateDetailId'] = this.technicalDateDetailId;
    data['technicalDateId'] = this.technicalDateId;
    data['type'] = this.type;
    if (this.listImageInformation != null) {
      data['listImageInformation'] = this.listImageInformation.map((v) => v.toJson()).toList();
    }
    data['fwmodelId'] = this.fwmodelId;
    return data;
  }
}
