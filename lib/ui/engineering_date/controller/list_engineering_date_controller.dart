import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:trackcarvcc/constants/constants.dart';
import 'package:trackcarvcc/helpers/extensions.dart';
import 'package:trackcarvcc/helpers/string_utils.dart';
import 'package:trackcarvcc/models/models.dart';
import 'package:trackcarvcc/repository/api/api.dart';
import 'package:trackcarvcc/repository/preferences/data_center.dart';
import 'package:trackcarvcc/ui/engineering_date/model/engineering_date_request.dart';
import 'package:trackcarvcc/ui/engineering_date/model/engineering_date_response.dart';

class ListEngineeringDateController extends GetxController {
  Api api;
  var appState = Rx<AppState>();

  ListEngineeringDateController(this.api);

  final TextEditingController searchController = TextEditingController();
  RxList<EngineeringDateDTO> listEngineeringDate = RxList<EngineeringDateDTO>();
  List<EngineeringDateDTO> backupListEngineeringDate = List<EngineeringDateDTO>();

  RxString status = RxString();

  bool isDrive() {
    final userProfile = DataCenter.shared().getUserInfo();
    List<String> _roles = userProfile.roleCode != null ? userProfile.roleCode.split(";") : [];
    for (var roleCode in _roles) {
      if (roleCode == Constants.LAIXE){
        return true;
      }
    }
    return false;
  }

  void search(String value) {
    listEngineeringDate.clear();
    listEngineeringDate.addAll(backupListEngineeringDate
        .where((element) =>
            StringUtils.removeDiacritics(element.driverCode)
                .toLowerCase()
                .contains(StringUtils.removeDiacritics(value.trim()).toLowerCase()) ||
            (element.licenseCar != null &&
                StringUtils.removeDiacritics(element.licenseCar)
                    .toLowerCase()
                    .contains(StringUtils.removeDiacritics(value.trim()).toLowerCase())))
        .toList());
  }

  fetchData() async {
    try {
      appState.value = AppState.LOADING;
      final response = await api.getListEngineeringDate();
      final result = BaseApiResponse.fromJson(response);
      if (result.success) {
        final data = EngineeringDateResponse.fromJson(result.data);
        if (data.resultInfo.status == RESULT_OK) {
          backupListEngineeringDate = data.listTechnicalDTO;
          listEngineeringDate.clear();
          listEngineeringDate.addAll(data.listTechnicalDTO);
          appState.value = AppState.DONE;
        } else {
          appState.value = AppState.ERROR;
        }
      } else {
        appState.value = AppState.ERROR;
      }
    } catch (e) {
      appState.value = AppState.ERROR;
    }
  }

  updateStatusTechnicalDate(EngineeringDateDTO engineeringDateDTO, int status) async {
    try {
      showLoadingDialog();
      appState.value = AppState.LOADING;
      final response = await api.updateStatusEngineeringDate(EngineeringDateRequest(
          technicalDateDTO: EngineeringDateDTO(
              technicalDateId: engineeringDateDTO.technicalDateId,
              sysUserId: DataCenter.shared().getUserInfo().sysUserId,
              status: status)));
      final result = BaseApiResponse.fromJson(response);
      if (result.success) {
        dismissLoadingDialog();
        final data = EngineeringDateResponse.fromJson(result.data);
        if (data.resultInfo.status == RESULT_OK) {
          Get.snackbar("OK", data.resultInfo.message, backgroundColor: Colors.white);
          fetchData();
          appState.value = AppState.DONE;
        } else {
          appState.value = AppState.ERROR;
        }
      } else {
        dismissLoadingDialog();
        appState.value = AppState.ERROR;
      }
    } catch (e) {
      dismissLoadingDialog();
      appState.value = AppState.ERROR;
    }
  }
}
