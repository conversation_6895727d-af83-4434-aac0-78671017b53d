import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:trackcarvcc/constants/constants.dart';
import 'package:trackcarvcc/helpers/extensions.dart';
import 'package:trackcarvcc/models/models.dart';
import 'package:trackcarvcc/repository/api/api.dart';
import 'package:trackcarvcc/repository/preferences/data_center.dart';
import 'package:trackcarvcc/ui/engineering_date/model/engineering_date_request.dart';
import 'package:trackcarvcc/ui/engineering_date/model/engineering_date_response.dart';
import 'package:trackcarvcc/ui/images/image_info.dart';

class CreateEngineeringDateController extends GetxController {
  Api api;
  var appState = Rx<AppState>();

  CreateEngineeringDateController(this.api);

  final TextEditingController searchController = TextEditingController();
  final TextEditingController proposeController = TextEditingController();
  RxList<EngineeringDateDTO> listLicenseCar = RxList<EngineeringDateDTO>();
  Rx<EngineeringDateDTO> licenseCarSelect = Rx<EngineeringDateDTO>();
  RxList<ImageInformation> listImageType1 = RxList<ImageInformation>();
  RxList<ImageInformation> listImageType2 = RxList<ImageInformation>();
  RxList<ImageInformation> listImageType3 = RxList<ImageInformation>();
  RxList<ImageInformation> listImageType4 = RxList<ImageInformation>();
  RxList<ImageInformation> listImageType5 = RxList<ImageInformation>();
  List<ListTechnicalDetailDTO> listTechnicalDetailDTO = List<ListTechnicalDetailDTO>();

  bool isCarCaptain() {
    final userProfile = DataCenter.shared().getUserInfo();
    List<String> _roles = userProfile.roleCode != null ? userProfile.roleCode.split(";") : [];
    for (var roleCode in _roles) {
      if (roleCode == Constants.DOITRUONGXE) {
        return true;
      }
    }
    return false;
  }

  fetchData() async {
    try {
      appState.value = AppState.LOADING;
      final response = await api.getListLicenseCar();
      final result = BaseApiResponse.fromJson(response);
      if (result.success) {
        final data = EngineeringDateResponse.fromJson(result.data);
        if (data.resultInfo.status == RESULT_OK) {
          listLicenseCar = RxList<EngineeringDateDTO>.from(data.listLicenseCar);

          appState.value = AppState.DONE;
        } else {
          appState.value = AppState.ERROR;
        }
      } else {
        appState.value = AppState.ERROR;
      }
    } catch (e) {
      appState.value = AppState.ERROR;
    }
  }

  getEngineeringDateDetail(int technicalDateId) async {
    try {
      appState.value = AppState.LOADING;
      final response = await api.getEngineeringDateDetail(technicalDateId);
      final result = BaseApiResponse.fromJson(response);
      if (result.success) {
        final data = EngineeringDateResponse.fromJson(result.data);
        if (data.resultInfo.status == RESULT_OK) {
          listTechnicalDetailDTO = data.listTechnicalDetailDTO;
          data.listTechnicalDetailDTO.forEach((element) {
            if (element.type == 1) {
              listImageType1.addAll(element.listImageInformation);
            } else if (element.type == 2) {
              listImageType2.addAll(element.listImageInformation);
            } else if (element.type == 3) {
              listImageType3.addAll(element.listImageInformation);
            } else if (element.type == 4) {
              listImageType4.addAll(element.listImageInformation);
            } else if (element.type == 5) {
              listImageType5.addAll(element.listImageInformation);
            }
          });

          appState.value = AppState.DONE;
        } else {
          appState.value = AppState.ERROR;
        }
      } else {
        appState.value = AppState.ERROR;
      }
    } catch (e) {
      appState.value = AppState.ERROR;
    }
  }

  request(EngineeringDateDTO engineeringDateDTO) {
    if (engineeringDateDTO != null) {
      listTechnicalDetailDTO.forEach((element) {
        if (element.type == 1) {
          element.listImageInformation = listImageType1;
        } else if (element.type == 2) {
          element.listImageInformation = listImageType2;
        } else if (element.type == 3) {
          element.listImageInformation = listImageType3;
        } else if (element.type == 4) {
          element.listImageInformation = listImageType4;
        } else if (element.type == 5) {
          element.listImageInformation = listImageType5;
        }
      });
    } else {
      listTechnicalDetailDTO.add(ListTechnicalDetailDTO(type: 1, listImageInformation: listImageType1));
      listTechnicalDetailDTO.add(ListTechnicalDetailDTO(type: 2, listImageInformation: listImageType2));
      listTechnicalDetailDTO.add(ListTechnicalDetailDTO(type: 3, listImageInformation: listImageType3));
      listTechnicalDetailDTO.add(ListTechnicalDetailDTO(type: 4, listImageInformation: listImageType4));
      listTechnicalDetailDTO.add(ListTechnicalDetailDTO(type: 5, listImageInformation: listImageType5));
    }

    return EngineeringDateRequest(
        technicalDateDTO: EngineeringDateDTO(
            createdDate: engineeringDateDTO != null ? engineeringDateDTO.createdDate : null,
            technicalDateId: engineeringDateDTO != null ? engineeringDateDTO.technicalDateId : null,
            sysGroupId: engineeringDateDTO != null
                ? engineeringDateDTO.sysGroupId
                : DataCenter.shared().getUserInfo().sysGroupId,
            sysGroupName: engineeringDateDTO != null
                ? engineeringDateDTO.sysGroupName
                : DataCenter.shared().getUserInfo().sysGroupName,
            carId: licenseCarSelect.value.carId,
            licenseCar: licenseCarSelect.value.licenseCar,
            sysUserId: DataCenter.shared().getUserInfo().sysUserId,
            createdBy: engineeringDateDTO != null ? engineeringDateDTO.createdBy : null,
            driverId:
                engineeringDateDTO != null ? engineeringDateDTO.driverId : DataCenter.shared().getUserInfo().sysUserId,
            driverCode: engineeringDateDTO != null
                ? engineeringDateDTO.driverCode
                : DataCenter.shared().getUserInfo().employeeCode,
            employeeCode: engineeringDateDTO != null ? null : DataCenter.shared().getUserInfo().employeeCode,
            fullName: engineeringDateDTO != null ? null : DataCenter.shared().getUserInfo().fullName,
            propose: proposeController.text ?? ""),
        listTechnicalDetailDTO: listTechnicalDetailDTO);
  }

  bool validate() {
    if (licenseCarSelect.value == null) {
      HapticFeedback.mediumImpact();
      Get.snackbar(":(((", "Vui lòng chọn biển kiểm soát", backgroundColor: Colors.white);
      return false;
    }
    if (listImageType1.length == 0 ||
        listImageType2.length == 0 ||
        listImageType3.length == 0 ||
        listImageType4.length == 0) {
      HapticFeedback.mediumImpact();
      Get.snackbar(":(((", "Với Loại Gầm, Máy, Vỏ, Nội thất bắt buộc chụp tối thiểu 1 ảnh ",
          backgroundColor: Colors.white);
      return false;
    }
    return true;
  }

  createEngineeringDate() async {
    try {
      showLoadingDialog();
      appState.value = AppState.LOADING;
      final response = await api.createEngineeringDate(request(null));
      final result = BaseApiResponse.fromJson(response);
      if (result.success) {
        final data = EngineeringDateResponse.fromJson(result.data);
        dismissLoadingDialog();
        if (data.resultInfo.status == RESULT_OK) {
          Get.back();
          Get.snackbar("OK", "Tạo phiếu thành công!", backgroundColor: Colors.white);
          appState.value = AppState.DONE;
        } else {
          Get.snackbar("NOK", data.resultInfo.message ?? "Có lỗi xảy ra!", backgroundColor: Colors.white);
          appState.value = AppState.ERROR;
        }
      } else {
        appState.value = AppState.ERROR;
        dismissLoadingDialog();
      }
    } catch (e) {
      appState.value = AppState.ERROR;
      dismissLoadingDialog();
    }
  }

  editEngineeringDate(EngineeringDateDTO engineeringDateDTO) async {
    try {
      showLoadingDialog();
      appState.value = AppState.LOADING;
      final response = await api.editEngineeringDate(request(engineeringDateDTO));
      final result = BaseApiResponse.fromJson(response);
      if (result.success) {
        final data = EngineeringDateResponse.fromJson(result.data);
        dismissLoadingDialog();
        if (data.resultInfo.status == RESULT_OK) {
          Get.back();
          Get.snackbar("OK", "Cập nhật thành công!", backgroundColor: Colors.white);
          appState.value = AppState.DONE;
        } else {
          appState.value = AppState.ERROR;
        }
      } else {
        appState.value = AppState.ERROR;
        dismissLoadingDialog();
      }
    } catch (e) {
      appState.value = AppState.ERROR;
      dismissLoadingDialog();
    }
  }

  updateStatusTechnicalDate(EngineeringDateDTO engineeringDateDTO, int status, String value) async {
    try {
      showLoadingDialog();
      appState.value = AppState.LOADING;
      final response = await api.updateStatusEngineeringDate(EngineeringDateRequest(
          technicalDateDTO: EngineeringDateDTO(
              technicalDateId: engineeringDateDTO.technicalDateId,
              sysUserId: DataCenter.shared().getUserInfo().sysUserId,
              status: status,
              rejectReson: value)));
      final result = BaseApiResponse.fromJson(response);
      if (result.success) {
        dismissLoadingDialog();
        final data = EngineeringDateResponse.fromJson(result.data);
        if (data.resultInfo.status == RESULT_OK) {
          Get.back();
          Get.snackbar("OK", data.resultInfo.message, backgroundColor: Colors.white);
          appState.value = AppState.DONE;
        } else {
          appState.value = AppState.ERROR;
        }
      } else {
        dismissLoadingDialog();
        appState.value = AppState.ERROR;
      }
    } catch (e) {
      dismissLoadingDialog();
      appState.value = AppState.ERROR;
    }
  }
}
