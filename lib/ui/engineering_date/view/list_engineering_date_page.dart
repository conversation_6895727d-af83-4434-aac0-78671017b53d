import 'dart:ui';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:trackcarvcc/constants/app_state.dart';
import 'package:trackcarvcc/constants/style/style.dart';
import 'package:trackcarvcc/helpers/extensions.dart';
import 'package:trackcarvcc/repository/preferences/data_center.dart';
import 'package:trackcarvcc/routes/routes.dart';
import 'package:trackcarvcc/ui/engineering_date/controller/list_engineering_date_controller.dart';
import 'package:trackcarvcc/ui/login/widget/custom_text_form_field.dart';
import 'package:trackcarvcc/widget/circle_loading.dart';

class ListOrderRequestPage extends GetView<ListEngineeringDateController> {
  @override
  Widget build(BuildContext context) {
    // TODO: implement build
    return GetBuilder(initState: (_) {
      controller.fetchData();
    }, builder: (_) {
      return Scaffold(
          backgroundColor: Color(0xFFF5F5F5),
          appBar: AppBar(
              title: Text('<PERSON><PERSON><PERSON> kỹ thuật'),
              leading: Icon<PERSON>utton(
                icon: const Icon(
                  Icons.menu,
                  color: Colors.white,
                ),
                onPressed: () {
                  Scaffold.of(context).openDrawer();
                },
              ),
              centerTitle: true,
              backgroundColor: Color(0xFFEE0033),
              actions: [_buildFilter()]),
          body: Column(
            children: [
              //Search bar
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: CustomTextFormField(
                  controller: controller.searchController,
                  hintText: "Tìm kiếm",
                  prefixIcon: Icon(Icons.search),
                  onChanged: controller.search,
                ),
              ),
              Expanded(
                child: Obx(() {
                  if (controller.appState() == AppState.LOADING) {
                    return _buildLoading();
                  }

                  if (controller.appState() == AppState.ERROR) {
                    return _buildErrorWidget();
                  }
                  if (controller.listEngineeringDate.isEmpty) {
                    return _buildNoDataWidget();
                  } else {
                    return ListView.builder(
                      itemCount: controller.listEngineeringDate.length,
                      padding: EdgeInsets.symmetric(horizontal: 5),
                      itemBuilder: (context, index) {
                        return buildItem(index, context);
                      },
                    );
                  }
                }),
              ),
            ],
          ),
          floatingActionButton: FloatingActionButton(
            backgroundColor: Color(0xFFEE0033),
            child: Icon(Icons.add, color: Colors.white),
            onPressed: () {
              Get.toNamed(Routers.create_engineering_date_page).whenComplete(() => controller.fetchData());
            },
          ));
    });
  }

  buildItem(int index, BuildContext context) {
    String status = "";
    Color colorStatus = Color(0xFFEE0033);
    switch (controller.listEngineeringDate[index].status) {
      case 1:
        status = "Mới tạo";
        colorStatus = Color(0xFF4F7FFA);
        break;
      case 2:
        status = "Chờ duyệt";
        colorStatus = Color(0xFFF2994A);
        break;
      case 3:
        status = "Đã duyệt";
        colorStatus = Color(0xFF00C851);
        break;
      case 4:
        status = "Đã từ chối";
        colorStatus = Color(0xFFA8ABAD);
        break;
      case 0:
        status = "Đã hủy";
        colorStatus = Color(0xFFEE0033);
        break;
      default:
        status = "Chưa xác định";
        colorStatus = Color(0xFFEE0033);
        break;
    }
    return GestureDetector(
      onTap: () {
        Get.toNamed(Routers.create_engineering_date_page, arguments: controller.listEngineeringDate[index])
            .whenComplete(() => controller.fetchData());
      },
      child: Card(
        elevation: 5,
        child: Container(
          margin: EdgeInsets.all(10),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Row(
                children: <Widget>[
                  Expanded(
                      child: Text("${index + 1}. Đơn vị: ${controller.listEngineeringDate[index].sysGroupName}",
                          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 13))),
                  controller.listEngineeringDate[index].status == 1
                      ? GestureDetector(
                          onTap: () {
                            showConfirmDialog(context, 'Bạn có chắc chắn muốn xóa ngày kỹ thuật?', yesCallBack: () {
                              controller.updateStatusTechnicalDate(controller.listEngineeringDate[index], 0);
                            });
                          },
                          child: Icon(Icons.clear))
                      : SizedBox()
                ],
              ),
              SizedBox(height: 10),
              Text("Biển kiểm soát: ${controller.listEngineeringDate[index].licenseCar}"),
              SizedBox(height: 5),
              Text("Lái xe: ${controller.listEngineeringDate[index].driverName}"),
              SizedBox(height: 5),
              Text("Ngày tạo: ${controller.listEngineeringDate[index].createdDate ?? ""}"),
              SizedBox(height: 5),
              Obx(() => Row(children: <Widget>[
                    Expanded(child: Text("Trạng thái: $status", style: TextStyle(color: colorStatus))),
                    Visibility(
                        visible: controller.listEngineeringDate[index].status == 1 &&
                            controller.listEngineeringDate[index].createdBy ==
                                DataCenter.shared().getUserInfo().sysUserId,
                        child: GestureDetector(
                            onTap: () {
                              controller.updateStatusTechnicalDate(controller.listEngineeringDate[index], 2);
                            },
                            child: Icon(Icons.send, color: Colors.green)))
                  ])),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLoading() => Container(
        color: AppThemes.colorViettelGray3,
        child: LoadingCircle(),
      );

  Widget _buildNoDataWidget() => Center(
        child: Text("Không có dữ liệu!"),
      );

  Widget _buildErrorWidget() => Center(
        child: Text("Có lỗi xảy ra. Vui lòng thử lại sau."),
      );

  _buildFilter() {
    return PopupMenuButton(
        icon: Icon(Icons.filter_alt_sharp, color: Colors.white),
        onSelected: (value) {
          controller.listEngineeringDate.clear();
          switch (value) {
            case -1:
              controller.listEngineeringDate.addAll(controller.backupListEngineeringDate);
              break;
            case 1:
              controller.listEngineeringDate
                  .addAll(controller.backupListEngineeringDate.where((order) => order.status == 1));
              break;
            case 2:
              controller.listEngineeringDate
                  .addAll(controller.backupListEngineeringDate.where((order) => order.status == 2));
              break;
            case 3:
              controller.listEngineeringDate
                  .addAll(controller.backupListEngineeringDate.where((order) => order.status == 3));
              break;
            case 4:
              controller.listEngineeringDate
                  .addAll(controller.backupListEngineeringDate.where((order) => order.status == 4));
              break;
            case 0:
              controller.listEngineeringDate
                  .addAll(controller.backupListEngineeringDate.where((order) => order.status == 0));
              break;
          }
        },
        itemBuilder: (context) => [
              PopupMenuItem(child: Text("Tất cả", style: TextStyle(fontSize: 14, color: Colors.black)), value: -1),
              PopupMenuItem(child: Text("Mới tạo", style: TextStyle(fontSize: 14, color: Colors.black)), value: 1),
              PopupMenuItem(child: Text("Chờ duyệt", style: TextStyle(fontSize: 14, color: Colors.black)), value: 2),
              PopupMenuItem(child: Text("Đã duyệt", style: TextStyle(fontSize: 14, color: Colors.black)), value: 3),
              PopupMenuItem(child: Text("Đã từ chối", style: TextStyle(fontSize: 14, color: Colors.black)), value: 4),
              PopupMenuItem(child: Text("Đã hủy", style: TextStyle(fontSize: 14, color: Colors.black)), value: 0),
            ]);
  }
}
