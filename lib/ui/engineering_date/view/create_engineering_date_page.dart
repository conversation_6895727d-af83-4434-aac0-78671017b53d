import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:trackcarvcc/constants/app_state.dart';
import 'package:trackcarvcc/constants/style/style.dart';
import 'package:trackcarvcc/controllers/order_car_creation_controller.dart';
import 'package:trackcarvcc/helpers/extensions.dart';
import 'package:trackcarvcc/repository/preferences/data_center.dart';
import 'package:trackcarvcc/ui/engineering_date/controller/create_engineering_date_controller.dart';
import 'package:trackcarvcc/ui/engineering_date/model/engineering_date_response.dart';
import 'package:trackcarvcc/ui/images/images_horizontal.dart';
import 'package:trackcarvcc/widget/circle_loading.dart';
import 'package:trackcarvcc/widget/common_button.dart';

class CreateEngineeringDatePage extends GetView<CreateEngineeringDateController> {
  final EngineeringDateDTO engineeringDateDTO;

  CreateEngineeringDatePage({this.engineeringDateDTO});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<CarOrderCreationController>(initState: (_) {
      if (engineeringDateDTO == null) {
        controller.fetchData();
      } else {
        controller.proposeController.text = engineeringDateDTO.propose;
        controller.getEngineeringDateDetail(engineeringDateDTO.technicalDateId);
      }
    }, builder: (_) {
      return Scaffold(
        appBar: AppBar(
          backgroundColor: AppThemes.colorViettelRed,
          brightness: Brightness.light,
          elevation: 1.0,
          leading: IconButton(
            icon: Icon(
              Icons.arrow_back,
              color: Colors.white,
            ),
            onPressed: () {
              Get.back();
            },
          ),
          centerTitle: true,
          title: Text(
            'Ngày kỹ thuật',
            style: CommonTextStyle.textStyleFontLatoLargeBoldWhite,
          ),
        ),
        backgroundColor: Colors.white,
        body: Obx(() {
          if (controller.appState() == AppState.LOADING) {
            return _buildLoading();
          }

          if (controller.appState() == AppState.ERROR) {
            return _buildErrorWidget();
          }
          return _body(context);
        }),
      );
    });
  }

  _body(BuildContext context) => GestureDetector(
        onTap: () {
          FocusScope.of(context).requestFocus(FocusNode());
        },
        child: Container(
          padding: const EdgeInsets.only(left: mPaddingLarge, right: mPaddingLarge, bottom: mPaddingLarge),
          child: Stack(
            children: [
              SingleChildScrollView(
                child: Column(
                  children: [
                    SizedBox(height: mPaddingLarge),
                    _userCreationInfo(),
                    SizedBox(height: mPaddingXXLarge),
                    _carInfo(),
                    SizedBox(height: mPaddingXXLarge * 2.5),
                  ],
                ),
              ),
              controller.isCarCaptain() && engineeringDateDTO != null && engineeringDateDTO.status == 2
                  ? _btnCarCaptain(context)
                  : _buttonSave(context)
            ],
          ),
        ),
      );

  _buttonSave(BuildContext context) => engineeringDateDTO != null && engineeringDateDTO.status == 1
      ? Positioned(
          bottom: 0,
          left: 0,
          right: 0,
          child: Padding(
            padding: const EdgeInsets.only(top: mPaddingXLarge),
            child: CommonButton(
              title: "Cập nhật",
              textColor: Colors.white,
              onButtonClick: () {
                FocusScope.of(context).requestFocus(FocusNode());
                if (controller.validate()) {
                  controller.editEngineeringDate(engineeringDateDTO);
                }
              },
            ),
          ),
        )
      : engineeringDateDTO == null
          ? Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: Padding(
                padding: const EdgeInsets.only(top: mPaddingXLarge),
                child: CommonButton(
                  title: "Lưu lại",
                  textColor: Colors.white,
                  onButtonClick: () {
                    FocusScope.of(context).requestFocus(FocusNode());
                    if (controller.validate()) {
                      controller.createEngineeringDate();
                    }
                  },
                ),
              ),
            )
          : SizedBox();

  _btnCarCaptain(BuildContext context) => Positioned(
        bottom: 0,
        left: 0,
        right: 0,
        child: Row(
          children: <Widget>[
            Expanded(
                child: CommonButton(
              title: "Từ chối",
              textColor: Colors.white,
              onButtonClick: () {
                FocusScope.of(context).requestFocus(FocusNode());
                showInputConfirmDialog(Get.context, 'Từ chối', 'Lý do từ chối', yesCallBack: (value) {
                  if (value != null) {
                    Get.back();
                    controller.updateStatusTechnicalDate(engineeringDateDTO, 4, value);
                  } else {
                    Get.snackbar(":((", "Bạn chưa nhập lý do từ chối!", backgroundColor: Colors.white);
                  }
                });
              },
            )),
            SizedBox(width: 5),
            Expanded(
                child: CommonButton(
              title: "Phê duyệt",
              textColor: Colors.white,
              onButtonClick: () {
                FocusScope.of(context).requestFocus(FocusNode());
                controller.updateStatusTechnicalDate(engineeringDateDTO, 3, null);
              },
            ))
          ],
        ),
      );

  _userCreationInfo() {
    if (engineeringDateDTO != null) {
      controller.licenseCarSelect.value =
          EngineeringDateDTO(licenseCar: engineeringDateDTO.licenseCar, carId: engineeringDateDTO.carId);
    }
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _title(title: 'Thông tin người tạo'),
        _itemUserCreationInfo(
            label: 'Đơn vị',
            userInfo: engineeringDateDTO != null
                ? engineeringDateDTO.sysGroupName ?? ""
                : DataCenter.shared().getUserInfo().sysGroupName),
        _itemUserCreationInfo(
            label: 'Họ tên',
            userInfo: engineeringDateDTO != null
                ? engineeringDateDTO.driverName ?? ""
                : DataCenter.shared().getUserInfo().fullName),
        _itemUserCreationInfo(
            label: 'Mã nhân viên',
            userInfo: engineeringDateDTO != null
                ? engineeringDateDTO.driverCode ?? ""
                : DataCenter.shared().getUserInfo().employeeCode),
        Text("Chọn biển kiểm soát", style: TextStyle(color: Colors.black)),
        Container(
          height: 50,
          padding: const EdgeInsets.only(left: mPadding),
          alignment: Alignment.center,
          decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: const BorderRadius.all(
                Radius.circular(mRadiusSmall),
              ),
              border: Border.all(color: AppThemes.colorViettelGray2)),
          child: engineeringDateDTO != null
              ? Text(controller.licenseCarSelect.value.licenseCar ?? "")
              : Obx(() => DropdownButton<EngineeringDateDTO>(
                    isExpanded: true,
                    elevation: 20,
                    dropdownColor: AppThemes.colorViettelGray3,
                    focusColor: AppThemes.colorViettelRed02,
                    value: controller.licenseCarSelect.value,
                    underline: const SizedBox(),
                    items: controller.listLicenseCar.map((EngineeringDateDTO engineering) {
                      return DropdownMenuItem<EngineeringDateDTO>(
                        value: engineering,
                        child: Text(
                          engineering.licenseCar,
                        ),
                      );
                    }).toList(),
                    onChanged: (value) {
                      controller.licenseCarSelect.value = value;
                    },
                  )),
        ),
      ],
    );
  }

  _carInfo() => Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SelectImages(
              title: "1. Gầm",
              listImage: controller.listImageType1,
              maxImage: 3,
              isDeleteImage:
                  engineeringDateDTO == null || engineeringDateDTO != null && engineeringDateDTO.status == 1),
          SelectImages(
              title: "2. Máy",
              listImage: controller.listImageType2,
              maxImage: 3,
              isDeleteImage:
                  engineeringDateDTO == null || engineeringDateDTO != null && engineeringDateDTO.status == 1),
          SelectImages(
              title: "3. Vỏ",
              listImage: controller.listImageType3,
              maxImage: 3,
              isDeleteImage:
                  engineeringDateDTO == null || engineeringDateDTO != null && engineeringDateDTO.status == 1),
          SelectImages(
              title: "4. Nội thất",
              listImage: controller.listImageType4,
              maxImage: 3,
              isDeleteImage:
                  engineeringDateDTO == null || engineeringDateDTO != null && engineeringDateDTO.status == 1),
          SelectImages(
              title: "5. Khác",
              listImage: controller.listImageType5,
              isDeleteImage:
                  engineeringDateDTO == null || engineeringDateDTO != null && engineeringDateDTO.status == 1),
          Text("Đề xuất", style: TextStyle(color: Colors.black)),
          Container(
            decoration: BoxDecoration(
              color: AppThemes.colorViettelGray3,
              borderRadius: const BorderRadius.all(
                Radius.circular(mRadiusSmall),
              ),
              border: Border.all(color: Colors.transparent),
            ),
            child: TextField(
              controller: controller.proposeController,
              enabled: true,
              readOnly: false,
              maxLines: 5,
              decoration: InputDecoration(
                hintText: 'Nhập nội dung đề xuất',
                border: InputBorder.none,
                contentPadding: const EdgeInsets.all(mPadding),
                hintStyle: CommonTextStyle.textStyleFontLatoNormalHint,
                isDense: true,
              ),
              autofocus: false,
              style: CommonTextStyle.textStyleFontLatoNormal,
            ),
          )
        ],
      );

  _itemUserCreationInfo({String label, String userInfo, VoidCallback onClick, Color userInfoColor}) => InkResponse(
        onTap: () {
          if (onClick != null) {
            onClick();
          }
        },
        child: Column(
          children: [
            Padding(
              padding: const EdgeInsets.only(top: mPaddingXMedium, bottom: mPaddingXMedium),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    label,
                    style: CommonTextStyle.textStyleFontLatoNormal,
                  ),
                  Flexible(
                    child: Text(
                      userInfo,
                      maxLines: 2,
                      textAlign: TextAlign.right,
                      style: userInfoColor != null
                          ? CommonTextStyle.textStyleFontLatoNormalBold.copyWith(color: userInfoColor)
                          : CommonTextStyle.textStyleFontLatoNormalBold,
                    ),
                  )
                ],
              ),
            ),
            Divider(
              height: 1,
              thickness: 1,
              color: AppThemes.colorViettelGray3,
            )
          ],
        ),
      );

  _title({String title}) => Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(title, style: CommonTextStyle.textStyleFontLatoRedNormal),
          const SizedBox(height: mPaddingXSmall),
          Divider(
            height: 1,
            thickness: 1,
            color: AppThemes.colorViettelRed,
          )
        ],
      );

  Widget _buildLoading() => Container(
        color: Colors.white,
        child: LoadingCircle(),
      );

  Widget _buildErrorWidget() => Center(
        child: Text("Có lỗi xảy ra. Vui lòng thử lại sau."),
      );
}
