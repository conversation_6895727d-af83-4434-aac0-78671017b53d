import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:trackcarvcc/constants/constants.dart';
import 'package:trackcarvcc/constants/style/style.dart';
import 'package:trackcarvcc/controllers/order_car_creation_controller.dart';
import 'package:trackcarvcc/models/models.dart';
import 'package:trackcarvcc/repository/api/urls.dart';
import 'package:trackcarvcc/ui/images/images_horizontal.dart';
import 'package:trackcarvcc/ui/images/import_file_bottomsheet.dart';
import 'package:trackcarvcc/widget/common_button.dart';
import 'widget/item_car_and_driver_info_widget.dart';
import 'widget/item_car_type_widget.dart';
import 'widget/item_choose_transition_type_widget.dart';
import 'widget/item_destination_point_widget.dart';
import 'widget/item_expect_distance_widget.dart';
import 'widget/item_file_attachment.dart';
import 'widget/item_finish_time_widget.dart';
import 'widget/item_job_description_widget.dart';
import 'widget/item_next_user_accept_widget.dart';
import 'widget/item_start_point_widget.dart';
import 'widget/item_start_time_widget.dart';
import 'widget/item_user_accept.dart';
import 'widget/item_weight_widget.dart';
import 'widget/user_go_with_widget.dart';
import 'package:trackcarvcc/helpers/extensions.dart';
import 'widget/item_serve_field_widget.dart';
import 'widget/item_planned_route.dart';

enum PageType { CREATE_ORDER, UPDATE, COPY_ORDER }

// ignore: must_be_immutable
class CarOrderCreationPage extends GetView<CarOrderCreationController> {
  final TextEditingController _textTimeStartDialogController =
      TextEditingController(text: Constants.TIMER_DEFAULT);
  final TextEditingController _textTimeFinishDialogController =
      TextEditingController(text: Constants.TIMER_END_DEFAULT);

  final PageType pageType;

  LstBookCarDto _bookCarDto;

  CarOrderCreationPage({this.pageType});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<CarOrderCreationController>(initState: (_) {
      // controller
      controller.clear();
      if (pageType == PageType.UPDATE || pageType == PageType.COPY_ORDER) {
        _bookCarDto = Get.arguments[Constants.KEY_BOOK_CAR];
        controller.bookCarDtoObj = _bookCarDto;
        WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
          controller.syncBookCarDtoInfo(_bookCarDto.bookCarId);
          controller.getListAttachment(_bookCarDto.bookCarId);
        });
        print(
            'aaaa: ${_bookCarDto.status} - ${_bookCarDto.fullName} - ${_bookCarDto.estimateDistance} - ${_bookCarDto.estimateTime}');

        controller.synServeFieldDtoChosen(_bookCarDto.serveField);
      } else {
        controller.init();
        WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
          controller.fetchUserInfoAndPrepareInfo();
        });
      }

      controller.checkPermissions();
    }, builder: (_) {
      return Scaffold(
        appBar: AppBar(
          backgroundColor: AppThemes.colorViettelRed,
          brightness: Brightness.light,
          elevation: 1.0,
          leading: IconButton(
            icon: Icon(
              pageType == PageType.CREATE_ORDER ? Icons.menu : Icons.arrow_back,
              color: Colors.white,
            ),
            onPressed: () {
              if (pageType == PageType.CREATE_ORDER) {
                Scaffold.of(context).openDrawer();
              } else {
                Get.back();
              }
            },
          ),
          centerTitle: true,
          title: Text(
            pageType == PageType.CREATE_ORDER || pageType == PageType.COPY_ORDER
                ? 'Lập phiếu đặt xe'
                : 'Cập nhật thông tin phiếu đặt xe',
            style: CommonTextStyle.textStyleFontLatoLargeBoldWhite,
          ),
        ),
        backgroundColor: Colors.white,
        body: _body(context),
      );
    });
  }

  ///
  /// PARENT BODY
  ///
  _body(BuildContext context) => GestureDetector(
        onTap: () {
          FocusScope.of(context).requestFocus(FocusNode());
        },
        child: Container(
          padding: const EdgeInsets.only(
              left: mPaddingLarge, right: mPaddingLarge, bottom: mPaddingLarge),
          child: Stack(
            children: [
              SingleChildScrollView(
                child: Column(
                  children: [
                    const SizedBox(
                      height: mPaddingLarge,
                    ),
                    _userCreationInfo(),
                    const SizedBox(
                      height: mPaddingXXLarge,
                    ),
                    _carOrderCreationInfo(context),
                    const SizedBox(
                      height: mPaddingXXLarge * 2.5,
                    ),
                  ],
                ),
              ),
              _buttonSave(context)
            ],
          ),
        ),
      );

  /// ==============================
  /// User creation info
  _userCreationInfo() => Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _title(title: 'Thông tin người tạo'),
          Obx(() => _itemUserCreationInfo(
              label: 'Đơn vị', userInfo: controller.userUnit.value)),
          Obx(() => _itemUserCreationInfo(
              label: 'Họ tên', userInfo: controller.userName.value)),
          Obx(() => _itemUserCreationInfo(
              label: 'Email', userInfo: controller.userEmail.value)),
          Obx(() => _itemUserCreationInfo(
              label: 'Số điện thoại',
              userInfo: controller.userPhone.value ?? '',
              userInfoColor: AppThemes.colorViettelRed,
              onClick: () {
                controller.makeAction('tel:${controller.userPhone.value}');
              }))
        ],
      );

  _itemUserCreationInfo(
          {String label,
          String userInfo,
          VoidCallback onClick,
          Color userInfoColor}) =>
      InkResponse(
        onTap: () {
          if (onClick != null) {
            onClick();
          }
        },
        child: Column(
          children: [
            Padding(
              padding: const EdgeInsets.only(
                  top: mPaddingXMedium, bottom: mPaddingXMedium),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    label,
                    style: CommonTextStyle.textStyleFontLatoNormal,
                  ),
                  Flexible(
                    child: Text(
                      userInfo,
                      maxLines: 2,
                      textAlign: TextAlign.right,
                      style: userInfoColor != null
                          ? CommonTextStyle.textStyleFontLatoNormalBold
                              .copyWith(color: userInfoColor)
                          : CommonTextStyle.textStyleFontLatoNormalBold,
                    ),
                  )
                ],
              ),
            ),
            Divider(
              height: 1,
              thickness: 1,
              color: AppThemes.colorViettelGray3,
            )
          ],
        ),
      );

  /// ==============================
  /// car order creation info
  ///
  _carOrderCreationInfo(BuildContext context) => Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _title(title: 'Thông tin đặt xe'),
          _itemStartPoint(),
          _itemDestinationPoint(),
          _itemFileAttachment(),
          _plannedRoute(),
          _itemServeField(),
          _itemExpectDistance(),
          _itemTimeRange(),
          _jobDescription(),
          _itemChooseTransitionType(),
          _itemChooseCarAndDriver(),
          _itemCarType(),
          _itemWeight(),
          _itemGoWith(),
          _itemUserAccept(),
          _itemNextUserAccept()
        ],
      );

  _itemStartPoint() => Obx(
        () => ItemStartPoint(
          isValidate: controller.startPointIsValid,
          textController: controller.startPointController,
          onChanged: controller.changeStartPoint,
        ),
      );

  _itemDestinationPoint() => ItemDestinationPoint();

  _itemFileAttachment() => ItemFileAttachment();

  _itemExpectDistance() => Obx(() => Offstage(
      offstage: !controller.visibilityOfExpectDistance.value,
      child: ExpectDistance()));

  _itemTimeRange() => Container(
        width: double.infinity,
        padding: const EdgeInsets.only(top: mPaddingLarge),
        child: Row(
          children: [
            _itemStartTime(),
            const SizedBox(
              width: mPaddingLarge,
            ),
            _itemFinishTime()
          ],
        ),
      );

  _itemStartTime() => Obx(
        () => ItemStartTime(
          controller: controller.timeStartController,
          timeController: _textTimeStartDialogController,
          isValidate: controller.timeFromToIsValid,
          onChanged: controller.changeTimeStart,
        ),
      );

  _itemFinishTime() => Obx(
        () => ItemFinishTime(
          controller: controller.timeFinishController,
          timeController: _textTimeFinishDialogController,
          isValidate: controller.timeFromToIsValid,
          onChanged: controller.changeTimeFinish,
        ),
      );

  // Noi dung cong viec
  _jobDescription() => Obx(
        () => JobDescription(
          controller: controller.jobDescriptionController,
          isValidate: controller.jobDescriptionIsValid,
          onChanged: controller.changeJobDescription,
        ),
      );

  // chon kieu di
  _itemChooseTransitionType() => Obx(
        () => ItemChooseTransitionType(
          controller: controller.transitionTypeController,
          isValidate: controller.transitionTypeIsValid,
          onChanged: (transitionType) {
            controller.typeBookCarChosen = transitionType.code;
            controller.changeTransitionType(transitionType);
            controller.visibilityOfCarAndDriver.value =
                transitionType.code == 4;
            controller.visibilityOfNextUserAccept.value =
                transitionType.code == 5;
          },
        ),
      );

  _itemCarType() => Obx(
        () => ItemCarType(
          controllerCarType: controller.carTypeController,
          isValidate: controller.carTypeIsValid,
          onChanged: (carItem) {
            controller.changeCarType(carItem.carTypeName, carItem);
          },
        ),
      );

  _plannedRoute() => ItemPlannedRoutedWidget();

  _itemServeField() => Obx(() => ItemServeFieldWidget(
        isValidate: controller.serveFieldDtoValid,
      ));

  _buttonSave(BuildContext context) => Positioned(
        bottom: 0,
        left: 0,
        right: 0,
        child: Padding(
          padding: const EdgeInsets.only(top: mPaddingXLarge),
          child: CommonButton(
            title: pageType == PageType.CREATE_ORDER ||
                    pageType == PageType.COPY_ORDER
                ? 'Đặt xe'
                : 'Cập nhật thông tin',
            textColor: Colors.white,
            onButtonClick: () {
              FocusScope.of(context).requestFocus(FocusNode());
              controller.validate.value = true;
              if (controller.formCarOrderCreationIsValid) {
                // call api create order
                showConfirmDialog(
                    context,
                    pageType == PageType.CREATE_ORDER ||
                            pageType == PageType.COPY_ORDER
                        ? 'Bạn có chắc chắn muốn tạo phiếu?'
                        : 'Bạn có chắc chắn với thông tin vừa cập nhật?',
                    yesCallBack: () {
                  controller
                      .addOrUpdateCarOrderCreation(pageType)
                      .then((isSuccess) => {
                            if (isSuccess)
                              {
                                // navigate to list car order creation
                              }
                          });
                });
              } else {
                showErrorToast(
                    error:
                        'Bạn chưa điền đẩy đủ thông tin \n hoặc thông tin đã nhập chưa đúng!');
              }
            },
          ),
        ),
      );

  /// ==============================
  /// Common widget
  _title({String title}) => Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(title, style: CommonTextStyle.textStyleFontLatoRedNormal),
          const SizedBox(height: mPaddingXSmall),
          Divider(
            height: 1,
            thickness: 1,
            color: AppThemes.colorViettelRed,
          )
        ],
      );

  _itemWeight() => ItemWeight(
        controller: controller.weightController,
      );

  _itemGoWith() => Obx(
        () => UserGoWith(
          isValidate: controller.userGoWithIsValid,
          textController: controller.userGoWithController,
        ),
      );

  _itemUserAccept() => Obx(
        () => ItemUserAccept(
            textController: controller.userAcceptController,
            isValidate: controller.userAcceptIsValid),
      );

  _itemChooseCarAndDriver() => Obx(() => Offstage(
      offstage: !controller.visibilityOfCarAndDriver.value,
      child: CarAndDriver(
        carController: controller.carTransitionTypeController,
        driverController: controller.driverTransitionTypeController,
      )));

  _itemNextUserAccept() => Obx(() => Offstage(
      offstage: !controller.visibilityOfNextUserAccept.value,
      child: NextUserAccept(
        listQLTS: controller.listUserQLTS,
        listUserCarLeader: controller.listUserCarLeader,
        listUserCarCaptain: controller.listUserCarCaptain,
        userQLTSSelected: controller.userQLTSSelected.name,
        userLeaderSelected: controller.userLeaderSelected.name,
        userCaptainSelected: controller.userCaptainSelected.name,
        onChangedQlts: (userQlts) {
          controller.userQLTSSelected = userQlts;
        },
        onChangedCarLeader: (userLeader) {
          controller.userLeaderSelected = userLeader;
        },
        onChangedCarCaptain: (userCaptain) {
          controller.userCaptainSelected = userCaptain;
        },
      )));

  /// ==============================
  /// action
}
