import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:trackcarvcc/constants/style/style.dart';
import 'package:trackcarvcc/controllers/order_car_creation_controller.dart';
import '../../../models/base/lst_serve_field.dart';

class ItemServeFieldWidget extends GetView<CarOrderCreationController> {
  const ItemServeFieldWidget({
    Key key,
    this.isValidate = true,
  }) : super(key: key);

  final bool isValidate;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: mPaddingLarge),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.category,
                color: AppThemes.colorViettelRed,
              ),
              const SizedBox(
                width: mPadding,
              ),
              Text(
                'Phục vụ cho lĩnh vực',
                style: CommonTextStyle.textStyleFontLatoNormal,
              ),
            ],
          ),
          const SizedBox(height: mPadding),
          GestureDetector(
            onTap: () {
              FocusScope.of(context).requestFocus(FocusNode());
              showModalBottomSheet(
                context: context,
                isScrollControlled: true,
                backgroundColor: Colors.transparent,
                builder: (BuildContext context) {
                  return Container(
                    height: MediaQuery.of(context).size.height * 0.6,
                    decoration: const BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.vertical(top: Radius.circular(mRadiusMedium)),
                    ),
                    padding: EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Center(
                          child: Text(
                            'Chọn lĩnh vực',
                            style: CommonTextStyle.textStyleFontLatoLargeBoldBlack,
                          ),
                        ),
                        const SizedBox(height: mPadding),
                        const Divider(
                          color: AppThemes.colorViettelGray3,
                          height: 1,
                        ),
                        const SizedBox(height: mPaddingLarge),
                        Expanded(
                          child: ListView.builder(
                            itemCount: controller.lstServeFieldDto.length,
                            padding: EdgeInsets.symmetric(horizontal: 8),
                            itemBuilder: (context, index) {
                              var item = controller.lstServeFieldDto[index];
                              return InkWell(
                                onTap: () {
                                  controller.addServeFieldDtoChosen(item);
                                  Get.back();
                                },
                                child: Padding(
                                  padding: const EdgeInsets.all(mPadding),
                                  child: Text(
                                    controller.lstServeFieldDto[index].name,
                                    style: CommonTextStyle.textStyleFontLatoNormal,
                                  ),
                                ),
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                  );
                },
              );
            },
            child: Container(
              decoration: BoxDecoration(
                color: AppThemes.colorViettelGray3,
                borderRadius: const BorderRadius.all(
                  Radius.circular(mRadiusSmall),
                ),
                border: Border.all(color: isValidate ? AppThemes.colorViettelGray2 : AppThemes.colorViettelRed),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: _buildListTagForm(),
                  ),
                  Container(
                    width: 50.0,
                    height: 50.0,
                    padding: const EdgeInsets.all(mPadding),
                    margin: const EdgeInsets.only(bottom: 0),
                    //check password field type
                    child: Icon(Icons.keyboard_arrow_down_sharp),
                  )
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  _buildListTagForm() {
    if (controller.listServeFieldDtoChosen == null || controller.listServeFieldDtoChosen.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(mPadding),
        child: Text(
          'Chọn lĩnh vực',
          style: CommonTextStyle.textStyleFontLatoNormalHint,
        ),
      );
    }
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(mPadding),
      child: Wrap(
        spacing: 6.0,
        runSpacing: 6.0,
        direction: Axis.horizontal,
        children: controller.listServeFieldDtoChosen.map((item) => _buildTagItemForm(item.name, item.code)).toList().cast<Widget>(),
      ),
    );
  }

  _buildTagItemForm(String name, int code) {
    return Chip(
      label: Text(
        name,
        style: CommonTextStyle.textStyleFontLatoNormal.copyWith(color: Colors.white),
      ),
      backgroundColor: AppThemes.colorViettelRed,
      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(mRadiusSmall),
      ),
      padding: const EdgeInsets.only(left: 0, right: 0, bottom: 0, top: 0),
      deleteIcon: const Icon(
        Icons.close,
        color: Colors.white,
        size: 20,
      ),
      onDeleted: () {
        _deleteServeFieldDtoChosen(code);
      },
    );
  }

  _deleteServeFieldDtoChosen(int code) {
    controller.deleteServeFieldDtoChosen(code);
  }
}
