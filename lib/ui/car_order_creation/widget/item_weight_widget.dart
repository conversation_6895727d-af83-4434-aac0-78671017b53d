import 'package:flutter/material.dart';
import 'package:trackcarvcc/constants/style/style.dart';

class ItemWeight extends StatelessWidget {
  const ItemWeight({Key key, this.controller}) : super(key: key);

  final TextEditingController controller;
  @override
  Widget build(BuildContext context) {
    return _itemWeight();
  }

  _itemWeight() => Padding(
    padding: const EdgeInsets.only(top: mPaddingLarge),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              Icons.line_weight_sharp,
              color: AppThemes.colorViettelRed,
            ),
            const SizedBox(
              width: mPadding,
            ),
            Text(
              'Trọng lượng (tấn)',
              style: CommonTextStyle.textStyleFontLatoNormal,
            ),
          ],
        ),
        const SizedBox(height: mPadding),
        GestureDetector(
          onTap: () {
          },
          child: Container(
            height: 40.0,
            decoration: BoxDecoration(
              color: AppThemes.colorViettelGray3,
              borderRadius: const BorderRadius.all(
                Radius.circular(mRadiusSmall),
              ),
            ),
            child: TextField(
              controller: controller,
              enabled: true,
              readOnly: false,
              keyboardType: TextInputType.number,
              decoration: InputDecoration(
                hintText: '0.0',
                border: InputBorder.none,
                contentPadding: const EdgeInsets.all(mPadding),
                hintStyle:
                CommonTextStyle.textStyleFontLatoNormalHint,
                isDense: true,
              ),
              autofocus: false,
              style: CommonTextStyle.textStyleFontLatoNormal,
              onChanged: (value) {},
            ),
          ),
        ),
      ],
    ),
  );
}
