import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:trackcarvcc/constants/style/style.dart';
import 'package:trackcarvcc/controllers/order_car_creation_controller.dart';
import 'package:trackcarvcc/models/models.dart';
import 'package:trackcarvcc/ui/car_order_creation/widget/bottom_sheet/search_car_type_widget.dart';

class ItemCarType extends GetView<CarOrderCreationController> {
  const ItemCarType(
      {Key key, this.controllerCarType, this.isValidate = true, this.onChanged})
      : super(key: key);

  final TextEditingController controllerCarType;
  final bool isValidate;
  final ValueChanged<LstBookCarDto> onChanged;

  @override
  Widget build(BuildContext context) {
    return _itemCarType(context);
  }

  /// loai xe
  ///
  _itemCarType(BuildContext context) => Padding(
        padding: const EdgeInsets.only(top: mPaddingLarge),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.directions_car,
                  color: AppThemes.colorViettelRed,
                ),
                const SizedBox(
                  width: mPadding,
                ),
                Text(
                  'Loại xe',
                  style: CommonTextStyle.textStyleFontLatoNormal,
                ),
              ],
            ),
            const SizedBox(height: mPadding),
            GestureDetector(
              onTap: () {
                FocusScope.of(context).requestFocus(FocusNode());
                showModalBottomSheet(
                  context: context,
                  isScrollControlled: true,
                  backgroundColor: Colors.transparent,
                  builder: (BuildContext context) {
                    return SearchCarType(
                      onChanged: (carItem) {
                        controllerCarType.text = carItem.carTypeName;
                        controller.changeWeight(carItem.weight);
                        onChanged(carItem);
                      },
                    );
                  },
                );
              },
              child: Container(
                decoration: BoxDecoration(
                  color: AppThemes.colorViettelGray3,
                  borderRadius: const BorderRadius.all(
                    Radius.circular(mRadiusSmall),
                  ),
                  border: Border.all(
                      color: isValidate
                          ? AppThemes.colorViettelGray2
                          : AppThemes.colorViettelRed),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: TextField(
                        controller: controllerCarType,
                        enabled: false,
                        readOnly: true,
                        decoration: InputDecoration(
                          hintText: 'Xe 7 chỗ',
                          border: InputBorder.none,
                          contentPadding: const EdgeInsets.all(mPadding),
                          hintStyle:
                              CommonTextStyle.textStyleFontLatoNormalHint,
                          isDense: true,
                        ),
                        autofocus: false,
                        style: CommonTextStyle.textStyleFontLatoNormal,
                        onChanged: (_) {},
                      ),
                    ),
                    Container(
                      width: 50.0,
                      height: 50.0,
                      padding: const EdgeInsets.all(mPadding),
                      margin: const EdgeInsets.only(bottom: 0),
                      //check password field type
                      child: Icon(Icons.keyboard_arrow_down_sharp),
                    )
                  ],
                ),
              ),
            ),
          ],
        ),
      );
}
