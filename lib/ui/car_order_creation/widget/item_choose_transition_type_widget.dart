import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:trackcarvcc/constants/style/style.dart';

import 'transition_type_dropdown_sheet.dart';

class ItemChooseTransitionType extends StatelessWidget {
  const ItemChooseTransitionType(
      {Key key, this.controller, this.isValidate = true, this.onChanged})
      : super(key: key);

  final TextEditingController controller;
  final bool isValidate;
  final ValueChanged<TransitionTypeItem> onChanged;

  @override
  Widget build(BuildContext context) {
    return _itemChooseTransitionType(context);
  }

  _itemChooseTransitionType(BuildContext context) => Padding(
        padding: const EdgeInsets.only(top: mPaddingLarge),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.compare_arrows,
                  color: AppThemes.colorViettelRed,
                ),
                const SizedBox(
                  width: mPadding,
                ),
                Text(
                  'Chọn kiểu đi',
                  style: CommonTextStyle.textStyleFontLatoNormal,
                ),
              ],
            ),
            const SizedBox(height: mPadding),
            GestureDetector(
              onTap: () {
                FocusScope.of(context).requestFocus(FocusNode());
                final action = TranisitionDropDownSheet(
                  listSeverities: [
                    TransitionTypeItem('Một chiều', 1),
                    TransitionTypeItem('Hai chiều', 2),
                    TransitionTypeItem('Phát sinh', 3),
                    TransitionTypeItem('Đặc biệt', 4),
                    TransitionTypeItem('Xe tải', 5),
                  ],
                  onItemActionTap: (action) {
                    controller.text = action.value;
                    onChanged(action);
                  },
                );
                showCupertinoModalPopup(
                    context: context, builder: (context) => action);
              },
              child: Container(
                decoration: BoxDecoration(
                  color: AppThemes.colorViettelGray3,
                  borderRadius: const BorderRadius.all(
                    Radius.circular(mRadiusSmall),
                  ),
                  border: Border.all(
                      color: isValidate
                          ? AppThemes.colorViettelGray2
                          : AppThemes.colorViettelRed),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: TextField(
                        controller: controller,
                        enabled: false,
                        readOnly: true,
                        decoration: InputDecoration(
                          hintText: 'Một chiều',
                          border: InputBorder.none,
                          contentPadding: const EdgeInsets.all(mPadding),
                          hintStyle:
                              CommonTextStyle.textStyleFontLatoNormalHint,
                          isDense: true,
                        ),
                        autofocus: false,
                        style: CommonTextStyle.textStyleFontLatoNormal,
                        onChanged: (value) {},
                      ),
                    ),
                    Container(
                      width: 50.0,
                      height: 50.0,
                      padding: const EdgeInsets.all(mPadding),
                      margin: const EdgeInsets.only(bottom: 0),
                      //check password field type
                      child: Icon(Icons.keyboard_arrow_down_sharp),
                    )
                  ],
                ),
              ),
            ),
          ],
        ),
      );
}
