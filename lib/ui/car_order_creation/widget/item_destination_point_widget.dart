import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:trackcarvcc/constants/style/style.dart';
import 'package:trackcarvcc/controllers/order_car_creation_controller.dart';
import 'package:trackcarvcc/models/models.dart';
import 'package:trackcarvcc/routes/routes.dart';
import 'package:trackcarvcc/ui/address_selection/data/address_info.dart';

class ItemDestinationPoint extends GetView<CarOrderCreationController> {
  const ItemDestinationPoint({
    Key key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return _itemDestinationPoint(context);
  }

  _itemDestinationPoint(BuildContext context) => Padding(
        padding: const EdgeInsets.only(top: mPaddingLarge),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.my_location_outlined,
                      color: AppThemes.colorViettelRed,
                    ),
                    const SizedBox(
                      width: mPadding,
                    ),
                    Text(
                      'Thêm điểm đến',
                      style: CommonTextStyle.textStyleFontLatoNormal,
                    ),
                  ],
                ),
                IconButton(
                  icon: Icon(
                    Icons.add_circle,
                    color: Colors.red,
                  ),
                  onPressed: () {
                    controller.addDestinationPoints();
                  },
                )
              ],
            ),
            const SizedBox(height: mPadding),
            Obx(
              () => ListView.separated(
                itemCount: controller.destinationPoints.length,
                separatorBuilder: (context, index) => const SizedBox(height: mPadding),
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemBuilder: (context, index) {
                  var item = controller.destinationPoints[index];
                  return GestureDetector(
                    onTap: () {
                      Get.toNamed(Routers.address).then((addressInfo) {
                        if (addressInfo is AddressInfo) {
                          final address =
                              addressInfo.numbHouse + '' + addressInfo.ward + ',' + addressInfo.district + ',' + addressInfo.province + ', Việt Nam';
                          controller.setAddressPositionDestinationPoints(addressInfo.provinceId ,address, index);
                          // textController.text = address;
                          // controller.changeDestinationPoint(address);
                          // controller.destinationProvince = addressInfo.province;
                          controller.changeVisibilityOfExpectDistance();
                        }
                      });
                    },
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              'Điểm đến ${index + 1}',
                              style: CommonTextStyle.textStyleFontLatoNormal,
                            ),
                            IconButton(
                              icon: Icon(Icons.delete_outline_rounded, color: Colors.red,),
                              onPressed: () {
                                controller.removeAddressPositionDestinationPoints(index);
                                controller.changeVisibilityOfExpectDistance();
                              },
                            )
                          ],
                        ),
                        Container(
                          decoration: BoxDecoration(
                            color: AppThemes.colorViettelGray3,
                            borderRadius: const BorderRadius.all(
                              Radius.circular(mRadiusSmall),
                            ),
                            border: Border.all(color: (item.toAddress ?? '').isNotEmpty ? AppThemes.colorViettelGray2 : AppThemes.colorViettelRed),
                          ),
                          padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                          child: Row(
                            children: [
                              Icon(Icons.location_on, color: AppThemes.colorViettelGray2),
                              const SizedBox(
                                width: mPadding,
                              ),
                              Expanded(
                                child: Text(
                                  item.toAddress ?? 'Chọn điạ điểm',
                                  style: CommonTextStyle.textStyleFontLatoNormal.copyWith(
                                    color: (item.toAddress ?? '').isEmpty ? AppThemes.colorViettelGray2 : AppThemes.colorViettelGray1,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      );
}
