import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:trackcarvcc/constants/images.dart';
import 'package:trackcarvcc/constants/style/style.dart';
import 'package:trackcarvcc/controllers/controllers.dart';
import 'package:trackcarvcc/repository/api/urls.dart';
import 'package:trackcarvcc/ui/images/import_file_bottomsheet.dart';
import 'package:trackcarvcc/widget/view_file_attachment.dart';

class ItemFileAttachment extends StatelessWidget {
  ItemFileAttachment({
    Key key,
  }) : super(key: key);
  final CarOrderCreationController controller =
      Get.find<CarOrderCreationController>();

  @override
  Widget build(BuildContext context) {
    return Obx(
      () {
        if (controller.destinationPoints.any((element) =>
            element.toProvinceId != controller.startPointProvinceId.value)) {
          return Padding(
            padding: const EdgeInsets.only(top: mPaddingLarge),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.map,
                      color: AppThemes.colorViettelRed,
                    ),
                    const SizedBox(
                      width: mPadding,
                    ),
                    Text(
                      'File đính kèm',
                      style: CommonTextStyle.textStyleFontLatoNormal,
                    ),
                    const SizedBox(
                      width: mPadding,
                    ),
                    IconButton(
                      icon: Icon(Icons.add_box),
                      onPressed: () {
                        showModalBottomSheet<void>(
                          context: context,
                          builder: (_) {
                            return ImportFileBottomSheet(
                              onChooseImageSource: () {
                                Navigator.pop(context);
                              },
                              onFileSelected: (file) async {
                                controller.postFile(file);
                              },
                              onSelectImage: (image) {
                                controller.postFile(image);
                              },
                            );
                          },
                        );
                      },
                    ),
                  ],
                ),
                const SizedBox(
                  height: mPaddingXSmall,
                ),
                if ((controller.listFile ?? []).isNotEmpty)
                  Container(
                    height: 100,
                    child: ListView.builder(
                      itemBuilder: (_, index) {
                        return Stack(
                          children: <Widget>[
                            InkWell(
                              onTap: () {
                                Get.to(
                                  ViewFileAttachment(
                                    fileUrl: BASE_URL +
                                        "BookCarRestService/service/showImage?fileName=" +
                                        controller.listFile[index].imagePath,
                                    fileType: controller
                                                .listFile[index].filePath
                                                .split(".")
                                                .last ==
                                            "pdf"
                                        ? FileType.pdf
                                        : FileType.image,
                                    downloadFile: () {
                                      controller.downloadFile(
                                        filePath: controller
                                            .listFile[index].imagePath,
                                        fileName: controller
                                            .listFile[index].filePath
                                            .split("/")
                                            .last,
                                      );
                                    },
                                  ),
                                );
                              },
                              child: Container(
                                margin: const EdgeInsets.only(right: 10.0),
                                padding: EdgeInsets.only(right: 5, top: 5),
                                child: new ClipRRect(
                                  borderRadius: BorderRadius.circular(8.0),
                                  child: Image.network(
                                    BASE_URL +
                                        "BookCarRestService/service/showImage?fileName=" +
                                        controller.listFile[index].imagePath,
                                    errorBuilder: (BuildContext context,
                                        Object exception,
                                        StackTrace stackTrace) {
                                      return Image.asset(file_pdf);
                                    },
                                    fit: BoxFit.contain,
                                  ),
                                ),
                              ),
                            ),
                            Positioned(
                              right: 10,
                              child: ClipOval(
                                child: Material(
                                  color: Color(0xFF9B9B9B), // Button color
                                  child: InkWell(
                                    splashColor: Colors.red, // Splash color
                                    onTap: () {
                                      controller.listFile.removeAt(index);
                                    },
                                    child: SizedBox(
                                        width: 20,
                                        height: 20,
                                        child: Icon(
                                          Icons.clear,
                                          size: 15,
                                        )),
                                  ),
                                ),
                              ),
                            )
                          ],
                        );
                      },
                      itemCount: (controller.listFile ?? []).length,
                      shrinkWrap: true,
                      scrollDirection: Axis.horizontal,
                    ),
                  ),
              ],
            ),
          );
        } else {
          return SizedBox();
        }
      },
    );
  }
}
