import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:trackcarvcc/constants/style/style.dart';
import 'package:trackcarvcc/controllers/order_car_creation_controller.dart';

import 'bottom_sheet/search_car_widget.dart';
import 'bottom_sheet/search_driver_widget.dart';

class CarAndDriver extends GetView<CarOrderCreationController> {
  const CarAndDriver(
      {Key key,
      this.carController,
      this.isValidated = true,
      this.onChanged,
      this.driverController})
      : super(key: key);

  final TextEditingController carController;
  final TextEditingController driverController;
  final bool isValidated;
  final ValueChanged<String> onChanged;

  @override
  Widget build(BuildContext context) {
    return _itemChooseCar(context);
  }

  _itemChooseCar(BuildContext context) => Obx(
      () => Padding(
          padding:
              const EdgeInsets.only(top: mPaddingSmall, left: mPaddingXLarge),
          child: <PERSON>umn(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Chọn thông tin xe & lái xe',
                style: CommonTextStyle.textStyleFontLatoNormal,
              ),
              const SizedBox(height: mPadding),
              GestureDetector(
                onTap: () {
                  showModalBottomSheet(
                    context: context,
                    isScrollControlled: true,
                    backgroundColor: Colors.transparent,
                    builder: (BuildContext context) {
                      return SearchCar(
                        onChanged: (car) {
                          carController.text = car.licenseCar;
                          controller.changeCarLicence(car.licenseCar);
                        },
                      );
                    },
                  );
                },
                child: Container(
                  decoration: BoxDecoration(
                    color: AppThemes.colorViettelGray3,
                    borderRadius: const BorderRadius.all(
                      Radius.circular(mRadiusSmall),
                    ),
                    border: Border.all(
                        color: controller.userCarLicenceIsValid
                            ? AppThemes.colorViettelGray2
                            : AppThemes.colorViettelRed),
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        child: TextField(
                          controller: carController,
                          enabled: false,
                          readOnly: true,
                          decoration: InputDecoration(
                            hintText: 'Chọn xe',
                            border: InputBorder.none,
                            contentPadding: const EdgeInsets.all(mPadding),
                            hintStyle:
                                CommonTextStyle.textStyleFontLatoNormalHint,
                            isDense: true,
                          ),
                          autofocus: false,
                          style: CommonTextStyle.textStyleFontLatoNormal,
                          onChanged: (value) {},
                        ),
                      ),
                      Container(
                        width: 50.0,
                        height: 50.0,
                        padding: const EdgeInsets.all(mPadding),
                        margin: const EdgeInsets.only(bottom: 0),
                        //check password field type
                        child: Icon(Icons.arrow_forward_ios_rounded),
                      )
                    ],
                  ),
                ),
              ),
              const SizedBox(height: mPadding),
              GestureDetector(
                onTap: () {
                  showModalBottomSheet(
                    context: context,
                    isScrollControlled: true,
                    backgroundColor: Colors.transparent,
                    builder: (BuildContext context) {
                      return SearchDriver(
                        onChanged: (driver) {
                          driverController.text = driver.driverName;
                          controller.changeCarDriver(driver.driverName);
                        },
                      );
                    },
                  );
                },
                child: Container(
                  decoration: BoxDecoration(
                    color: AppThemes.colorViettelGray3,
                    borderRadius: const BorderRadius.all(
                      Radius.circular(mRadiusSmall),
                    ),
                    border: Border.all(
                        color: controller.userCarDriverIsValid
                            ? AppThemes.colorViettelGray2
                            : AppThemes.colorViettelRed),
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        child: TextField(
                          controller: driverController,
                          enabled: false,
                          readOnly: true,
                          decoration: InputDecoration(
                            hintText: 'Chọn lái xe',
                            border: InputBorder.none,
                            contentPadding: const EdgeInsets.all(mPadding),
                            hintStyle:
                                CommonTextStyle.textStyleFontLatoNormalHint,
                            isDense: true,
                          ),
                          autofocus: false,
                          style: CommonTextStyle.textStyleFontLatoNormal,
                          onChanged: (value) {},
                        ),
                      ),
                      Container(
                        width: 50.0,
                        height: 50.0,
                        padding: const EdgeInsets.all(mPadding),
                        margin: const EdgeInsets.only(bottom: 0),
                        //check password field type
                        child: Icon(Icons.arrow_forward_ios_rounded),
                      )
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
  );
}
