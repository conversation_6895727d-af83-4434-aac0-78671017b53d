import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:trackcarvcc/constants/style/style.dart';
import 'package:trackcarvcc/controllers/order_car_creation_controller.dart';
import 'package:trackcarvcc/models/models.dart';

import 'bottom_sheet/search_user_accept_widget.dart';

class ItemUserAccept extends GetView<CarOrderCreationController> {
  final TextEditingController textController;
  final bool isValidate;

  ItemUserAccept({this.textController, this.isValidate});

  @override
  Widget build(BuildContext context) {
    return _itemUserAccept(context);
  }

  _itemUserAccept(BuildContext context) => Obx(
        () => Padding(
          padding: const EdgeInsets.only(top: mPaddingLarge),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.verified_user,
                    color: AppThemes.colorViettelRed,
                  ),
                  const SizedBox(
                    width: mPadding,
                  ),
                  Text(
                    'Chọn người phê duyệt',
                    style: CommonTextStyle.textStyleFontLatoNormal,
                  ),
                ],
              ),
              const SizedBox(height: mPadding),
              GestureDetector(
                onTap: () {
                  FocusScope.of(context).requestFocus(FocusNode());
                  showModalBottomSheet(
                    context: context,
                    isScrollControlled: true,
                    backgroundColor: Colors.transparent,
                    builder: (BuildContext context) {
                      return SearchUserAccept(
                        onChanged: (manager) {
                          controller.addUserManagerTagged(manager);
                        },
                      );
                    },
                  );
                },
                child: Container(
                  decoration: BoxDecoration(
                    color: AppThemes.colorViettelGray3,
                    borderRadius: const BorderRadius.all(
                      Radius.circular(mRadiusSmall),
                    ),
                    border: Border.all(
                        color: isValidate
                            ? AppThemes.colorViettelGray2
                            : AppThemes.colorViettelRed),
                  ),
                  child: Row(
                    children: [
                      Expanded(child: _buildListTagForm()),
                      Container(
                        width: 50.0,
                        height: 50.0,
                        padding: const EdgeInsets.all(mPadding),
                        margin: const EdgeInsets.only(bottom: 0),
                        //check password field type
                        child: Icon(Icons.search),
                      )
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      );

  _buildListTagForm() {
    if (controller.listUserManagerChosen == null ||
        controller.listUserManagerChosen.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(mPadding),
        child: Text(
          'Tìm kiếm',
          style: CommonTextStyle.textStyleFontLatoNormalHint,
        ),
      );
    }
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(mPadding),
      child: Wrap(
        spacing: 6.0,
        runSpacing: 6.0,
        direction: Axis.horizontal,
        children: controller.listUserManagerChosen
            .map((item) =>
                _buildTagItemForm(item.fullName, item.sysUserId, item))
            .toList()
            .cast<Widget>(),
      ),
    );
  }

  _buildTagItemForm(String userName, int userId, UserLogin user) {
    return Chip(
      label: Row(
        mainAxisSize : MainAxisSize.min,
        children: [
          Text(
            userName,
            style: CommonTextStyle.textStyleFontLatoNormal
                .copyWith(color: Colors.white),
          ),
          const SizedBox(
            width: mPadding,
          ),
          Flexible(
            child: FittedBox(
              fit: BoxFit.scaleDown,
              child: Text(
                user?.status == "1"
                    ? 'Đang chờ duyệt'
                    : user?.status == "2"
                        ? 'Đã được duyệt'
                        : user.status == "3"
                            ? 'Từ chối'
                            : user.status == "4"
                                ? 'Yêu cầu sửa'
                                : user.status == "5"
                                    ? 'Đã đóng lệnh'
                                    : user.status == "6"
                                        ? 'Chờ đóng lệnh'
                                        : ' ' ?? ' ',
                style: CommonTextStyle.textStyleFontLatoNormal.copyWith(
                    color: user?.status == "1"
                        ? Colors.grey.shade400
                        : user?.status == "2"
                            ? Colors.green
                            : user.status == "3"
                                ? Colors.red
                                : user.status == "4"
                                    ? Colors.indigo.shade500
                                    : user.status == "5"
                                        ? Colors.green
                                        : user.status == "6"
                                            ? Colors.grey.shade400
                                            : Colors.white ?? Colors.white),
              ),
            ),
          ),
        ],
      ),
      backgroundColor: AppThemes.colorViettelRed,
      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(mRadiusSmall),
      ),
      padding: const EdgeInsets.only(left: 0, right: 0, bottom: 0, top: 0),
      deleteIcon: const Icon(
        Icons.close,
        color: Colors.white,
        size: 20,
      ),
      onDeleted: () {
        _deleteTagItem();
      },
    );
  }

  _deleteTagItem() {
    controller.deleteManagerTagged();
  }
}
