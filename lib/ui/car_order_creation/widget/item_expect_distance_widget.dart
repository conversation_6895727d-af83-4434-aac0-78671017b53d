import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:trackcarvcc/constants/style/style.dart';
import 'package:trackcarvcc/controllers/order_car_creation_controller.dart';

class ExpectDistance extends GetView<CarOrderCreationController> {
  @override
  Widget build(BuildContext context) {
    return _itemExpectDistance(context);
  }

  _itemExpectDistance(BuildContext context) => Obx(
        () => Padding(
          padding: const EdgeInsets.only(top: mPaddingLarge),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.add_road_outlined,
                    color: AppThemes.colorViettelRed,
                  ),
                  const SizedBox(
                    width: mPadding,
                  ),
                  Text(
                    controller.outOfProvince.value ? 'Đi ngoại tỉnh' : 'Đi nội tỉnh',
                    style: CommonTextStyle.textStyleFontLatoNormal,
                  ),
                ],
              ),
              const SizedBox(
                height: mPadding,
              ),
              Text(
                'Dự kiến quãng đường là: ' + controller.expectDistance.value.toString() + ' km',
                style: CommonTextStyle.textStyleFontLatoNormal,
              ),
              const SizedBox(
                height: mPadding,
              ),
              Text(
                'Dự kiến thời gian là: ' + controller.expectTime.value,
                style: CommonTextStyle.textStyleFontLatoNormal,
              ),
              const SizedBox(
                height: mPadding,
              ),
              !controller.getDistanceSuccess.value
                  ? Text(
                      'Không tính được km dự kiến, bạn vui lòng kiểm tra lại thông tin địa điểm đi và địa điểm đến',
                      style: CommonTextStyle.textStyleFontLatoNormal.copyWith(
                        color: Colors.red,
                        fontSize: 13,
                      ),
                    )
                  : const SizedBox(),
            ],
          ),
        ),
      );
}
