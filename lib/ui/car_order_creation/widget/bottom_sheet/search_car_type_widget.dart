import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:trackcarvcc/constants/app_state.dart';
import 'package:trackcarvcc/constants/style/style.dart';
import 'package:trackcarvcc/controllers/order_car_creation_controller.dart';
import 'package:trackcarvcc/models/models.dart';
import 'package:trackcarvcc/widget/circle_loading.dart';

class SearchCarType extends GetView<CarOrderCreationController> {
  final TextEditingController _controller = TextEditingController();
  final ValueChanged<LstBookCarDto> onChanged;

  SearchCarType({this.onChanged});

  @override
  Widget build(BuildContext context) {
    return _body(context);
  }

  Widget _body(BuildContext context) {
    return GetBuilder<CarOrderCreationController>(initState: (_) {
      controller.fetchListCarType();
    },
      builder: (_) {
        return GestureDetector(
          onTap: () {
            FocusScope.of(context).requestFocus(FocusNode());
          },
          child: Container(
            height: MediaQuery.of(context).size.height * 0.9,
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius:
              BorderRadius.vertical(top: Radius.circular(mRadiusMedium)),
            ),
            padding:
            EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
            child: Stack(
              children: [
                Padding(
                  padding: const EdgeInsets.only(top: mPadding),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Center(
                        child: Text(
                          'Chọn loai xe',
                          style: CommonTextStyle.textStyleFontLatoLargeBoldBlack,
                        ),
                      ),
                      const SizedBox(height: mPadding),
                      const Divider(
                        color: AppThemes.colorViettelGray3,
                        height: 1,
                      ),
                      const SizedBox(height: mPaddingLarge),
                      _buildSearchBox(),
                      _buildListCar(),
                    ],
                  ),
                ),
                Align(
                  alignment: Alignment.topRight,
                  child: IconButton(
                    padding: const EdgeInsets.only(right: mPaddingLarge),
                    icon: const Icon(
                      Icons.close,
                      size: 23,
                      color: AppThemes.colorViettelBlack,
                    ),
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );

  }

  Widget _buildSearchBox() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: mPaddingLarge),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _controller,
                  decoration: InputDecoration(
                    hintText: 'Tìm kiếm',
                    border: InputBorder.none,
                    contentPadding: const EdgeInsets.only(
                        top: mPadding, bottom: mPadding, right: mPadding),
                    hintStyle: CommonTextStyle.textStyleFontLatoNormalHint,
                    isDense: true,
                  ),
                  cursorColor: AppThemes.colorViettelRed,
                  autofocus: false,
                  style: CommonTextStyle.textStyleFontLatoNormal,
                  onChanged: (value) {
                    controller.searchCarType(value);
                  },
                ),
              ),
              Container(
                width: 45,
                height: 45,
                padding: const EdgeInsets.all(mPadding),
                margin: const EdgeInsets.only(bottom: 0),
                //check password field type
                child: IconButton(
                  onPressed: () {
                    //FocusScope.of(context).requestFocus(_focusNode);
                  },
                  icon: Icon(Icons.search),
                  padding: const EdgeInsets.all(0),
                ),
              )
            ],
          ),
          Divider(color: AppThemes.colorViettelGray3, height: 1),
        ],
      ),
    );
  }

  Widget _buildListCar() => Expanded(
    child: Obx(() {
      if (controller.appState() == AppState.LOADING) {
        return _buildLoading();
      }

      if (controller.appState() == AppState.ERROR) {
        return _buildErrorWidget();
      }
      if (controller.listCarType.isEmpty) {
        return _buildNoDataWidget();
      } else {
        return ListView.builder(
          itemCount: controller.listCarType.length,
          padding: EdgeInsets.symmetric(horizontal: 8),
          itemBuilder: (context, index) {
            return InkWell(
              onTap: () {
                if(onChanged != null) {
                  onChanged(controller.listCarType[index]);
                  Get.back();
                }
              },
              child: Padding(
                padding: const EdgeInsets.all(mPadding),
                child: Text(
                  controller.listCarType[index].carTypeName,
                  style: CommonTextStyle.textStyleFontLatoNormal,
                ),
              ),
            );
          },
        );
      }
    }),
  );

  Widget _buildLoading() => Container(
    color: Colors.white,
    child: LoadingCircle(),
  );

  Widget _buildNoDataWidget() => Center(
    child: Text("Không có dữ liệu!"),
  );

  Widget _buildErrorWidget() => Center(
    child: Text("Có lỗi xảy ra. Vui lòng thử lại sau."),
  );
}
