import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:trackcarvcc/constants/app_state.dart';
import 'package:trackcarvcc/constants/style/style.dart';
import 'package:trackcarvcc/controllers/order_car_creation_controller.dart';
import 'package:trackcarvcc/models/models.dart';
import 'package:trackcarvcc/widget/circle_loading.dart';
import 'package:trackcarvcc/helpers/extensions.dart';

class SearchUserGoWith extends GetView<CarOrderCreationController> {
  final TextEditingController _controller = TextEditingController();
  final ValueChanged<UserLogin> onChanged;

  SearchUserGoWith({this.onChanged});

  @override
  Widget build(BuildContext context) {
    return _body(context);
  }

  Widget _body(BuildContext context) {
    return GetBuilder<CarOrderCreationController>(initState: (_) {
      controller.fetchListUserGoWith();
    }, builder: (_) {
      return GestureDetector(
        onTap: () {
          FocusScope.of(context).requestFocus(FocusNode());
        },
        child: Container(
          height: MediaQuery.of(context).size.height * 0.9,
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius:
                BorderRadius.vertical(top: Radius.circular(mRadiusMedium)),
          ),
          padding:
              EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
          child: Stack(
            children: [
              Padding(
                padding: const EdgeInsets.only(top: mPadding),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Center(
                      child: Text(
                        'Chọn người đi cùng',
                        style: CommonTextStyle.textStyleFontLatoLargeBoldBlack,
                      ),
                    ),
                    const SizedBox(height: mPadding),
                    const Divider(
                      color: AppThemes.colorViettelGray3,
                      height: 1,
                    ),
                    const SizedBox(height: mPaddingLarge),
                    _buildSearchBox(),
                    _buildListUserGoWith()
                  ],
                ),
              ),
              Align(
                alignment: Alignment.topRight,
                child: IconButton(
                  padding: const EdgeInsets.only(right: mPaddingLarge),
                  icon: const Icon(
                    Icons.close,
                    size: 23,
                    color: AppThemes.colorViettelBlack,
                  ),
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                ),
              ),
            ],
          ),
        ),
      );
    });
  }

  Widget _buildSearchBox() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: mPaddingLarge),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _controller,
                  decoration: InputDecoration(
                    hintText: 'Tìm kiếm',
                    border: InputBorder.none,
                    contentPadding: const EdgeInsets.only(
                        top: mPadding, bottom: mPadding, right: mPadding),
                    hintStyle: CommonTextStyle.textStyleFontLatoNormalHint,
                    isDense: true,
                  ),
                  cursorColor: AppThemes.colorViettelRed,
                  autofocus: false,
                  style: CommonTextStyle.textStyleFontLatoNormal,
                  onChanged: (value) {
                    controller.searchUserGoWith(value);
                  },
                ),
              ),
              Container(
                width: 45,
                height: 45,
                padding: const EdgeInsets.all(mPadding),
                margin: const EdgeInsets.only(bottom: 0),
                //check password field type
                child: IconButton(
                  onPressed: () {
                    //FocusScope.of(context).requestFocus(_focusNode);
                  },
                  icon: Icon(Icons.search),
                  padding: const EdgeInsets.all(0),
                ),
              )
            ],
          ),
          Divider(color: AppThemes.colorViettelGray3, height: 1),
        ],
      ),
    );
  }

  Widget _buildListUserGoWith() => Expanded(
        child: Obx(() {
          if (controller.appState() == AppState.LOADING) {
            return _buildLoading();
          }

          if (controller.appState() == AppState.ERROR) {
            return _buildErrorWidget();
          }
          if (controller.listUserGoWith.isEmpty) {
            return _buildNoDataWidget();
          } else {
            return ListView.builder(
              itemCount: controller.listUserGoWith.length,
              padding: EdgeInsets.symmetric(horizontal: 8),
              itemBuilder: (context, index) {
                return InkWell(
                  onTap: () {
                    if (onChanged != null && !controller.listUserGoWith[index].isChosen) {
                      // onChanged(controller.listUserGoWith[index]);
                      controller.addUserTagged(controller.listUserGoWith[index]);
                      Get.back();
                    } else {
                      showErrorToast(error: 'Người dùng đã được chọn');
                    }
                  },
                  child: Padding(
                    padding: const EdgeInsets.all(mPadding),
                    child: Container(
                      color: controller.listUserGoWith[index].isChosen ? AppThemes.colorViettelRed02 : Colors.white,
                      child: Column(
                        crossAxisAlignment : CrossAxisAlignment.start,
                        children: [
                          Text(
                            controller.listUserGoWith[index]?.fullName ?? '',
                            style: CommonTextStyle.textStyleFontLatoNormalBold,
                          ),
                          const SizedBox(
                            height: mPaddingSmall,
                          ),
                          Text(
                            controller.listUserGoWith[index]?.email ?? 'Chưa có email',
                            style: CommonTextStyle.textStyleFontLatoNormalLight,
                          ),
                          const SizedBox(
                            height: mPaddingSmall,
                          ),
                          Text(
                            controller.listUserGoWith[index]?.phoneNumber ?? 'Chưa có số điện thoại',
                            style: CommonTextStyle.textStyleFontLatoNormalLight,
                          ),
                          const SizedBox(
                            height: mPaddingSmall,
                          ),
                          const Divider(
                            height: 1,
                            color: AppThemes.colorViettelGray3,
                          )
                        ],
                      ),
                    ),
                  ),
                );
              },
            );
          }
        }),
      );

  Widget _buildLoading() => Container(
        color: Colors.white,
        child: LoadingCircle(),
      );

  Widget _buildNoDataWidget() => Center(
        child: Text("Không có dữ liệu!"),
      );

  Widget _buildErrorWidget() => Center(
        child: Text("Có lỗi xảy ra. Vui lòng thử lại sau."),
      );
}
