import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:trackcarvcc/constants/style/style.dart';
import 'package:trackcarvcc/controllers/order_car_creation_controller.dart';
import 'package:trackcarvcc/routes/routes.dart';
import 'package:trackcarvcc/ui/address_selection/data/address_info.dart';

class ItemStartPoint extends GetView<CarOrderCreationController> {
  const ItemStartPoint(
      {Key key, this.textController, this.isValidate = true, this.onChanged})
      : super(key: key);

  final TextEditingController textController;
  final bool isValidate;
  final ValueChanged<String> onChanged;

  @override
  Widget build(BuildContext context) {
    return _itemStartPoint(context);
  }

  _itemStartPoint(BuildContext context) => Padding(
        padding: const EdgeInsets.only(top: mPaddingLarge),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.location_on_outlined,
                      color: AppThemes.colorViettelRed,
                    ),
                    const SizedBox(
                      width: mPadding,
                    ),
                    Text(
                      'Điểm khởi hành',
                      style: CommonTextStyle.textStyleFontLatoNormal,
                    ),
                  ],
                ),
                InkResponse(
                  onTap: () {
                    Get.snackbar(":((","Chức năng đang phát triển. Vui lòng quay lại sau!",backgroundColor: Colors.white);
                  },
                  child: Text(
                    'Bản đồ',
                    style: CommonTextStyle.textStyleFontLatoNormal,
                  ),
                )
              ],
            ),
            const SizedBox(height: mPadding),
            GestureDetector(
              onTap: () {
                Get.toNamed(Routers.address).then((addressInfo) {
                  if (addressInfo is AddressInfo) {
                    final address = addressInfo.numbHouse +
                        ' ' +
                        addressInfo.ward +
                        ', ' +
                        addressInfo.district +
                        ', ' +
                        addressInfo.province +
                        ', Việt Nam';
                    textController.text = address;
                    onChanged(address);
                    controller.startPointProvinceId.value = addressInfo.provinceId;
                    controller.startProvince = addressInfo.province;
                    controller.changeVisibilityOfExpectDistance();
                  }
                });
              },
              child: Container(
                decoration: BoxDecoration(
                  color: AppThemes.colorViettelGray3,
                  borderRadius: const BorderRadius.all(
                    Radius.circular(mRadiusSmall),
                  ),
                  border: Border.all(
                      color: isValidate
                          ? AppThemes.colorViettelGray2
                          : AppThemes.colorViettelRed),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: TextField(
                        controller: textController,
                        enabled: false,
                        readOnly: true,
                        maxLines: 2,
                        decoration: InputDecoration(
                          hintText: 'Chọn điạ điểm',
                          border: InputBorder.none,
                          contentPadding: const EdgeInsets.all(mPadding),
                          hintStyle:
                              CommonTextStyle.textStyleFontLatoNormalHint,
                          isDense: true,
                        ),
                        autofocus: false,
                        style: CommonTextStyle.textStyleFontLatoNormal,
                        onChanged: (value) {},
                      ),
                    ),
                    Container(
                      width: 50.0,
                      height: 50.0,
                      padding: const EdgeInsets.all(mPadding),
                      margin: const EdgeInsets.only(bottom: 0),
                      //check password field type
                      child: Icon(Icons.navigate_next),
                    )
                  ],
                ),
              ),
            ),
          ],
        ),
      );
}
