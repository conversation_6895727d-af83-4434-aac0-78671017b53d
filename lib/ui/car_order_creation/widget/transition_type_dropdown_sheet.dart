import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:trackcarvcc/constants/style/style.dart';

const double mWidthIconImage = 24.0;

typedef OnItemActionTap = void Function(TransitionTypeItem);

class TranisitionDropDownSheet extends StatefulWidget {
  const TranisitionDropDownSheet({this.listSeverities, this.onItemActionTap});

  final List<TransitionTypeItem> listSeverities;
  final OnItemActionTap onItemActionTap;

  @override
  _TranisitionDropDownSheetState createState() => _TranisitionDropDownSheetState();
}

class _TranisitionDropDownSheetState extends State<TranisitionDropDownSheet> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return CupertinoActionSheet(
      actions: widget.listSeverities.map((e) => _itemAction(e)).toList(),
      cancelButton: CupertinoActionSheetAction(
        child: Text('Hủy', style: CommonTextStyle.textStyleFontLatoRedNormal),
        onPressed: () {
          Navigator.pop(context);
        },
      ),
    );
  }

  Widget _itemAction(TransitionTypeItem item) => Container(
        color: Colors.white,
        child: CupertinoActionSheetAction(
          onPressed: () {
            widget.onItemActionTap(item);
            Navigator.pop(context);
          },
          child: Stack(
            alignment: AlignmentDirectional.center,
            children: [
              Center(
                child: Padding(
                  padding: const EdgeInsets.only(right: 120),
                  child: SizedBox(
                      width: mWidthIconImage,
                      height: mWidthIconImage,
                      child: Icon(
                        item.code == 1
                            ? Icons.arrow_back
                            : item.code == 2
                                ? Icons.compare_arrows
                                : item.code == 3
                                    ? Icons.devices_other
                                    : item.code == 4
                                        ? Icons.star
                                        : Icons.car_repair,
                        color: Colors.red,
                      )),
                ),
              ),
              Center(
                child: Text(
                  item.value,
                  style: CommonTextStyle.textStyleFontLatoNormal,
                ),
              )
            ],
          ),
        ),
      );
}

class TransitionTypeItem {
  TransitionTypeItem(this.value, this.code);

  final int code;
  final String value;
}
