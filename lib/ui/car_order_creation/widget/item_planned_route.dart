import 'package:flutter/material.dart';

// import 'package:flutter_polyline_points/flutter_polyline_points.dart';
import 'package:get/get.dart';
import 'package:trackcarvcc/constants/style/style.dart';
import 'package:trackcarvcc/controllers/order_car_creation_controller.dart';
import 'package:trackcarvcc/routes/routes.dart';
import '../../../helpers/extensions.dart';

class ItemPlannedRoutedWidget extends GetView<CarOrderCreationController> {
  const ItemPlannedRoutedWidget({
    Key key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        // if ((controller.startPoint.value ?? '').isEmpty ||
        //     (controller.destinationPoint.value ?? '').isEmpty) {
        //
        //   return;
        // }
        Get.toNamed(
          Routers.planned_routed,
          arguments: [
            controller.startPoint.value,
            ...controller.destinationPoints.map((e) => e.toAddress).toList(),
          ],
        );
      },
      child: Padding(
        padding: const EdgeInsets.only(top: mPaddingLarge),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.map,
                  color: AppThemes.colorViettelRed,
                ),
                const SizedBox(
                  width: mPadding,
                ),
                Text(
                  'Xem lộ trình đi dự kiến tại đây',
                  style: CommonTextStyle.textStyleFontLatoNormalBold.copyWith(
                    decoration: TextDecoration.underline,
                    color: Colors.blue,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
