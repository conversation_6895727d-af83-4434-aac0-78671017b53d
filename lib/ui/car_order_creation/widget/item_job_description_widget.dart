import 'package:flutter/material.dart';
import 'package:trackcarvcc/constants/style/style.dart';

class JobDescription extends StatelessWidget {
  const JobDescription(
      {Key key, this.controller, this.isValidate = true, this.onChanged})
      : super(key: key);

  final TextEditingController controller;
  final bool isValidate;
  final ValueChanged<String> onChanged;

  @override
  Widget build(BuildContext context) {
    return _jobDescription();
  }

  _jobDescription() => Padding(
        padding: const EdgeInsets.only(top: mPaddingLarge),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.sticky_note_2_outlined,
                  color: AppThemes.colorViettelRed,
                ),
                const SizedBox(
                  width: mPadding,
                ),
                Text(
                  'Nội dung công việc',
                  style: CommonTextStyle.textStyleFontLatoNormal,
                ),
              ],
            ),
            const SizedBox(height: mPadding),
            Container(
              decoration: BoxDecoration(
                color: AppThemes.colorViettelGray3,
                borderRadius: const BorderRadius.all(
                  Radius.circular(mRadiusSmall),
                ),
                border: Border.all(
                    color: isValidate
                        ? Colors.transparent
                        : AppThemes.colorViettelRed),
              ),
              child: TextField(
                controller: controller,
                enabled: true,
                readOnly: false,
                maxLines: 5,
                decoration: InputDecoration(
                  hintText: 'Nhập nội dung công việc',
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.all(mPadding),
                  hintStyle: CommonTextStyle.textStyleFontLatoNormalHint,
                  isDense: true,
                ),
                autofocus: false,
                style: CommonTextStyle.textStyleFontLatoNormal,
                onChanged: (value) {
                  onChanged(value);
                },
              ),
            ),
          ],
        ),
      );
}
