import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:trackcarvcc/constants/style/style.dart';
import 'package:trackcarvcc/controllers/order_car_creation_controller.dart';
import 'bottom_sheet/search_user_go_with_widget.dart';

class UserGoWith extends GetView<CarOrderCreationController> {
  final TextEditingController textController;
  final bool isValidate;

  const UserGoWith({Key key, this.textController, this.isValidate}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return _itemUserGoWith(context);
  }

  _itemUserGoWith(BuildContext context) => Obx(
        () => Padding(
          padding: const EdgeInsets.only(top: mPaddingLarge),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.person_add_sharp,
                    color: AppThemes.colorViettelRed,
                  ),
                  const SizedBox(
                    width: mPadding,
                  ),
                  Text(
                    'Chọn người đi cùng',
                    style: CommonTextStyle.textStyleFontLatoNormal,
                  ),
                  const SizedBox(
                    width: mPadding,
                  ),
                  Expanded(
                    child: FittedBox(
                      fit: BoxFit.scaleDown,
                      child: Text(
                        'Số người đã được chọn ${controller.numberUserChosen.value}',
                        style: CommonTextStyle.textStyleFontLatoRedNormal,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: mPadding),
              GestureDetector(
                onTap: () {
                  FocusScope.of(context).requestFocus(FocusNode());
                  showModalBottomSheet(
                    context: context,
                    isScrollControlled: true,
                    backgroundColor: Colors.transparent,
                    builder: (BuildContext context) {
                      return SearchUserGoWith(
                        onChanged: (value) {
                          controller.addUserTagged(value);
                        },
                      );
                    },
                  );
                },
                child: Container(
                  decoration: BoxDecoration(
                    color: AppThemes.colorViettelGray3,
                    borderRadius: const BorderRadius.all(
                      Radius.circular(mRadiusSmall),
                    ),
                    border: Border.all(
                        color: isValidate
                            ? AppThemes.colorViettelGray2
                            : AppThemes.colorViettelRed),
                  ),
                  child: Row(
                    children: [
                      Expanded(child: _buildListTagForm()),
                      Container(
                        width: 50.0,
                        height: 50.0,
                        padding: const EdgeInsets.all(mPadding),
                        margin: const EdgeInsets.only(bottom: 0),
                        //check password field type
                        child: Icon(Icons.search),
                      )
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      );

  _buildListTagForm() {
    if (controller.listUserGoWithChosen == null ||
        controller.listUserGoWithChosen.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(mPadding),
        child: Text(
          'Tìm kiếm',
          style: CommonTextStyle.textStyleFontLatoNormalHint,
        ),
      );
    }
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(mPadding),
      child: Wrap(
        spacing: 6.0,
        runSpacing: 6.0,
        direction: Axis.horizontal,
        children: controller.listUserGoWithChosen
            .map((item) => _buildTagItemForm(item.fullName, item.sysUserId))
            .toList()
            .cast<Widget>(),
      ),
    );
  }

  _buildTagItemForm(String userName, int userId) {
    return Chip(
      label: Text(
        userName,
        style: CommonTextStyle.textStyleFontLatoNormal
            .copyWith(color: Colors.white),
      ),
      backgroundColor: AppThemes.colorViettelRed,
      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(mRadiusSmall),
      ),
      padding: const EdgeInsets.only(left: 0, right: 0, bottom: 0, top: 0),
      deleteIcon: const Icon(
        Icons.close,
        color: Colors.white,
        size: 20,
      ),
      onDeleted: () {
        _deleteTagItem(userId);
      },
    );
  }

  _deleteTagItem(int userId) {
    controller.deleteUserTagged(userId);
  }
}
