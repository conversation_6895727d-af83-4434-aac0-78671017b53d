import 'package:flutter/material.dart';
import 'package:trackcarvcc/constants/constants.dart';
import 'package:trackcarvcc/constants/style/style.dart';
import 'package:trackcarvcc/helpers/string_utils.dart';
import 'package:trackcarvcc/widget/calendar_dialog.dart';

class ItemFinishTime extends StatelessWidget {
  const ItemFinishTime(
      {Key key,
      this.controller,
      this.timeController,
      this.isValidate = true,
      this.isExpanded = true,
      this.isClick = true,
      this.onChanged})
      : super(key: key);

  final TextEditingController controller;
  final TextEditingController timeController;
  final bool isValidate;
  final bool isExpanded;
  final bool isClick;
  final ValueChanged<String> onChanged;

  @override
  Widget build(BuildContext context) {
    return _itemFinishTime(context);
  }

  _itemFinishTime(BuildContext context) =>
      isExpanded ? Expanded(child: _body(context)) : _body(context);

  Widget _body(BuildContext context) => Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.lock_clock,
                color: AppThemes.colorViettelRed,
              ),
              const SizedBox(
                width: mPadding,
              ),
              Text(
                'Đến lúc',
                style: CommonTextStyle.textStyleFontLatoNormal,
              ),
            ],
          ),
          const SizedBox(height: mPadding),
          GestureDetector(
            onTap: () {
              if(isClick){
                showDialog(
                  context: context,
                  builder: (context) => CalendarDialog(
                    isEditTimer: true,
                    textTimerController: timeController,
                    startDate: DateTime.now(),
                    initialSelectedDay: DateTime.now(),
                  ),
                ).then(
                      (value) {
                    if (StringUtils.isEmpty(timeController.text)) {
                      timeController.text = Constants.TIMER_END_DEFAULT;
                    }
                    controller.text =
                        StringUtils.eventDateFormat(value, timeController.text);
                    onChanged(controller.text);
                  },
                );
              }
            },
            child: Container(
              decoration: BoxDecoration(
                color: AppThemes.colorViettelGray3,
                borderRadius: const BorderRadius.all(
                  Radius.circular(mRadiusSmall),
                ),
                border: Border.all(
                    color: isValidate
                        ? Colors.transparent
                        : AppThemes.colorViettelRed),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: controller,
                      enabled: false,
                      readOnly: true,
                      maxLines: 2,
                      decoration: InputDecoration(
                        hintText: 'Chọn thời gian',
                        border: InputBorder.none,
                        contentPadding: const EdgeInsets.all(mPadding),
                        hintStyle: CommonTextStyle.textStyleFontLatoNormalHint,
                        isDense: true,
                      ),
                      autofocus: false,
                      style: CommonTextStyle.textStyleFontLatoNormal,
                      onChanged: (value) {},
                    ),
                  ),
                  Container(
                    width: 50.0,
                    height: 50.0,
                    padding: const EdgeInsets.all(mPadding),
                    margin: const EdgeInsets.only(bottom: 0),
                    //check password field type
                    child: Icon(Icons.keyboard_arrow_down_sharp),
                  )
                ],
              ),
            ),
          ),
        ],
      );
}
