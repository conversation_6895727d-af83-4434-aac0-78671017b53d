import 'package:flutter/material.dart';
import 'package:trackcarvcc/constants/style/style.dart';
import 'package:trackcarvcc/models/response/user_role_response.dart';

class NextUserAccept extends StatelessWidget {
  final List<LstApprovePersons> listQLTS;
  final List<LstApprovePersons> listUserCarLeader;
  final List<LstApprovePersons> listUserCarCaptain;

  final String userQLTSSelected;
  final String userLeaderSelected;
  final String userCaptainSelected;

  final ValueChanged<LstApprovePersons> onChangedQlts;
  final ValueChanged<LstApprovePersons> onChangedCarLeader;
  final ValueChanged<LstApprovePersons> onChangedCarCaptain;

  const NextUserAccept({Key key,
    this.listQLTS,
    this.listUserCarLeader,
    this.listUserCarCaptain,
    this.userQLTSSelected,
    this.userLeaderSelected,
    this.userCaptainSelected,
    this.onChangedQlts,
    this.onChangedCarLeader,
    this.onChangedCarCaptain})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return _itemUserAccept(context);
  }

  _itemUserAccept(BuildContext context) =>
      Padding(
        padding: const EdgeInsets.only(top: mPaddingLarge),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.verified_user,
                  color: AppThemes.colorViettelRed,
                ),
                const SizedBox(
                  width: mPadding,
                ),
                Text(
                  'Chọn chân duyệt tiếp theo',
                  style: CommonTextStyle.textStyleFontLatoNormal,
                ),
              ],
            ),
            const SizedBox(height: mPadding),
            CustomDropDownButton(
              label: 'TTHT QLTS',
              listUser: listQLTS ?? [],
              selectedUserName: userQLTSSelected,
              onChanged: (userQlts) {
                onChangedQlts(userQlts);
              },
            ),
            const SizedBox(height: mPadding),
            CustomDropDownButton(
              label: 'Đội trưởng xe',
              listUser: listUserCarLeader ?? [],
              selectedUserName: userLeaderSelected,
              onChanged: (userLeader) {
                onChangedCarLeader(userLeader);
              },
            ),
            const SizedBox(height: mPadding),
            CustomDropDownButton(
              label: 'Thủ trưởng quản lý xe',
              listUser: listUserCarCaptain ?? [],
              selectedUserName: userCaptainSelected,
              onChanged: (userCaptain) {
                onChangedCarCaptain(userCaptain);
              },
            ),
          ],
        ),
      );
}

// ignore: must_be_immutable
class CustomDropDownButton extends StatefulWidget {
  final String label;

  final List<LstApprovePersons> listUser;

  String selectedUserName = '';

  final ValueChanged<LstApprovePersons> onChanged;

  CustomDropDownButton({Key key, this.label, this.listUser, this.selectedUserName, this.onChanged})
      : super(key: key);

  @override
  _CustomDropDownButtonState createState() => _CustomDropDownButtonState();
}

class _CustomDropDownButtonState extends State<CustomDropDownButton> {

  LstApprovePersons selectedUser;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(widget.label, style: CommonTextStyle.textStyleFontLatoNormal),
        const SizedBox(height: mPaddingXXSmall),
        Container(
          padding: const EdgeInsets.only(left: mPadding),
          decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: const BorderRadius.all(
                Radius.circular(mRadiusSmall),
              ),
              border: Border.all(color: AppThemes.colorViettelGray2)),
          child: DropdownButton<String>(
            isExpanded: true,
            elevation: 20,
            dropdownColor: AppThemes.colorViettelGray3,
            focusColor: AppThemes.colorViettelRed02,
            value: (widget.selectedUserName ?? '').isNotEmpty ? widget.selectedUserName : widget.listUser.length > 0 ? widget.listUser[0].name : 'A',
            underline: const SizedBox(),
            items: widget.listUser.map((LstApprovePersons user) {
              return DropdownMenuItem<String>(
                value: user.name ?? '',
                child: Text(
                  user.name,
                  style: CommonTextStyle.textStyleFontLatoNormal,
                ),
              );
            }).toList(),
            onChanged: (userName) {
              setState(() {
                widget.selectedUserName = userName;
              });
              widget.listUser.forEach((user) {
                if (userName == user.name) {
                  widget.onChanged(user);
                }
              });
            },
          ),
        ),
      ],
    );
  }
}
