import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:trackcarvcc/constants/constant_value.dart';
import 'package:trackcarvcc/constants/constants.dart';
import 'package:trackcarvcc/constants/style/style.dart';
import 'package:trackcarvcc/controllers/order_car_creation_controller.dart';
import 'package:trackcarvcc/repository/api/api.dart';
import 'package:trackcarvcc/ui/crave_car/view/crave_car_page.dart';
import 'package:trackcarvcc/ui/crave_car/view/list_crave_car_page.dart';
import 'package:trackcarvcc/ui/engineering_date/view/list_engineering_date_page.dart';
import 'package:trackcarvcc/ui/explanation/ui/explanation_page.dart';
import 'package:trackcarvcc/ui/home/<USER>';
import 'package:trackcarvcc/ui/menu/widget/menu_page_nav_item.dart';
import 'package:trackcarvcc/ui/ui.dart';

enum NavAppPageScreen {
  HOME_PAGE,
  CAR_ORDER_CREATION,
  LIST_BOOK_CAR,
  BOOK_CAR_APPROVAL,
  ARRANGE_CAR_MANAGER,
  BROWSING_CAR_MANAGER,
  ACCEPT_WORK,
  ARRANGE_CAR_TCT,
  BROWSING_CAR_TCT,
  PTGD,
  CAR_MONITORING,
  TTHT_QLTS,
  ENGINEERING_DATE,
  CRAVE_CAR,
  EXPLANATION,
}

extension AppPageScreenExtenion on NavAppPageScreen {
  String get title {
    switch (this) {
      case NavAppPageScreen.HOME_PAGE:
        return 'Theo dõi lộ trình xe';
      case NavAppPageScreen.CAR_ORDER_CREATION:
        return 'Lập phiếu đặt xe';
      case NavAppPageScreen.LIST_BOOK_CAR:
        return 'Danh sách phiếu đã lập';
      case NavAppPageScreen.BOOK_CAR_APPROVAL:
        return 'Duyệt phiếu (Trưởng bộ phận)';
      case NavAppPageScreen.ARRANGE_CAR_MANAGER:
        return 'Xếp xe (Quản lý đội xe)';
      case NavAppPageScreen.BROWSING_CAR_MANAGER:
        return 'Duyệt xe (Thủ trưởng đơn vị)';
      case NavAppPageScreen.ACCEPT_WORK:
        return 'Nhận việc (Lái xe)';
      case NavAppPageScreen.ARRANGE_CAR_TCT:
        return 'Xếp xe (Ban xe TCT)';
      case NavAppPageScreen.BROWSING_CAR_TCT:
        return 'Văn phòng';
      case NavAppPageScreen.PTGD:
        return 'PTGD Chuyên trách';
      case NavAppPageScreen.CAR_MONITORING:
        return 'Giám sát xe';
      case NavAppPageScreen.TTHT_QLTS:
        return 'TT Hạ tầng/QLTS';
      case NavAppPageScreen.ENGINEERING_DATE:
        return 'Ngày kỹ thuật';
      case NavAppPageScreen.CRAVE_CAR:
        return 'Xin xe đối tác';
      case NavAppPageScreen.EXPLANATION:
        return 'Giải trình vượt ngưỡng KM';
      default:
        return '';
    }
  }

  Widget get screen {
    switch (this) {
      case NavAppPageScreen.HOME_PAGE:
        return HomePage();
      case NavAppPageScreen.CAR_ORDER_CREATION:
        Get.lazyPut<CarOrderCreationController>(
              () => CarOrderCreationController(Get.find<Api>()),
          fenix: false,
        );
        return CarOrderCreationPage(
          pageType: PageType.CREATE_ORDER,
        );
      case NavAppPageScreen.LIST_BOOK_CAR:
        return ListBookCarPage(typeMenu: Constants.TYPE_LIST_BOOK_CAR);
      case NavAppPageScreen.BOOK_CAR_APPROVAL:
        return ListBookCarPage(typeMenu: Constants.TYPE_BOOK_CAR_APPROVAL);
      case NavAppPageScreen.ARRANGE_CAR_MANAGER:
        return ListBookCarPage(typeMenu: Constants.TYPE_ARRANGE_CAR_MANAGER);
      case NavAppPageScreen.BROWSING_CAR_MANAGER:
        return ListBookCarPage(typeMenu: Constants.TYPE_BROWSING_CAR_MANAGER);
      case NavAppPageScreen.ACCEPT_WORK:
        return ListBookCarPage(typeMenu: Constants.TYPE_ACCEPT_WORK);
      case NavAppPageScreen.ARRANGE_CAR_TCT:
        return ListBookCarPage(typeMenu: Constants.TYPE_ARRANGE_CAR_TCT);
      case NavAppPageScreen.BROWSING_CAR_TCT:
        return ListBookCarPage(typeMenu: Constants.TYPE_BROWSING_CAR_TCT);
      case NavAppPageScreen.PTGD:
        return ListBookCarPage(typeMenu: Constants.TYPE_PTGD);
      case NavAppPageScreen.CAR_MONITORING:
        return CarMonitoringPage();
      case NavAppPageScreen.TTHT_QLTS:
        return ListBookCarPage(typeMenu: Constants.TYPE_TTHT_QLTS);
      case NavAppPageScreen.ENGINEERING_DATE:
        return ListOrderRequestPage();
      case NavAppPageScreen.CRAVE_CAR:
        return ListCraveCarPage();
      case NavAppPageScreen.EXPLANATION:
        return ExplanationPage();
      // return CraveCarPage(
      //   pageType: PageTypeCraveCar.CREATE_ORDER,
      // );
      default:
        return Container();
    }
  }

  Widget icon(Color color) {
    switch (this) {
      case NavAppPageScreen.HOME_PAGE:
        return Icon(Icons.timeline, color: color);
      case NavAppPageScreen.CAR_ORDER_CREATION:
        return Icon(Icons.add_circle_outline, color: color);
      case NavAppPageScreen.LIST_BOOK_CAR:
        return Icon(Icons.format_list_numbered_outlined, color: color);
      case NavAppPageScreen.BOOK_CAR_APPROVAL:
        return Icon(Icons.ballot_outlined, color: color);
      case NavAppPageScreen.ARRANGE_CAR_MANAGER:
        return Icon(Icons.local_car_wash_outlined, color: color);
      case NavAppPageScreen.BROWSING_CAR_MANAGER:
        return Icon(Icons.ballot_outlined, color: color);
      case NavAppPageScreen.ACCEPT_WORK:
        return Icon(Icons.work_outline, color: color);
      case NavAppPageScreen.ARRANGE_CAR_TCT:
        return Icon(Icons.local_car_wash_outlined, color: color);
      case NavAppPageScreen.BROWSING_CAR_TCT:
        return Icon(Icons.ballot_outlined, color: color);
      case NavAppPageScreen.PTGD:
        return Icon(Icons.format_list_numbered_outlined, color: color);
      case NavAppPageScreen.CAR_MONITORING:
        return Icon(Icons.format_list_numbered_outlined, color: color);
      case NavAppPageScreen.TTHT_QLTS:
        return Icon(Icons.ballot_outlined, color: color);
      case NavAppPageScreen.ENGINEERING_DATE:
        return Icon(Icons.engineering, color: color);
      case NavAppPageScreen.CRAVE_CAR:
        return Image.asset(crave_car);
      case NavAppPageScreen.EXPLANATION:
        return Icon(Icons.extension, color: color);
      default:
        return Container();
    }
  }
}

abstract class NavAppPageManagerDelegate {
  void updateIndex(int index, bool shouldPopNavDrawer);
}

class NavAppPageManager {
  static NavAppPageManager shared = NavAppPageManager();
  int currentIndex = 0;

  Widget get currentPage {
    return (currentIndex < pages.length) ? pages[currentIndex] : Container();
  }

  NavAppPageManagerDelegate delegate;

  List<NavAppPageScreen> pageTypes = [
    NavAppPageScreen.HOME_PAGE,
    NavAppPageScreen.CAR_ORDER_CREATION,
    NavAppPageScreen.LIST_BOOK_CAR,
    NavAppPageScreen.BOOK_CAR_APPROVAL,
    NavAppPageScreen.ARRANGE_CAR_MANAGER,
    NavAppPageScreen.BROWSING_CAR_MANAGER,
    NavAppPageScreen.ACCEPT_WORK,
    NavAppPageScreen.ARRANGE_CAR_TCT,
    NavAppPageScreen.BROWSING_CAR_TCT,
    NavAppPageScreen.PTGD,
    NavAppPageScreen.CAR_MONITORING,
    NavAppPageScreen.TTHT_QLTS,
    NavAppPageScreen.ENGINEERING_DATE,
    NavAppPageScreen.CRAVE_CAR,
    NavAppPageScreen.EXPLANATION,
  ];

  List<Widget> get pages {
    return pageTypes.map((e) => e.screen).toList();
  }

  List<NavAppPageScreen> get pagesTypes => pageTypes;

  set pagesTypes(List<NavAppPageScreen> listPages) {
    listPages.sort((a, b) => a.index.compareTo(b.index));
    pageTypes = listPages;
  }

  List<Widget> get menus {
    return pageTypes
        .map((e) => MenuPageNavItem(
              icon: e.icon(currentIndex == e.index ? AppThemes.colorViettelRed : Colors.black),
              title: e.title,
              color: Colors.black,
              isSelected: currentIndex == e.index,
              onTap: () {
                moveToPage(e, shouldPopNavDrawer: true);
              },
            ))
        .toList();
  }

  void moveToPage(NavAppPageScreen page, {bool shouldPopNavDrawer}) {
    final index = pageTypes.indexOf(page);
    currentIndex = index;
    delegate?.updateIndex(index, shouldPopNavDrawer);
  }
}
