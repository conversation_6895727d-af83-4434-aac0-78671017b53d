import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:trackcarvcc/constants/constant_value.dart';
import 'package:trackcarvcc/repository/preferences/pref.dart';
import 'package:trackcarvcc/ui/menu/menu_page.dart';

import 'nav_manager.dart';

/// HomePage manages all child pages which in Navigation drawer and routes between pages
class AppNavigationPage extends StatefulWidget {
  @override
  _AppNavigationPageState createState() => _AppNavigationPageState();
}

class _AppNavigationPageState extends State<AppNavigationPage> with NavAppPageManagerDelegate {
  DateTime currentBackPressTime;
  PageController _controller;

  @override
  void initState() {
    _initNavigation();
    super.initState();
  }

  _initNavigation() {
    _controller = PageController(initialPage: 0);
    NavAppPageManager.shared.delegate = this;
    final userProfile = DataCenter.shared().getUserInfo();
    final List<NavAppPageScreen> _pageTypes = [
      NavAppPageScreen.HOME_PAGE,
    ];
    // check display menu corresponding role of user
    List<String> _roles = userProfile.roleCode != null ? userProfile.roleCode.split(";") : [];
    for (var roleCode in _roles) {
      switch (roleCode) {
        case Constants.NHANVIEN:
          _pageTypes.addAll([
            NavAppPageScreen.CAR_ORDER_CREATION,
            NavAppPageScreen.CRAVE_CAR,
            NavAppPageScreen.LIST_BOOK_CAR,
          ]);
          break;
        case Constants.TRUONGPHONG:
          _pageTypes.addAll([
            NavAppPageScreen.BOOK_CAR_APPROVAL,
          ]);
          break;
        case Constants.DOITRUONGXE:
          _pageTypes.addAll([
            NavAppPageScreen.ARRANGE_CAR_MANAGER,
            NavAppPageScreen.CAR_MONITORING,
            NavAppPageScreen.ENGINEERING_DATE,
            NavAppPageScreen.EXPLANATION,
          ]);
          break;
        case Constants.THUTRUONGXE:
          _pageTypes.addAll([
            NavAppPageScreen.BROWSING_CAR_MANAGER,
            NavAppPageScreen.CAR_MONITORING,
          ]);
          break;
        case Constants.LAIXE:
          _pageTypes.addAll([
            NavAppPageScreen.ACCEPT_WORK,
            NavAppPageScreen.ENGINEERING_DATE,
            NavAppPageScreen.EXPLANATION,
          ]);
          break;
        case Constants.BANXETCT:
          _pageTypes.addAll([
            NavAppPageScreen.ARRANGE_CAR_TCT,
            NavAppPageScreen.CAR_MONITORING,
          ]);
          break;
        case Constants.TPHANHCHINH:
          _pageTypes.addAll([
            NavAppPageScreen.BROWSING_CAR_TCT,
          ]);
          break;
        case Constants.PTGDCHUYENTRACH:
          _pageTypes.addAll([
            NavAppPageScreen.PTGD,
          ]);
          break;
        case Constants.TTHTPQLTS:
          _pageTypes.addAll([
            NavAppPageScreen.TTHT_QLTS,
          ]);
          break;
        case Constants.HANHTRINH:
          _pageTypes.addAll([
            NavAppPageScreen.CAR_MONITORING,
          ]);
          break;
        case Constants.PHEDUYETVUOTNGUONG:
          _pageTypes.addAll([
            NavAppPageScreen.CAR_MONITORING,
            NavAppPageScreen.EXPLANATION,
          ]);
          break;
      }
    }
    NavAppPageManager.shared.pagesTypes = _pageTypes.toSet().toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: WillPopScope(
        onWillPop: _onWillPop,
        child: _buildBody(),
      ),
      drawer: MenuPage(),
    );
  }

  Widget _buildBody() {
    return PageView.builder(
      controller: _controller,
      physics: NeverScrollableScrollPhysics(),
      itemCount: NavAppPageManager.shared.pages.length,
      itemBuilder: (context, index) => NavAppPageManager.shared.pages[index],
    );
  }

  @override
  void updateIndex(int index, bool shouldPopNavDrawer) {
    _controller.jumpToPage(index);
    if (shouldPopNavDrawer) {
      Navigator.of(context).pop();
    }
  }

  Future<bool> _onWillPop() {
    final DateTime now = DateTime.now();
    if (currentBackPressTime == null || now.difference(currentBackPressTime) > const Duration(seconds: 2)) {
      currentBackPressTime = now;

      Fluttertoast.showToast(
        msg: 'Chạm lần nữa để thoát',
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.CENTER,
        timeInSecForIosWeb: 1,
        // textColor: Colors.white,
        fontSize: 16.0,
      );
      return Future.value(false);
    }
    return Future.value(true);
  }

  @override
  void dispose() {
    _controller?.dispose();
    super.dispose();
  }
}
