import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:trackcarvcc/constants/images.dart';
import 'package:trackcarvcc/controllers/controllers.dart';
import 'package:trackcarvcc/routes/routes.dart';

const delayLoadingSec = 500;

class SplashPage extends StatefulWidget {
  @override
  _SplashPageState createState() => _SplashPageState();
}

class _SplashPageState extends State<SplashPage> {
  SplashController controller = Get.find<SplashController>();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      Future.delayed(const Duration(milliseconds: delayLoadingSec), () {
        if (controller.hasLogin()) {
          // To Nav page
          Get.offAndToNamed(Routers.nav);
        } else {
          // To Login page
          Get.offAndToNamed(Routers.login);
        }
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          Positioned.fill(
            child: Image.asset(mBgSplash, fit: BoxFit.cover),
          ),
        ],
      ),
    );
  }
}
