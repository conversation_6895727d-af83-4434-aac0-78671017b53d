
import 'package:flutter/material.dart';
import 'package:trackcarvcc/constants/constant_value.dart';
import 'package:trackcarvcc/constants/style/style.dart';
import 'package:url_launcher/url_launcher.dart';

typedef OnSuccess();

// ignore: must_be_immutable
class NotifyUpdateVersionAppDialog extends StatelessWidget {
  NotifyUpdateVersionAppDialog({Key key, this.onSuccess, this.versionApp, this.currentVersion}) : super(key: key);

  final OnSuccess onSuccess;
  final String versionApp;
  final String currentVersion;

  TextEditingController controller = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Dialog(
        backgroundColor: Colors.white,
        elevation: mRadiusSmall,
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: _content(),
        ));
  }

  _content() => Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.end,
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          Text(
            'Bạn đang sử dụng phiên bản cũ ($currentVersion), h<PERSON>y cập nhật '
            'phiên bản mới ($versionApp) để tiếp tục sử dụng.',
            style: CommonTextStyle.textStyleFontLatoNormal,
          ),
          const SizedBox(
            height: mPaddingLarge,
          ),
          TextButton(
            child: Text('Xác nhận'),
            onPressed: () {
              makeAction().then((value) => onSuccess());
            },
          ),
          // CommonButton(
          //   title: 'OK',
          //   widthButton: 110,
          //   heightButton: 45,
          //   textColor: Colors.white,
          //   bgColor: AppThemes.colorViettelRed,
          //   onButtonClick: () {
          //     makeAction().then((value) => onSuccess());
          //   },
          // ),
        ],
      );

  Future<void> makeAction() async {
    // if (Platform.isAndroid) {
    //   // Android-specific code
    //   if (await canLaunch(Constants.APP_URL_ANDROID)) {
    await launch(Constants.APP_URL);
    //   } else {
    //     showErrorToast(error: 'Có lỗi xảy ra');
    //   }
    // } else if (Platform.isIOS) {
    //   // iOS-specific code
    //   if (await canLaunch(Constants.APP_URL_IOS)) {
    //     await launch(Constants.APP_URL_IOS);
    //   } else {
    //     showErrorToast(error: 'Có lỗi xảy ra');
    //   }
    // }
  }
}
