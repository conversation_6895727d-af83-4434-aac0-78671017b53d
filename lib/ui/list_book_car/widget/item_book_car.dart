import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:trackcarvcc/constants/constant_value.dart';
import 'package:trackcarvcc/controllers/controllers.dart';
import 'package:trackcarvcc/models/models.dart';
import 'package:trackcarvcc/routes/routes.dart';
import 'package:trackcarvcc/ui/car_order_creation/car_order_creation_page.dart';
import 'package:trackcarvcc/widget/common_button.dart';

class BookCarItem extends StatelessWidget {
  final LstBookCarDto item;
  final int index;
  final int typeMenu;

  const BookCarItem({Key key, @required this.typeMenu, this.item, this.index})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    final ListBookCarController controller = Get.find<ListBookCarController>();
    return Card(
      color: Colors.white,
      elevation: 10,
      child: Padding(
        padding: const EdgeInsets.all(10.0),
        child: Column(
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  index == null ? '' : '$index.',
                  style: TextStyle(
                    color: Color(0xff1daaa5),
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(width: 4),
                Text(
                  item?.code ?? '',
                  style: TextStyle(color: Color(0xff2a2a2a), fontSize: 16),
                ),
                const Spacer(),
                Icon(
                  Icons.radio_button_checked,
                  color: getStatusIconColor(),
                  size: 16,
                )
              ],
            ),
            const SizedBox(height: 8),
            Container(
              color: Color(0xfff9f9f9),
              // height: 100,
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Expanded(
                    flex: 1,
                    child: Padding(
                      padding: const EdgeInsets.all(12.0),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Xuất phát',
                            style: TextStyle(
                              color: Color(0xff969696),
                              fontSize: 14,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            item?.fromAddress ?? '',
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            style: TextStyle(
                              color: Colors.black,
                              fontSize: 14,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            item?.startTime ?? '',
                            style: TextStyle(
                              color: Color(0xffd58431),
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  Container(width: 1, height: 90, color: Color(0xFFE0E0E0)),
                  Expanded(
                    flex: 1,
                    child: Padding(
                      padding: const EdgeInsets.all(12.0),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Đích đến',
                            style: TextStyle(
                              color: Color(0xff969696),
                              fontSize: 14,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            item?.toAddress ?? '',
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            style: TextStyle(
                              color: Colors.black,
                              fontSize: 14,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            item?.endTime ?? '',
                            style: TextStyle(
                              color: Color(0xffd58431),
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
            Offstage(
              offstage: typeMenu == Constants.TYPE_ACCEPT_WORK,
              child: _buildItem('Người tạo', item?.fullName ?? ''),
            ),
            Divider(color: Color(0xFFE0E0E0), height: 1),
            Offstage(
              offstage: showStatus(),
              child: _buildItem('Trạng thái', getStatus(),
                  color: getStatusColor()),
            ),
            Offstage(
              offstage: !showReason(),
              child: Divider(color: Color(0xFFE0E0E0), height: 1),
            ),
            Offstage(
              offstage: !showReason(),
              child: _buildItem('Lý do', getReason()),
            ),
            Offstage(
              offstage: !showReason(),
              child: Divider(color: Color(0xFFE0E0E0), height: 1),
            ),
            Offstage(
              offstage: typeMenu != Constants.TYPE_ACCEPT_WORK,
              child: _buildItem('Người yêu cầu', item?.managerCarName ?? ''),
            ),
            if(typeMenu == Constants.TYPE_LIST_BOOK_CAR)
              CommonButton(
                title: 'Sao chép',
                textColor: Colors.white,
                heightButton: 50,
                onButtonClick: () async {
                  var result = await Get.toNamed(Routers.list_car_creation,
                      arguments: {
                        Constants.KEY_BOOK_CAR: item,
                        "type": PageType.COPY_ORDER
                      });
                  if(result ?? false){
                    controller.fetchData();
                  }
                },
              )
          ],
        ),
      ),
    );
  }

  Widget _buildItem(String label, String value, {Color color}) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(12, 12, 24, 12),
      child: Row(
        children: [
          Text(
            label ?? '',
            style: TextStyle(
              color: Color(0xff969696),
              fontSize: 14,
            ),
          ),
          Spacer(),
          Text(
            value ?? '',
            style: TextStyle(
              color: color ?? Colors.black,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  String getStatus() {
    switch (typeMenu) {
      case Constants.TYPE_ARRANGE_CAR_MANAGER:
        switch (itemStatus) {
          case "1":
            return 'Đang chờ xếp';
          case "2":
            return 'Đã được xếp';
          case "3":
            return 'Từ chối';
          case "4":
            return 'Yêu cầu sửa';
          default:
            return '';
        }
        break;
      case Constants.TYPE_ACCEPT_WORK:
        if (item.typeBookCar == "1" || item.typeBookCar == "5") {
          if (itemStatus == "3") {
            return 'Đã từ chối';
          } else if (itemStatus == "4") {
            return 'Yêu cầu sửa';
          } else if (itemStatus == "5") {
            return 'Đã đóng lệnh';
          } else if (itemStatus == "2" && item.statusManagerCar == "2") {
            return 'Chờ đóng lệnh';
          } else {
            return 'Đã được duyệt';
          }
        } else {
          if (itemStatus == "3") {
            return 'Đã từ chối';
          } else if (itemStatus == "4") {
            return 'Yêu cầu sửa';
          } else if (itemStatus == "5") {
            return 'Đã đóng lệnh';
          } else {
            return 'Đã được duyệt';
          }
        }
        break;
      case Constants.TYPE_BOOK_CAR_APPROVAL:
      case Constants.TYPE_ARRANGE_CAR_TCT:
      case Constants.TYPE_TTHT_QLTS:
      case Constants.TYPE_BROWSING_CAR_TCT:
      case Constants.TYPE_BROWSING_CAR_MANAGER:
      default:
        switch (itemStatus) {
          case "1":
            return 'Đang chờ duyệt';
          case "2":
            return 'Đã được duyệt';
          case "3":
            return 'Từ chối';
          case "4":
            return 'Yêu cầu sửa';
          case "5":
            return typeMenu == Constants.TYPE_BOOK_CAR_APPROVAL
                ? 'Đã đóng lệnh'
                : 'Nhân viên đóng lệnh';
          case "6":
            return 'Lái xe đóng lệnh';
          case "7":
            return 'Ứng cứu thông tin';
          default:
            return '';
        }
    }
  }

  Color getStatusColor() {
    if (typeMenu == Constants.TYPE_ACCEPT_WORK) {
      if (item.typeBookCar == "1" || item.typeBookCar == "5") {
        if (itemStatus == "3") {
          return Colors.red;
        } else if (itemStatus == "4") {
          return Color(0xFFFFA726);
        } else if (itemStatus == "5") {
          return Colors.green;
        } else if (itemStatus == "2" && item.statusManagerCar == "2") {
          return Colors.grey;
        } else {
          return Colors.green;
        }
      } else {
        if (itemStatus == "3") {
          return Colors.red;
        } else if (itemStatus == "4") {
          return Color(0xFFFFA726);
        } else if (itemStatus == "5") {
          return Color(0xff8e8e8e);
        } else {
          return Colors.green;
        }
      }
    } else {
      switch (itemStatus) {
        case "1":
        case "5":
        case "6":
        case "7":
          return Colors.grey;
          break;
        case "2":
          return Colors.green;
          break;
        case "3":
          return Colors.red;
          break;
        case "4":
          return Color(0xFFFFA726);
          break;
        default:
          return Colors.black;
          break;
      }
    }
  }

  String get itemStatus {
    switch (typeMenu) {
      case Constants.TYPE_ARRANGE_CAR_MANAGER:
        return item.statusCaptainCar;
      case Constants.TYPE_ARRANGE_CAR_TCT:
        return item.statusDriverBoard;
      case Constants.TYPE_TTHT_QLTS:
        return item.statusTthtPqlts;
      case Constants.TYPE_BROWSING_CAR_TCT:
        return item.statusAdministrative;
      case Constants.TYPE_BROWSING_CAR_MANAGER:
        return item.statusManagerCar;
      case Constants.TYPE_ACCEPT_WORK:
        return item.statusDriver;
      case Constants.TYPE_BOOK_CAR_APPROVAL:
        return item.statusManage;
      default:
        return item.status;
    }
  }

  Color getStatusIconColor() {
    switch (itemStatus) {
      case "1":
        return Color(0xFFBBB2BB);
        break;
      case "2":
        return Color(0xFF1daaa5);
        break;
      case "3":
        return Color(0xFFFF0000);
        break;
      case "4":
        return Color(0xFFFFA726);
        break;
      case "5":
        return Color(0xFFBBE5E4);
        break;
      case "6":
        return Color(0xFF60C3C0);
        break;
      case "7":
        return Color(0xFF60C3C0);
        break;
      default:
        return Color(0xFF1daaa5);
        break;
    }
  }

  bool showStatus() {
    return (typeMenu == Constants.TYPE_ACCEPT_WORK &&
        isEmptyOrNull(item.statusDriver));
  }

  bool showReason() {
    bool isShow = false;
    switch (typeMenu) {
      case Constants.TYPE_ARRANGE_CAR_MANAGER:
        switch (item.statusCaptainCar) {
          case "1":
          case "2":
            isShow = false;
            break;
          case "3":
          case "4":
          default:
            isShow = true;
            break;
        }
        return isShow && !isEmptyOrNull(item.reasonCaptainCar);
      case Constants.TYPE_ARRANGE_CAR_TCT:
        switch (item.statusDriverBoard) {
          case "1":
            isShow = false;
            break;
          case "2":
          case "3":
          case "4":
          default:
            isShow = true;
            break;
        }
        return isShow && !isEmptyOrNull(item.reasonDriverBoard);
      case Constants.TYPE_TTHT_QLTS:
        switch (item.statusTthtPqlts) {
          case "1":
            isShow = false;
            break;
          case "2":
          case "3":
          case "4":
          default:
            isShow = true;
            break;
        }
        return isShow && !isEmptyOrNull(item.reasonTthtPqlts);
      case Constants.TYPE_BROWSING_CAR_TCT:
        switch (item.statusAdministrative) {
          case "1":
            isShow = false;
            break;
          case "2":
          case "3":
          case "4":
          default:
            isShow = true;
            break;
        }
        return isShow && !isEmptyOrNull(item.reasonAdministrative);
      case Constants.TYPE_BROWSING_CAR_MANAGER:
        switch (item.statusManagerCar) {
          case "1":
            isShow = false;
            break;
          case "2":
          case "3":
          case "4":
          default:
            isShow = true;
            break;
        }
        return isShow && !isEmptyOrNull(item.reasonManagerCar);
      case Constants.TYPE_ACCEPT_WORK:
        return false;
      case Constants.TYPE_BOOK_CAR_APPROVAL:
        switch (item.statusManage) {
          case "1":
          case "5":
            isShow = false;
            break;
          case "2":
          case "3":
          case "4":
          default:
            isShow = true;
            break;
        }
        return isShow && !isEmptyOrNull(item.reasonManage);
      default:
        switch (item.status) {
          case "1":
          case "5":
          case "6":
          case "7":
            isShow = false;
            break;
          case "2":
          case "3":
          case "4":
          default:
            isShow = true;
            break;
        }
        return isShow && !isEmptyOrNull(item.reasonManage) ||
            !isEmptyOrNull(item.reasonCaptainCar) ||
            !isEmptyOrNull(item.reasonManagerCar) ||
            !isEmptyOrNull(item.reasonDriverBoard) ||
            !isEmptyOrNull(item.reasonAdministrative) ||
            !isEmptyOrNull(item.reasonViceManager) ||
            !isEmptyOrNull(item.reasonTthtPqlts);
    }
  }

  bool isEmptyOrNull(String s) => s == null || s.isEmpty;

  String getReason() {
    switch (typeMenu) {
      case Constants.TYPE_ARRANGE_CAR_MANAGER:
        return item.reasonCaptainCar;
      case Constants.TYPE_ARRANGE_CAR_TCT:
        return item.reasonDriverBoard;
      case Constants.TYPE_TTHT_QLTS:
        return item.reasonTthtPqlts;
      case Constants.TYPE_BROWSING_CAR_TCT:
        return item.reasonAdministrative;
      case Constants.TYPE_BROWSING_CAR_MANAGER:
        return item.reasonManagerCar;
      case Constants.TYPE_ACCEPT_WORK:
        return '';
      case Constants.TYPE_BOOK_CAR_APPROVAL:
        return item.reasonManage;
      default:
        if (!isEmptyOrNull(item.reasonManage)) {
          return item.reasonManage;
        } else if (!isEmptyOrNull(item.reasonCaptainCar)) {
          return item.reasonCaptainCar;
        } else if (!isEmptyOrNull(item.reasonManagerCar)) {
          return item.reasonManagerCar;
        } else if (!isEmptyOrNull(item.reasonDriverBoard)) {
          return item.reasonDriverBoard;
        } else if (!isEmptyOrNull(item.reasonAdministrative)) {
          return item.reasonAdministrative;
        } else if (!isEmptyOrNull(item.reasonViceManager)) {
          return item.reasonViceManager;
        } else if (!isEmptyOrNull(item.reasonTthtPqlts)) {
          return item.reasonTthtPqlts;
        } else {
          return '';
        }
    }
  }
}
