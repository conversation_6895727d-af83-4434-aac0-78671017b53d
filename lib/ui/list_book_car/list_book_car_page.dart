import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/get_state_manager.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:trackcarvcc/constants/constants.dart';
import 'package:trackcarvcc/constants/style/style.dart';
import 'package:trackcarvcc/controllers/controllers.dart';
import 'package:trackcarvcc/models/models.dart';
import 'package:trackcarvcc/routes/routes.dart';
import 'package:trackcarvcc/ui/list_book_car/widget/item_book_car.dart';
import 'package:trackcarvcc/ui/login/widget/custom_text_form_field.dart';
import 'package:trackcarvcc/widget/circle_loading.dart';
import 'package:trackcarvcc/widget/infinity_scroll.dart';

class ListBookCarPage extends StatefulWidget {
  ListBookCarPage({@required this.typeMenu});

  final int typeMenu;

  @override
  _ListBookCarPageState createState() => _ListBookCarPageState();
}

class _ListBookCarPageState extends State<ListBookCarPage> with AutomaticKeepAliveClientMixin {
  final ListBookCarController controller = Get.find<ListBookCarController>();

  RefreshController _refreshController = RefreshController(initialRefresh: false);

  void _onRefresh() async {
    controller.fetchData();
    _refreshController.refreshCompleted();
  }

  void _onLoading() async {
    // controller.fetchData();
    // _refreshController.refreshCompleted();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return GetBuilder<ListBookCarController>(
      initState: (_) {
        controller.clear();
        controller.typeMenu = widget.typeMenu;
        controller.fetchData();
      },
      builder: (_) {
        return GestureDetector(
          onTap: () {
            FocusScope.of(context).requestFocus(FocusNode());
          },
          child: Scaffold(
            appBar: AppBar(
              backgroundColor: AppThemes.colorViettelRed,
              brightness: Brightness.light,
              elevation: 1.0,
              leading: IconButton(
                icon: const Icon(
                  Icons.menu,
                  color: Colors.white,
                ),
                onPressed: () {
                  Scaffold.of(context).openDrawer();
                },
              ),
              centerTitle: true,
              title: Text(
                _getTitle(),
                style: CommonTextStyle.textStyleFontLatoLargeBoldWhite,
              ),
              actions: widget.typeMenu == Constants.TYPE_ACCEPT_WORK
                  ? null
                  : [
                      Obx(
                        () => PopupMenuButton(
                          icon: Icon(
                            Icons.filter_list_sharp,
                            color: Colors.white,
                          ),
                          elevation: 10,
                          initialValue: controller.status.value,
                          onSelected: (value) {
                            if (value == controller.status()) {
                              // value = null;
                              return;
                            }
                            controller.filter(value);
                          },
                          itemBuilder: (context) => _listAction(),
                        ),
                      ),
                    ],
            ),
            backgroundColor: AppThemes.colorViettelGray3,
            body: SafeArea(
              child: Column(
                children: [
                  //Search bar
                  Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: CustomTextFormField(
                      controller: controller.searchController,
                      hintText: "Tìm kiếm",
                      prefixIcon: Icon(Icons.search),
                      onChanged: controller.search,
                    ),
                  ),
                  Expanded(
                    child: Obx(() {
                      if (controller.appState() == AppState.LOADING) {
                        return _buildLoading();
                      }

                      if (controller.appState() == AppState.ERROR) {
                        return _buildErrorWidget();
                      }
                      if (controller.listBookCars.isEmpty) {
                        return _buildNoDataWidget();
                      } else {
                        return InfinityScroll(
                          refreshController: _refreshController,
                          onRefresh: _onRefresh,
                          onLoading: _onLoading,
                          padding: EdgeInsets.symmetric(horizontal: 8),
                          child: Column(
                            children: controller.listBookCars
                                .map(
                                  (item) => GestureDetector(
                                    onTap: () {
                                      _onItemClick(item);
                                    },
                                    child: Padding(
                                      padding: const EdgeInsets.only(bottom: 8),
                                      child: BookCarItem(
                                        typeMenu: widget.typeMenu,
                                        index: controller.listBookCars.indexOf(item) + 1,
                                        item: item,
                                      ),
                                    ),
                                  ),
                                )
                                .toList(),
                          ),
                        );
                      }
                    }),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildLoading() => Container(
        color: AppThemes.colorViettelGray3,
        child: LoadingCircle(),
      );

  Widget _buildNoDataWidget() => Center(
        child: Text("Không có dữ liệu!"),
      );

  Widget _buildErrorWidget() => Center(
        child: Text("Có lỗi xảy ra. Vui lòng thử lại sau."),
      );

  String _getTitle() {
    switch (widget.typeMenu) {
      case Constants.TYPE_LIST_BOOK_CAR:
        return 'Danh sách phiếu đã lập';
      case Constants.TYPE_BOOK_CAR_APPROVAL:
        return 'Duyệt phiếu (Trưởng bộ phận)';
      case Constants.TYPE_ARRANGE_CAR_MANAGER:
        return 'Xếp xe (Quản lý đội xe)';
      case Constants.TYPE_BROWSING_CAR_MANAGER:
        return 'Duyệt xe (Thủ trưởng đơn vị)';
      case Constants.TYPE_ACCEPT_WORK:
        return 'Nhận việc (Lái xe)';
      case Constants.TYPE_ARRANGE_CAR_TCT:
        return 'Xếp xe (Ban xe TCT)';
      case Constants.TYPE_BROWSING_CAR_TCT:
        return 'Văn phòng';
      case Constants.TYPE_PTGD:
        return 'PTGD Chuyên trách';
      case Constants.TYPE_TTHT_QLTS:
        return 'TT Hạ tầng/QLTS';
      default:
        return '';
    }
  }

  List<PopupMenuEntry<String>> _listAction() {
    switch (widget.typeMenu) {
      case Constants.TYPE_LIST_BOOK_CAR:
      case Constants.TYPE_BOOK_CAR_APPROVAL:
        return [
          _itemPopupMenu(Color(0xFF1daaa5), 'Đã được duyệt', '2'),
          _itemPopupMenu(Color(0xFFFF0000), 'Từ chối', '3'),
          _itemPopupMenu(Color(0xFFFFA726), 'Yêu cầu sửa', '4'),
          _itemPopupMenu(Color(0xFFBBB2BB), 'Đang chờ duyệt', '1'),
          _itemPopupMenu(Color(0xFFBBE5E4), 'Nhân viên đóng lệnh', '5'),
          _itemPopupMenu(Color(0xFF60C3C0), 'Lái xe đóng lệnh', '6'),
        ];
      case Constants.TYPE_BROWSING_CAR_MANAGER:
      case Constants.TYPE_ARRANGE_CAR_TCT:
      case Constants.TYPE_BROWSING_CAR_TCT:
      case Constants.TYPE_PTGD:
      case Constants.TYPE_TTHT_QLTS:
        return [
          _itemPopupMenu(Color(0xFF1daaa5), 'Đã được duyệt', '2'),
          _itemPopupMenu(Color(0xFFFF0000), 'Từ chối', '3'),
          _itemPopupMenu(Color(0xFFFFA726), 'Yêu cầu sửa', '4'),
          _itemPopupMenu(Color(0xFFBBB2BB), 'Đang chờ duyệt', '1'),
        ];
      case Constants.TYPE_ARRANGE_CAR_MANAGER:
        return [
          _itemPopupMenu(Color(0xFF1daaa5), 'Đã được xếp', '2'),
          _itemPopupMenu(Color(0xFFFF0000), 'Từ chối', '3'),
          _itemPopupMenu(Color(0xFFFFA726), 'Yêu cầu sửa', '4'),
          _itemPopupMenu(Color(0xFFBBB2BB), 'Đang chờ xếp', '1'),
        ];
      default:
        return [];
    }
  }

  // Build custom item for popup menu
  PopupMenuItem<String> _itemPopupMenu(Color color, String text, String value) {
    return PopupMenuItem<String>(
      // return value of item
      value: value,
      child: Row(
        children: [
          Icon(
            Icons.radio_button_checked,
            color: color,
            size: 16,
          ),
          const SizedBox(width: 8),
          Text(text),
        ],
      ),
    );
  }

  @override
  bool get wantKeepAlive => false;

  _onItemClick(LstBookCarDto bookCarDto) {
    switch (widget.typeMenu) {
      case Constants.TYPE_ARRANGE_CAR_MANAGER:
      case Constants.TYPE_BOOK_CAR_APPROVAL:
      case Constants.TYPE_BROWSING_CAR_MANAGER:
      case Constants.TYPE_ARRANGE_CAR_TCT:
      case Constants.TYPE_BROWSING_CAR_TCT:
      case Constants.TYPE_PTGD:
      case Constants.TYPE_TTHT_QLTS:
        _goToDetail(bookCarDto);
        break;
      case Constants.TYPE_LIST_BOOK_CAR:
      default:
        String status = bookCarDto.status;
        String typeBookCar = bookCarDto.typeBookCar;
        String statusManagerCar = bookCarDto.statusManagerCar;
        String statusCaptainCar = bookCarDto.statusCaptainCar;
        String statusDriverBoard = bookCarDto.statusDriverBoard;
        String statusAdministrative = bookCarDto.statusAdministrative;
        if (status == "1" && statusCaptainCar != "2")
          _goToUpdate(bookCarDto);
        else if (status == "4")
          _goToUpdate(bookCarDto);
        else if ((typeBookCar == "1" || typeBookCar == "2") && status == "2" && statusManagerCar == "4")
          _goToUpdate(bookCarDto);
        else if ((typeBookCar == "1" || typeBookCar == "2") && status == "2" && statusCaptainCar == "4")
          _goToUpdate(bookCarDto);
        else if (typeBookCar == "3" && status == "2" && statusCaptainCar == "4")
          _goToUpdate(bookCarDto);
        else if (typeBookCar == "4" && status == "2" && statusDriverBoard == "4")
          _goToUpdate(bookCarDto);
        else if (typeBookCar == "4" && status == "2" && (statusAdministrative != null && statusAdministrative == "4"))
          _goToUpdate(bookCarDto);
        else
          _goToDetail(bookCarDto);
        break;
    }
  }

  _goToDetail(LstBookCarDto bookCarDto) async {
    final result = await Get.toNamed(
      Routers.detail,
      arguments: {Constants.KEY_BOOK_CAR: bookCarDto, Constants.KEY_TYPE_MENU: widget.typeMenu},
    );

    if (result != null && result) {
      controller.handleRefresh();
    }
  }

  _goToUpdate(LstBookCarDto bookCarDto) async {
    final result = await Get.toNamed(Routers.list_car_creation, arguments: {Constants.KEY_BOOK_CAR: bookCarDto});

    if (result != null && result) {
      controller.handleRefresh();
    }
  }
}
