// To parse this JSON data, do
//
//     final explanation = explanationFrom<PERSON>son(jsonString);

import 'dart:convert';

ExplanationDTO explanationFromJson(String str) => ExplanationDTO.fromJson(json.decode(str));

String explanationToJson(ExplanationDTO data) => json.encode(data.toJson());

class ExplanationDTO {
  final int bookCarDayReportId;
  final String bookCarDate;
  final String licenseCar;
  final int explanationStatus;
  final List<Detail> details;
  final double estimateDistance;
  final double actualDistance;
  final String rateExcess;
  final String driverName;
  final String driverCode;
  final String explainReason;
  final String requestReason;
  final String rejectReason;
  final String approvedBy;
  final String approvedAt;
  final String approvedName;
  final String rejectedAt;
  final String rejectedName;
  final bool isExcessTenPercent;
  final bool isRoleCaptainCar;
  final bool isRoleExcessTenPercent;

  ExplanationDTO({
    this.bookCarDayReportId,
    this.bookCarDate,
    this.licenseCar,
    this.explanationStatus,
    this.details,
    this.estimateDistance,
    this.actualDistance,
    this.rateExcess,
    this.driverName,
    this.driverCode,
    this.explainReason,
    this.requestReason,
    this.rejectReason,
    this.approvedBy,
    this.approvedAt,
    this.approvedName,
    this.rejectedAt,
    this.rejectedName,
    this.isExcessTenPercent = false,
    this.isRoleCaptainCar = false,
    this.isRoleExcessTenPercent = false,
  });

  factory ExplanationDTO.fromJson(Map<String, dynamic> json) => ExplanationDTO(
        bookCarDayReportId: json["bookCarDayReportId"],
        bookCarDate: json["bookCarDate"],
        licenseCar: json["licenseCar"],
        explanationStatus: json["explanationStatus"],
        details: List<Detail>.from(json["details"].map((x) => Detail.fromJson(x))),
        estimateDistance: json["estimateDistance"].toDouble(),
        actualDistance: json["actualDistance"].toDouble(),
        rateExcess: json["rateExcess"],
        driverName: json["driverName"],
        driverCode: json["driverCode"],
        explainReason: json["explainReason"],
        requestReason: json["requestReason"],
        rejectReason: json["rejectReason"],
        approvedBy: json["approvedBy"],
        approvedAt: json["approvedAt"],
        approvedName: json["approvedName"],
        rejectedName: json["rejectedName"],
        rejectedAt: json["rejectedAt"],
        isExcessTenPercent: json["isExcessTenPercent"],
        isRoleCaptainCar: json["isRoleCaptainCar"],
        isRoleExcessTenPercent: json["isRoleExcessTenPercent"],
      );

  Map<String, dynamic> toJson() => {
        "bookCarDayReportId": bookCarDayReportId,
        "bookCarDate": bookCarDate,
        "licenseCar": licenseCar,
        "explanationStatus": explanationStatus,
        "details": List<dynamic>.from(details.map((x) => x.toJson())),
        "estimateDistance": estimateDistance,
        "actualDistance": actualDistance,
        "rateExcess": rateExcess,
        "driverName": driverName,
        "explainReason": explainReason,
        "approvedBy": approvedBy,
        "approvedAt": approvedAt,
        "approvedName": approvedName,
        "driverCode": driverCode,
        "isExcessTenPercent": isExcessTenPercent,
        "isRoleCaptainCar": isRoleCaptainCar,
        "isRoleExcessTenPercent": isRoleExcessTenPercent,
        "requestReason": requestReason,
        "rejectReason": rejectReason,
        "rejectedAt": rejectedAt,
        "rejectedName": rejectedName,
      };
}

class Detail {
  final int bookCarDayReportId;
  final String bookCarCode;
  final String driverName;
  final double estimateDistance;

  Detail({
    this.bookCarDayReportId,
    this.bookCarCode,
    this.driverName,
    this.estimateDistance,
  });

  factory Detail.fromJson(Map<String, dynamic> json) => Detail(
        bookCarDayReportId: json["bookCarDayReportId"],
        bookCarCode: json["bookCarCode"],
        driverName: json["driverName"],
        estimateDistance: json["estimateDistance"].toDouble(),
      );

  Map<String, dynamic> toJson() => {
        "bookCarDayReportId": bookCarDayReportId,
        "bookCarCode": bookCarCode,
        "driverName": driverName,
        "estimateDistance": estimateDistance,
      };
}

enum ExplanationStatusEnum {
  all, //-1
  // non, //0
  waiting, //1
  waitingApprove, //2
  reject, //3
  approve, //4
  requestAdditional, //5
}

extension ExplanationStatusEnumExtention on num {
  ExplanationStatusEnum toExplanationStatusEnum() {
    switch (this) {
      case -1:
        return ExplanationStatusEnum.all;
      // case 0:
      //   return ExplanationStatusEnum.non;
      case 1:
        return ExplanationStatusEnum.waiting;
      case 2:
        return ExplanationStatusEnum.waitingApprove;
      case 3:
        return ExplanationStatusEnum.reject;
      case 4:
        return ExplanationStatusEnum.approve;
      case 5:
        return ExplanationStatusEnum.requestAdditional;
      default:
        throw ArgumentError("Invalid value for ExplanationStatusEnum: $this");
    }
  }
}

extension ExplanationStatusEnumExtension on ExplanationStatusEnum {
  String toShortString() {
    switch (this) {
      case ExplanationStatusEnum.all:
        return "Tất cả";
      // case ExplanationStatusEnum.non:
      //   return "Không cần giải trình";
      case ExplanationStatusEnum.waiting:
        return "Chờ lái xe giải trình";
      case ExplanationStatusEnum.waitingApprove:
        return "Chờ phê duyệt";
      case ExplanationStatusEnum.reject:
        return "Từ chối";
      case ExplanationStatusEnum.approve:
        return "Đã phê duyệt";
      case ExplanationStatusEnum.requestAdditional:
        return "Yêu cầu bổ sung thông tin";
      default:
        return "";
    }
  }

  int toInt() {
    switch (this) {
      case ExplanationStatusEnum.all:
        return -1;
      // case ExplanationStatusEnum.non:
      //   return 0;
      case ExplanationStatusEnum.waiting:
        return 1;
      case ExplanationStatusEnum.waitingApprove:
        return 2;
      case ExplanationStatusEnum.reject:
        return 3;
      case ExplanationStatusEnum.approve:
        return 4;
      case ExplanationStatusEnum.requestAdditional:
        return 5;
      default:
        throw ArgumentError("Invalid value for ExplanationStatusEnum: $this");
    }
  }
}
