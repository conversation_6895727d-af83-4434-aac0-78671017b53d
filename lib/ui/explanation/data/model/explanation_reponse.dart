import 'dart:convert';

import 'explanation.dart';

ExplanationResponse explanationResponseFromJson(String str) => ExplanationResponse.fromJson(json.decode(str));

String explanationResponseToJson(ExplanationResponse data) => json.encode(data.toJson());

class ExplanationResponse {
  final int statusCode;
  final String message;
  final List<ExplanationDTO> data;
  final int total;

  ExplanationResponse({
    this.statusCode,
    this.message,
    this.data,
    this.total,
  });

  factory ExplanationResponse.fromJson(Map<String, dynamic> json) => ExplanationResponse(
        statusCode: json["statusCode"],
        message: json["message"],
        data: List<ExplanationDTO>.from(json["data"].map((x) => ExplanationDTO.fromJson(x))),
        total: json["total"],
      );

  Map<String, dynamic> toJson() => {
        "statusCode": statusCode,
        "message": message,
        "data": List<dynamic>.from(data.map((x) => x.toJson())),
        "total": total,
      };
}

ExplanationUpdateStatusRes explanationUpdateStatusResFromJson(String str) => ExplanationUpdateStatusRes.fromJson(json.decode(str));

String explanationUpdateStatusResToJson(ExplanationUpdateStatusRes data) => json.encode(data.toJson());

class ExplanationUpdateStatusRes {
  final int statusCode;
  final String message;
  final dynamic data;
  final int total;

  ExplanationUpdateStatusRes({
    this.statusCode,
    this.message,
    this.data,
    this.total,
  });

  factory ExplanationUpdateStatusRes.fromJson(Map<String, dynamic> json) => ExplanationUpdateStatusRes(
    statusCode: json["statusCode"],
    message: json["message"],
    data: json["data"],
    total: json["total"],
  );

  Map<String, dynamic> toJson() => {
    "statusCode": statusCode,
    "message": message,
    "data": data,
    "total": total,
  };
}
