import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:trackcarvcc/constants/app_state.dart';
import 'package:trackcarvcc/constants/http_code.dart';
import 'package:trackcarvcc/models/models.dart';
import 'package:trackcarvcc/repository/api/api.dart';
import 'package:trackcarvcc/repository/preferences/pref.dart';
import 'package:trackcarvcc/ui/explanation/data/model/explanation.dart';
import 'package:trackcarvcc/ui/explanation/data/model/explanation_reponse.dart';
import 'package:trackcarvcc/helpers/extensions.dart';

class ExplanationViewModel extends GetxController {
  Api api;
  var appState = Rx<AppState>();

  ExplanationViewModel(this.api);

  RxList<ExplanationDTO> explanationDTOs = RxList<ExplanationDTO>();

  TextEditingController explanationController = TextEditingController(text: '');

  Rx<DateTime> fromDate = DateTime(DateTime.now().year, DateTime.now().month - 1).obs;
  Rx<DateTime> toDate = DateTime.now().obs;

  String formatDate(DateTime dateTime) {
    return DateFormat('dd/MM/yyyy').format(dateTime);
  }

  fetchData({
    String driverName ,
    int explanationStatus,
    String licenseCar,
  }) async {
    try {
      appState.value = AppState.LOADING;
      Map<String, dynamic> request = {
        "fromDate": formatDate(fromDate.value),
        "toDate": formatDate(toDate.value),
        "driverName": driverName,
        "explanationStatus": explanationStatus,
        "licenseCar": licenseCar,
        "authenticationInfo": {
          "username": DataCenter.shared().getUserInfo().loginName,
        },
      };

      final response = await api.explanationGetList(request);
      final result = BaseApiResponse.fromJson(response);
      if (result.success) {
        final data = ExplanationResponse.fromJson(result.data);
        if (data.statusCode == 1) {
          explanationDTOs.assignAll(data.data);
          appState.value = AppState.DONE;
        } else {
          appState.value = AppState.ERROR;
        }
      } else {
        appState.value = AppState.ERROR;
      }
    } catch (e) {
      appState.value = AppState.ERROR;
    }
  }

  // explanationUpdateStatus({
  //   int bookCarDayReportId,
  //   ExplanationStatusEnum explanationStatus,
  //   String reason,
  // }) async {
  //   try {
  //     Map<String, dynamic> request = {
  //       "authenticationInfo": {
  //         "username": DataCenter.shared().getUserInfo().loginName,
  //       },
  //       "bookCarDayReportId": bookCarDayReportId,
  //       "explanationStatus": explanationStatus.toInt(),
  //       "updatedBy": DataCenter.shared().getUserInfo().loginName,
  //       // "reason": reason ?? null
  //     };
  //
  //     if (explanationStatus != ExplanationStatusEnum.approve) {
  //       if (reason.isEmpty) {
  //         showErrorToast(error: 'Vui lòng nhập lý do giải trình!');
  //         return;
  //       }
  //       request["reason"] = reason;
  //     }
  //     showLoadingDialog();
  //
  //     final response = await api.explanationUpdateStatus(request);
  //     final result = BaseApiResponse.fromJson(response);
  //     dismissLoadingDialog();
  //     if (result.success) {
  //       final data = ExplanationUpdateStatusRes.fromJson(result.data);
  //       if (data.statusCode == 1) {
  //         explanationController.clear();
  //         Get.back();
  //         showSuccessToast(
  //           msg: explanationStatus == ExplanationStatusEnum.waitingApprove
  //               ? 'Giải trình thành công!'
  //               : explanationStatus == ExplanationStatusEnum.approve
  //                   ? 'Phê duyệt thành công!'
  //                   : explanationStatus == ExplanationStatusEnum.reject
  //                       ? 'Từ chối thành công!'
  //                       : explanationStatus == ExplanationStatusEnum.requestAdditional
  //                           ? 'Đã yêu cầu bổ sung thông tin!'
  //                           : '',
  //         );
  //         fetchData();
  //       } else {
  //         showErrorToast(error: 'Xảy ra lỗi!');
  //       }
  //     } else {
  //       showErrorToast(error: 'Xảy ra lỗi!');
  //     }
  //   } catch (e) {
  //     showErrorToast(error: 'Xảy ra lỗi!');
  //     dismissLoadingDialog();
  //   }
  // }

  explanationUpdateStatus({
    int bookCarDayReportId,
    ExplanationStatusEnum explanationStatus,
    String reason,
  }) async {
    try {
      final request = _buildRequest(bookCarDayReportId, explanationStatus, reason);

      if (_isInvalidReason(explanationStatus, reason)) return;

      showLoadingDialog();

      final response = await api.explanationUpdateStatus(request);
      final result = BaseApiResponse.fromJson(response);

      dismissLoadingDialog();

      if (result.success) {
        _handleSuccess(result, explanationStatus);
      } else {
        showErrorToast(error: 'Xảy ra lỗi!');
      }
    } catch (e) {
      showErrorToast(error: 'Xảy ra lỗi!');
      dismissLoadingDialog();
    }
  }

  Map<String, dynamic> _buildRequest(int bookCarDayReportId, ExplanationStatusEnum explanationStatus, String reason) {
    final request = {
      "authenticationInfo": {
        "username": DataCenter.shared().getUserInfo().loginName,
      },
      "bookCarDayReportId": bookCarDayReportId,
      "explanationStatus": explanationStatus.toInt(),
      "updatedBy": DataCenter.shared().getUserInfo().loginName,
    };

    if (explanationStatus != ExplanationStatusEnum.approve) {
      request["reason"] = reason;
    }

    return request;
  }

  bool _isInvalidReason(ExplanationStatusEnum explanationStatus, String reason) {
    if (explanationStatus != ExplanationStatusEnum.approve && reason.isEmpty) {
      showErrorToast(error: 'Vui lòng nhập lý do giải trình!');
      return true;
    }
    return false;
  }

  void _handleSuccess(BaseApiResponse result, ExplanationStatusEnum explanationStatus) {
    final data = ExplanationUpdateStatusRes.fromJson(result.data);
    if (data.statusCode == 1) {
      explanationController.clear();
      Get.back();
      showSuccessToast(
        msg: _getSuccessMessage(explanationStatus),
      );
      fetchData();
    } else {
      showErrorToast(error: 'Xảy ra lỗi!');
    }
  }

  String _getSuccessMessage(ExplanationStatusEnum explanationStatus) {
    switch (explanationStatus) {
      case ExplanationStatusEnum.waitingApprove:
        return 'Giải trình thành công!';
      case ExplanationStatusEnum.approve:
        return 'Phê duyệt thành công!';
      case ExplanationStatusEnum.reject:
        return 'Từ chối thành công!';
      case ExplanationStatusEnum.requestAdditional:
        return 'Đã yêu cầu bổ sung thông tin!';
      default:
        return '';
    }
  }
}
