import 'package:flutter/material.dart';
import 'package:flutter_datetime_picker/flutter_datetime_picker.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:trackcarvcc/constants/app_state.dart';
import 'package:trackcarvcc/constants/style/indicator.dart';
import 'package:trackcarvcc/constants/style/style.dart';
import 'package:trackcarvcc/repository/preferences/pref.dart';
import 'package:trackcarvcc/routes/routes.dart';
import 'package:trackcarvcc/ui/explanation/data/view_model/explanation_view_model.dart';
import 'package:trackcarvcc/widget/circle_loading.dart';
import 'package:trackcarvcc/widget/common_button.dart';
import 'package:trackcarvcc/widget/form/form_field.dart';
import '../data/model/explanation.dart';

class ExplanationPage extends GetView<ExplanationViewModel> {
  @override
  Widget build(BuildContext context) {
    return GetBuilder(initState: (_) {
      controller.fetchData();
    }, builder: (_) {
      return Scaffold(
        backgroundColor: Color(0xFFF5F5F5),
        appBar: AppBar(
          title: Text('Giải trình vượt ngưỡng cho phép'),
          leading: IconButton(
            icon: const Icon(
              Icons.menu,
              color: Colors.white,
            ),
            onPressed: () {
              Scaffold.of(context).openDrawer();
            },
          ),
          centerTitle: true,
          backgroundColor: Color(0xFFEE0033),
          actions: [
            _buildFilter(context),
          ],
        ),
        body: RefreshIndicator(
          onRefresh: () async {
            controller.fetchData();
          },
          child: Column(
            children: [
              //Search bar
              Obx(
                () => Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  child: Container(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: InkWell(
                            onTap: () {
                              DatePicker.showDatePicker(
                                context,
                                showTitleActions: true,
                                minTime: DateTime(1900, 1, 1),
                                maxTime: DateTime.now(),
                                onChanged: (date) {},
                                onConfirm: (time) {
                                  // controller.fromDate.value = time;
                                  // if (controller.fromDate.value.isAfter(controller.toDate.value)) {
                                  //   controller.toDate.value = controller.fromDate.value;
                                  // }
                                  if (time.isAfter(controller.toDate.value)) {
                                    controller.fromDate.value = controller.toDate.value;
                                    controller.toDate.value = time;
                                  } else {
                                    controller.fromDate.value = time;
                                  }
                                  controller.fetchData();
                                },
                                currentTime: DateTime.now(),
                                locale: LocaleType.vi,
                              );
                            },
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Từ ngày',
                                ),
                                const SizedBox(
                                  height: 6,
                                ),
                                Row(
                                  children: [
                                    Icon(Icons.calendar_today_rounded),
                                    Text((DateFormat('dd/MM/yyyy').format(controller.fromDate.value))),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ),
                        Expanded(
                          child: InkWell(
                            onTap: () {
                              DatePicker.showDatePicker(
                                context,
                                showTitleActions: true,
                                minTime: DateTime(1900, 1, 1),
                                maxTime: DateTime.now(),
                                onChanged: (date) {},
                                onConfirm: (time) {
                                  if (time.isBefore(controller.fromDate.value)) {
                                    controller.toDate.value = controller.fromDate.value;
                                    controller.fromDate.value = time;
                                  } else {
                                    controller.toDate.value = time;
                                  }
                                  controller.fetchData();
                                },
                                currentTime: DateTime.now(),
                                locale: LocaleType.vi,
                              );
                            },
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Đến ngày',
                                ),
                                const SizedBox(
                                  height: 6,
                                ),
                                Row(
                                  children: [
                                    Icon(Icons.calendar_today_rounded),
                                    Text((DateFormat('dd/MM/yyyy').format(controller.toDate.value))),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              Expanded(
                child: Obx(() {
                  if (controller.appState() == AppState.LOADING) {
                    return Container(
                      color: AppThemes.colorViettelGray3,
                      child: LoadingCircle(),
                    );
                  }

                  if (controller.appState() == AppState.ERROR) {
                    return Center(
                      child: Text("Có lỗi xảy ra. Vui lòng thử lại sau."),
                    );
                  }
                  if (controller.explanationDTOs.isEmpty) {
                    return Center(
                      child: Text("Không có dữ liệu!"),
                    );
                  } else {
                    return ListView.separated(
                      itemCount: controller.explanationDTOs.length,
                      padding: EdgeInsets.all(16),
                      separatorBuilder: (context, index) {
                        return SizedBox(height: 12);
                      },
                      itemBuilder: (context, index) {
                        var item = controller.explanationDTOs[index];

                        var explanationStatusEnum = item.explanationStatus.toExplanationStatusEnum();

                        return Container(
                          padding: EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.grey[300]),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    item.bookCarDate ?? '',
                                    style: CommonTextStyle.textStyleFontLatoNormalBold.copyWith(
                                      color: Colors.teal,
                                    ),
                                  ),
                                  IconButton(
                                    icon: Icon(
                                      Icons.visibility,
                                      color: Colors.teal,
                                    ),
                                    onPressed: () {
                                      Get.toNamed(Routers.explanationDetail, arguments: [item, false]);
                                    },
                                  )
                                ],
                              ),
                              Divider(),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    'Biển số xe: ',
                                    style: CommonTextStyle.textStyleFontLatoNormal.copyWith(
                                      color: AppThemes.colorViettelBlack,
                                    ),
                                  ),
                                  Text(
                                    item.licenseCar ?? '',
                                    style: CommonTextStyle.textStyleFontLatoNormal.copyWith(
                                      color: AppThemes.colorViettelBlack,
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(
                                height: 12,
                              ),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    'Lái xe',
                                    style: CommonTextStyle.textStyleFontLatoNormal.copyWith(
                                      color: AppThemes.colorViettelBlack,
                                    ),
                                  ),
                                  Text(
                                    item.driverName ?? '',
                                    style: CommonTextStyle.textStyleFontLatoNormal.copyWith(
                                      color: AppThemes.colorViettelBlack,
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(
                                height: 12,
                              ),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    '% chênh lệch KM',
                                    style: CommonTextStyle.textStyleFontLatoNormal.copyWith(
                                      color: Colors.orange,
                                    ),
                                  ),
                                  Text(
                                    item.rateExcess ?? '',
                                    style: CommonTextStyle.textStyleFontLatoNormal.copyWith(
                                      color: Colors.black,
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(
                                height: 12,
                              ),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    'Số KM dự kiến',
                                    style: CommonTextStyle.textStyleFontLatoNormal.copyWith(
                                      color: AppThemes.colorViettelBlack,
                                    ),
                                  ),
                                  Text(
                                    '${item.estimateDistance ?? ''} km',
                                    style: CommonTextStyle.textStyleFontLatoNormal.copyWith(
                                      color: AppThemes.colorViettelBlack,
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(
                                height: 12,
                              ),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    'Số KM thực tế',
                                    style: CommonTextStyle.textStyleFontLatoNormal.copyWith(
                                      color: AppThemes.colorViettelBlack,
                                    ),
                                  ),
                                  Text(
                                    '${item.actualDistance ?? ''} km',
                                    style: CommonTextStyle.textStyleFontLatoNormal.copyWith(
                                      color: AppThemes.colorViettelBlack,
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(
                                height: 12,
                              ),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    'Trạng thái',
                                    style: CommonTextStyle.textStyleFontLatoNormal.copyWith(
                                      color: AppThemes.colorViettelBlack,
                                    ),
                                  ),
                                  Text(
                                    explanationStatusEnum.toShortString() ?? '',
                                    style: CommonTextStyle.textStyleFontLatoNormal.copyWith(
                                      color: Colors.teal,
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(
                                height: 12,
                              ),
                              ((explanationStatusEnum == ExplanationStatusEnum.waiting || explanationStatusEnum == ExplanationStatusEnum.requestAdditional) &&
                                      item.driverCode == DataCenter.shared().getUserInfo().loginName)
                                  ? CommonButton(
                                      title: 'Giải trình',
                                      textColor: Colors.white,
                                      onButtonClick: () {
                                        Get.toNamed(Routers.explanationDetail, arguments: [item, true]);
                                      },
                                    )
                                  : SizedBox(),
                              explanationStatusEnum == ExplanationStatusEnum.waitingApprove &&
                                      ((item.isExcessTenPercent && item.isRoleExcessTenPercent) || (!item.isExcessTenPercent && item.isRoleCaptainCar))
                                  ? CommonButton(
                                      title: 'Xác nhận',
                                      textColor: Colors.white,
                                      onButtonClick: () {
                                        Get.toNamed(Routers.explanationDetail, arguments: [item, true]);
                                      },
                                    )
                                  : SizedBox(),
                            ],
                          ),
                        );
                      },
                    );
                  }
                }),
              ),
            ],
          ),
        ),
      );
    });
  }

  _buildFilter(BuildContext context) {
    return IconButton(
      icon: Icon(Icons.filter_alt_sharp, color: Colors.white),
      onPressed: () {
        showModalBottomSheet(
          context: context,
          builder: (context) {
            return BuildFilter(
              (status, driverName, licenseCar) {
                controller.fetchData(
                  explanationStatus: status,
                  driverName: driverName,
                  licenseCar: licenseCar,
                );
              },
            );
          },
        );
      },
    );
  }
}

class BuildFilter extends StatefulWidget {
  const BuildFilter(
    this.callBack,
  );

  final Function callBack;

  @override
  State<BuildFilter> createState() => _BuildFilterState();
}

class _BuildFilterState extends State<BuildFilter> {
  final _formKey = GlobalKey<FormBuilderState>();

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).requestFocus(FocusNode());
      },
      child: Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(mRadiusMedium)),
        ),
        padding: EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: SingleChildScrollView(
            child: FormBuilder(
              key: _formKey,
              child: Column(
                // mainAxisSize: MainAxisSize.min,
                children: [
                  Text('Bộ lọc', style: CommonTextStyle.textStyleFontLatoBigBold),
                  const SizedBox(height: 12),
                  FormFieldWidget(
                    labelText: 'Trạng thái',
                    child: FormBuilderDropdown<ExplanationStatusEnum>(
                      name: 'status',
                      onChanged: (value) {},
                      decoration: Indicator.inputDecorationBorder(
                        hintText: 'Chọn',
                      ),
                      isExpanded: true,
                      icon: Icon(Icons.keyboard_arrow_down_outlined),
                      // initialValue: initData.code.isNotEmpty ? initData : null,
                      items: ExplanationStatusEnum.values.map((option) {
                        return DropdownMenuItem(
                          child: Text(option.toShortString() ?? ''),
                          value: option,
                          onTap: () {
                            // _formKey.currentState.fields['roomType'].didChange(option);
                          },
                        );
                      }).toList(),
                    ),
                  ),
                  const SizedBox(height: 12),
                  FormFieldWidget(
                    labelText: 'Lái xe',
                    child: FormBuilderTextField(
                      name: 'driverName',
                      onEditingComplete: () {},
                      decoration: Indicator.inputDecorationBorder(
                        hintText: 'Nhập nội dung',
                      ),
                      textInputAction: TextInputAction.done,
                      onSubmitted: (value) {
                        FocusScope.of(context).unfocus();
                      },
                    ),
                  ),
                  const SizedBox(height: 12),
                  FormFieldWidget(
                    labelText: 'Biển số xe',
                    child: FormBuilderTextField(
                      name: 'licenseCar',
                      onEditingComplete: () {},
                      decoration: Indicator.inputDecorationBorder(
                        hintText: 'Nhập nội dung',
                      ),
                      textInputAction: TextInputAction.done,
                      onSubmitted: (value) {
                        FocusScope.of(context).unfocus();
                      },
                    ),
                  ),
                  const SizedBox(height: 24),
                  CommonButton(
                    title: 'Tìm kiếm',
                    textColor: Colors.white,
                    onButtonClick: () {
                      _formKey.currentState.save();
                      if (_formKey.currentState.validate()) {
                        Get.back();
                        widget.callBack(
                          (_formKey.currentState.value['status'] as ExplanationStatusEnum) != null
                              ? (_formKey.currentState.value['status'] as ExplanationStatusEnum).toInt()
                              : null,
                          _formKey.currentState.value['driverName'],
                          _formKey.currentState.value['licenseCar'],
                        );
                      }
                    },
                  ),
                  // SizedBox(height: MediaQuery.of(context).viewInsets.bottom),
                ],
              ),
            ),
          ),
        ),
      ),
    );
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.all(16.0).copyWith(
          bottom: MediaQuery.of(context).viewInsets.bottom,
        ),
        child: Column(
          // mainAxisSize: MainAxisSize.min,
          children: [
            Text('Bộ lọc', style: CommonTextStyle.textStyleFontLatoBigBold),
            const SizedBox(height: 12),
            FormFieldWidget(
              labelText: 'Trạng thái',
              child: FormBuilderDropdown<ExplanationStatusEnum>(
                name: 'status',
                onChanged: (value) {},
                decoration: Indicator.inputDecorationBorder(
                  hintText: 'Chọn',
                ),
                isExpanded: true,
                icon: Icon(Icons.keyboard_arrow_down_outlined),
                // initialValue: initData.code.isNotEmpty ? initData : null,
                items: ExplanationStatusEnum.values.map((option) {
                  return DropdownMenuItem(
                    child: Text(option.toShortString() ?? ''),
                    value: option,
                    onTap: () {
                      // _formKey.currentState.fields['roomType'].didChange(option);
                    },
                  );
                }).toList(),
              ),
            ),
            const SizedBox(height: 12),
            FormFieldWidget(
              labelText: 'Lái xe',
              child: FormBuilderTextField(
                name: 'driverCode',
                onEditingComplete: () {},
                decoration: Indicator.inputDecorationBorder(
                  hintText: 'Nhập nội dung',
                ),
                textInputAction: TextInputAction.done,
                onSubmitted: (value) {
                  FocusScope.of(context).unfocus();
                },
              ),
            ),
            const SizedBox(height: 12),
            FormFieldWidget(
              labelText: 'Biển số xe',
              child: FormBuilderTextField(
                name: 'licenseCar',
                onEditingComplete: () {},
                decoration: Indicator.inputDecorationBorder(
                  hintText: 'Nhập nội dung',
                ),
                textInputAction: TextInputAction.done,
                onSubmitted: (value) {
                  FocusScope.of(context).unfocus();
                },
              ),
            ),
            const SizedBox(height: 24),
            CommonButton(
              title: 'Tìm kiếm',
              textColor: Colors.white,
              onButtonClick: () {},
            )
          ],
        ),
      ),
    );
  }
}
