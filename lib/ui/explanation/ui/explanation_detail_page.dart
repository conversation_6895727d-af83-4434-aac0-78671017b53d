import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:trackcarvcc/constants/style/style.dart';
import 'package:trackcarvcc/repository/preferences/pref.dart';
import 'package:trackcarvcc/ui/explanation/data/model/explanation.dart';
import 'package:trackcarvcc/ui/explanation/data/view_model/explanation_view_model.dart';
import 'package:trackcarvcc/widget/common_button.dart';
import 'package:trackcarvcc/helpers/extensions.dart';

class ExplanationDetailPage extends GetView<ExplanationViewModel> {
  ExplanationDetailPage({
    this.explanationDTO,
    this.isShowAction = true,
  });

  final ExplanationDTO explanationDTO;
  final bool isShowAction;

  @override
  Widget build(BuildContext context) {
    return GetBuilder<ExplanationViewModel>(initState: (_) {
      controller.explanationController.text = explanationDTO.explainReason ?? '';
    }, builder: (_) {
      var explanationStatusEnum = explanationDTO.explanationStatus.toExplanationStatusEnum();

      return Scaffold(
        backgroundColor: Color(0xFFF5F5F5),
        appBar: AppBar(
          title: Text('Giải trình vượt ngưỡng cho phép'),
          centerTitle: true,
          backgroundColor: Color(0xFFEE0033),
        ),
        bottomNavigationBar: isShowAction
            ? Padding(
                padding: EdgeInsets.symmetric(vertical: 8, horizontal: 16),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    ((explanationStatusEnum == ExplanationStatusEnum.waiting || explanationStatusEnum == ExplanationStatusEnum.requestAdditional) &&
                            explanationDTO.driverCode == DataCenter.shared().getUserInfo().loginName)
                        ? CommonButton(
                            title: 'Xác nhận',
                            textColor: Colors.white,
                            onButtonClick: () {
                              // if (controller.explanationController.text.isEmpty) {
                              //   showConfirmDialog(
                              //     Get.context,
                              //     'Vui lòng nhập lý do giải trình',
                              //   );
                              //   return;
                              // }
                              controller.explanationUpdateStatus(
                                bookCarDayReportId: explanationDTO.bookCarDayReportId,
                                explanationStatus: ExplanationStatusEnum.waitingApprove,
                                reason: controller.explanationController.text,
                              );
                            },
                          )
                        : const SizedBox(),
                    explanationStatusEnum == ExplanationStatusEnum.waitingApprove &&
                            ((explanationDTO.isExcessTenPercent && explanationDTO.isRoleExcessTenPercent) ||
                                (!explanationDTO.isExcessTenPercent && explanationDTO.isRoleCaptainCar))
                        ? Row(
                            children: [
                              Expanded(
                                child: CommonButton(
                                  title: 'Phê duyệt',
                                  textColor: Colors.white,
                                  bgColor: Colors.blue,
                                  onButtonClick: () {
                                    controller.explanationUpdateStatus(
                                      bookCarDayReportId: explanationDTO.bookCarDayReportId,
                                      explanationStatus: ExplanationStatusEnum.approve,
                                    );
                                  },
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: CommonButton(
                                  title: 'Từ chối',
                                  textColor: Colors.white,
                                  onButtonClick: () {
                                    // controller.explanationUpdateStatus(
                                    //   bookCarDayReportId: explanationDTO.bookCarDayReportId,
                                    //   explanationStatus: ExplanationStatusEnum.reject,
                                    //   reason: '',
                                    // );
                                    showInputConfirmDialog(
                                      Get.context,
                                      'Từ chối',
                                      'Lý do từ chối',
                                      yesCallBack: (value) {
                                        controller.explanationUpdateStatus(
                                          bookCarDayReportId: explanationDTO.bookCarDayReportId,
                                          explanationStatus: ExplanationStatusEnum.reject,
                                          reason: value,
                                        );
                                      },
                                    );
                                  },
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: CommonButton(
                                  title: 'YC bổ sung',
                                  textColor: Colors.white,
                                  bgColor: Colors.orange,
                                  onButtonClick: () {
                                    showInputConfirmDialog(
                                      Get.context,
                                      'Yêu cầu bổ sung thông tin',
                                      'Nội dung yêu cầu bổ sung',
                                      yesCallBack: (value) {
                                        controller.explanationUpdateStatus(
                                          bookCarDayReportId: explanationDTO.bookCarDayReportId,
                                          explanationStatus: ExplanationStatusEnum.requestAdditional,
                                          reason: value,
                                        );
                                      },
                                    );
                                  },
                                ),
                              ),
                            ],
                          )
                        : const SizedBox(),
                  ],
                ),
              )
            : SizedBox(),
        body: RefreshIndicator(
          onRefresh: () async {
            controller.fetchData();
          },
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Text(
                    'Thông tin vượt ngưỡng',
                    style: CommonTextStyle.textStyleFontLatoNormalBold.copyWith(
                      color: AppThemes.colorViettelRed,
                    ),
                  ),
                  Divider(),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Ngày : ',
                        style: CommonTextStyle.textStyleFontLatoNormal.copyWith(
                          color: AppThemes.colorViettelBlack,
                        ),
                      ),
                      Text(
                        explanationDTO.bookCarDate ?? '',
                        style: CommonTextStyle.textStyleFontLatoNormal.copyWith(
                          color: AppThemes.colorViettelBlack,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(
                    height: 12,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Biển số xe: ',
                        style: CommonTextStyle.textStyleFontLatoNormal.copyWith(
                          color: AppThemes.colorViettelBlack,
                        ),
                      ),
                      Text(
                        explanationDTO.licenseCar ?? '',
                        style: CommonTextStyle.textStyleFontLatoNormal.copyWith(
                          color: AppThemes.colorViettelBlack,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(
                    height: 12,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Lái xe',
                        style: CommonTextStyle.textStyleFontLatoNormal.copyWith(
                          color: AppThemes.colorViettelBlack,
                        ),
                      ),
                      Text(
                        explanationDTO.driverName ?? '',
                        style: CommonTextStyle.textStyleFontLatoNormal.copyWith(
                          color: AppThemes.colorViettelBlack,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(
                    height: 12,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        '% chênh lệch KM',
                        style: CommonTextStyle.textStyleFontLatoNormal.copyWith(
                          color: Colors.orange,
                        ),
                      ),
                      Text(
                        explanationDTO.rateExcess ?? '',
                        style: CommonTextStyle.textStyleFontLatoNormal.copyWith(
                          color: Colors.orange,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(
                    height: 12,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Số KM dự kiến',
                        style: CommonTextStyle.textStyleFontLatoNormal.copyWith(
                          color: AppThemes.colorViettelBlack,
                        ),
                      ),
                      Text(
                        '${explanationDTO.estimateDistance ?? ''} km',
                        style: CommonTextStyle.textStyleFontLatoNormal.copyWith(
                          color: AppThemes.colorViettelBlack,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(
                    height: 12,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Số KM thực tế',
                        style: CommonTextStyle.textStyleFontLatoNormal.copyWith(
                          color: AppThemes.colorViettelBlack,
                        ),
                      ),
                      Text(
                        '${explanationDTO.actualDistance ?? ''} km',
                        style: CommonTextStyle.textStyleFontLatoNormal.copyWith(
                          color: AppThemes.colorViettelBlack,
                        ),
                      ),
                    ],
                  ),
                  // const SizedBox(
                  //   height: 12,
                  // ),
                  // Row(
                  //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  //   children: [
                  //     Text(
                  //       'Trạng thái',
                  //       style: CommonTextStyle.textStyleFontLatoNormal.copyWith(
                  //         color: AppThemes.colorViettelBlack,
                  //       ),
                  //     ),
                  //     Text(
                  //       explanationStatusEnum.toShortString() ?? '',
                  //       style: CommonTextStyle.textStyleFontLatoNormal.copyWith(
                  //         color: AppThemes.colorViettelBlack,
                  //       ),
                  //     ),
                  //   ],
                  // ),
                  const SizedBox(
                    height: 12,
                  ),
                  Text.rich(
                    TextSpan(
                      children: [
                        TextSpan(
                          text: 'Lý do giải trình',
                          style: CommonTextStyle.textStyleFontLatoNormal.copyWith(
                            color: AppThemes.colorViettelBlack,
                          ),
                        ),
                        TextSpan(
                          text: ' *',
                          style: CommonTextStyle.textStyleFontLatoNormal.copyWith(
                            color: AppThemes.colorViettelRed,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(
                    height: 12,
                  ),
                  ((explanationStatusEnum == ExplanationStatusEnum.waiting || explanationStatusEnum == ExplanationStatusEnum.requestAdditional) &&
                          explanationDTO.driverCode == DataCenter.shared().getUserInfo().loginName)
                      ? TextField(
                          controller: controller.explanationController,
                          maxLines: 5,
                          maxLength: 200,
                          decoration: InputDecoration(
                            hintText: 'Nhập lý do giải trình',
                            contentPadding: const EdgeInsets.all(mPadding),
                            hintStyle: CommonTextStyle.textStyleFontLatoNormalHint,
                            isDense: true,
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8.0),
                              borderSide: BorderSide(
                                color: Colors.red,
                                width: 1.0,
                              ),
                            ),
                          ),
                          autofocus: false,
                          textInputAction: TextInputAction.done,
                          style: CommonTextStyle.textStyleFontLatoNormal,
                          onChanged: (value) {},
                          enabled: isShowAction,
                        )
                      : Container(
                          padding: EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                          decoration: BoxDecoration(
                            color: Colors.grey[200],
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: Colors.grey[300],
                              width: 1,
                            ),
                          ),
                          child: Text(explanationDTO.explainReason ?? ''),
                        ),
                  const SizedBox(
                    height: 12,
                  ),
                  (explanationStatusEnum == ExplanationStatusEnum.requestAdditional)
                      ? Column(
                          // mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          crossAxisAlignment: CrossAxisAlignment.stretch,
                          children: [
                            Text(
                              'Yêu cầu bổ sung thông tin',
                              style: CommonTextStyle.textStyleFontLatoNormal.copyWith(
                                color: AppThemes.colorViettelBlack,
                              ),
                            ),
                            const SizedBox(
                              height: 12,
                            ),
                            Container(
                              padding: EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                              decoration: BoxDecoration(
                                color: Colors.grey[200],
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(
                                  color: Colors.grey[300],
                                  width: 1,
                                ),
                              ),
                              child: Text(
                                '${explanationDTO.requestReason ?? ''}',
                                style: CommonTextStyle.textStyleFontLatoNormal.copyWith(
                                  color: AppThemes.colorViettelBlack,
                                ),
                              ),
                            ),
                          ],
                        )
                      : const SizedBox(),
                  const SizedBox(
                    height: 12,
                  ),
                  (explanationStatusEnum == ExplanationStatusEnum.reject)
                      ? Column(
                          // mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          crossAxisAlignment: CrossAxisAlignment.stretch,
                          children: [
                            Text(
                              'Lý do từ chối',
                              style: CommonTextStyle.textStyleFontLatoNormal.copyWith(
                                color: AppThemes.colorViettelBlack,
                              ),
                            ),
                            const SizedBox(
                              height: 12,
                            ),
                            Container(
                              padding: EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                              decoration: BoxDecoration(
                                color: Colors.grey[200],
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(
                                  color: Colors.grey[300],
                                  width: 1,
                                ),
                              ),
                              child: Text(
                                '${explanationDTO.rejectReason ?? ''}',
                                style: CommonTextStyle.textStyleFontLatoNormal.copyWith(
                                  color: AppThemes.colorViettelBlack,
                                ),
                              ),
                            ),
                          ],
                        )
                      : const SizedBox(),

                  const SizedBox(
                    height: 16,
                  ),
                  Text(
                    'Thông tin lệnh xe trong ngày',
                    style: CommonTextStyle.textStyleFontLatoNormalBold.copyWith(
                      color: AppThemes.colorViettelRed,
                    ),
                  ),
                  Divider(),
                  ListView.separated(
                    itemBuilder: (context, index) {
                      final detail = explanationDTO.details[index];
                      return Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                'Mã lệnh: ',
                                style: CommonTextStyle.textStyleFontLatoNormal.copyWith(
                                  color: AppThemes.colorViettelBlack,
                                ),
                              ),
                              Text(
                                detail.bookCarCode ?? '',
                                style: CommonTextStyle.textStyleFontLatoNormalBold.copyWith(
                                  color: Colors.teal,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(
                            height: 12,
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                'Số KM dự kiến: ',
                                style: CommonTextStyle.textStyleFontLatoNormal.copyWith(
                                  color: AppThemes.colorViettelBlack,
                                ),
                              ),
                              Text(
                                '${detail.estimateDistance ?? ''} km',
                                style: CommonTextStyle.textStyleFontLatoNormal.copyWith(
                                  color: AppThemes.colorViettelBlack,
                                ),
                              ),
                            ],
                          ),
                        ],
                      );
                    },
                    shrinkWrap: true,
                    physics: NeverScrollableScrollPhysics(),
                    separatorBuilder: (context, index) => const Divider(),
                    itemCount: explanationDTO.details.length,
                  ),
                  const SizedBox(
                    height: 16,
                  ),
                  explanationStatusEnum == ExplanationStatusEnum.approve
                      ? Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Thông tin người phê duyệt',
                              style: CommonTextStyle.textStyleFontLatoNormalBold.copyWith(
                                color: AppThemes.colorViettelRed,
                              ),
                            ),
                            Divider(),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  'Người duyệt: ',
                                  style: CommonTextStyle.textStyleFontLatoNormal.copyWith(
                                    color: Colors.green,
                                  ),
                                ),
                                Text(
                                  explanationDTO.approvedName ?? '',
                                  style: CommonTextStyle.textStyleFontLatoNormalBold.copyWith(
                                    color: Colors.green,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(
                              height: 12,
                            ),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  'Ngày duyệt: ',
                                  style: CommonTextStyle.textStyleFontLatoNormal.copyWith(
                                    color: Colors.orange,
                                  ),
                                ),
                                Text(
                                  explanationDTO.approvedAt ?? '',
                                  style: CommonTextStyle.textStyleFontLatoNormalBold.copyWith(
                                    color: Colors.orange,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(
                              height: 12,
                            ),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  'Trạng thái: ',
                                  style: CommonTextStyle.textStyleFontLatoNormal.copyWith(
                                    color: AppThemes.colorViettelBlack,
                                  ),
                                ),
                                Text(
                                  explanationStatusEnum == ExplanationStatusEnum.approve
                                      ? 'Đã duyệt'
                                      : explanationStatusEnum == ExplanationStatusEnum.reject
                                          ? 'Từ chối'
                                          : '',
                                  style: CommonTextStyle.textStyleFontLatoNormal.copyWith(
                                    color: AppThemes.colorViettelBlack,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        )
                      : SizedBox(),

                  explanationStatusEnum == ExplanationStatusEnum.reject
                      ? Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Thông tin người từ chối',
                              style: CommonTextStyle.textStyleFontLatoNormalBold.copyWith(
                                color: AppThemes.colorViettelRed,
                              ),
                            ),
                            Divider(),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  'Người từ chối: ',
                                  style: CommonTextStyle.textStyleFontLatoNormal.copyWith(
                                    color: Colors.green,
                                  ),
                                ),
                                Text(
                                  explanationDTO.rejectedName ?? '',
                                  style: CommonTextStyle.textStyleFontLatoNormalBold.copyWith(
                                    color: Colors.green,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(
                              height: 12,
                            ),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  'Ngày từ chối: ',
                                  style: CommonTextStyle.textStyleFontLatoNormal.copyWith(
                                    color: Colors.orange,
                                  ),
                                ),
                                Text(
                                  explanationDTO.rejectedAt ?? '',
                                  style: CommonTextStyle.textStyleFontLatoNormalBold.copyWith(
                                    color: Colors.orange,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(
                              height: 12,
                            ),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  'Trạng thái: ',
                                  style: CommonTextStyle.textStyleFontLatoNormal.copyWith(
                                    color: AppThemes.colorViettelBlack,
                                  ),
                                ),
                                Text(
                                  'Từ chối',
                                  style: CommonTextStyle.textStyleFontLatoNormal.copyWith(
                                    color: AppThemes.colorViettelBlack,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        )
                      : SizedBox(),
                ],
              ),
            ),
          ),
        ),
      );
    });
  }
}
