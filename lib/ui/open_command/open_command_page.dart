import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:trackcarvcc/constants/constants.dart';
import 'package:trackcarvcc/constants/style/style.dart';
import 'package:trackcarvcc/controllers/open_command_controller.dart';
import 'package:trackcarvcc/models/models.dart';
import 'package:trackcarvcc/helpers/extensions.dart';
import 'package:trackcarvcc/ui/car_order_creation/widget/item_finish_time_widget.dart';
import 'package:trackcarvcc/ui/car_order_creation/widget/item_job_description_widget.dart';
import 'package:trackcarvcc/ui/open_command/widget/item_next_destination.dart';
import 'package:trackcarvcc/widget/common_button.dart';

class OpenCommandPage extends GetView<OpenCommandController> {
  OpenCommandPage({this.bookCar, this.listTogether});

  final LstBookCarDto bookCar;
  final List<UserLogin> listTogether;

  final TextEditingController _textTimeFinishDialogController =
      TextEditingController(text: Constants.TIMER_END_DEFAULT);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: AppThemes.colorViettelRed,
        brightness: Brightness.light,
        elevation: 1.0,
        leading: IconButton(
          icon: const Icon(
            Icons.arrow_back,
            color: Colors.white,
          ),
          onPressed: () {
            Get.back();
          },
        ),
        centerTitle: true,
        title: Text(
          'Mở lệnh',
          style: CommonTextStyle.textStyleFontLatoLargeBoldWhite,
        ),
      ),
      backgroundColor: Colors.white,
      body: GetBuilder<OpenCommandController>(
        initState: (_) {
          controller.bookCar = bookCar;
          controller.listTogether = listTogether;
        },
        builder: (_) {
          return _body(context);
        },
      ),
    );
  }

  Widget _body(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: SingleChildScrollView(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            _title(title: 'Thông tin đặt xe'),
            _itemDestinationPoint(),
            _itemFinishTime(),
            _jobDescription(),
            _bottomButton(context),
          ],
        ),
      ),
    );
  }

  _title({String title}) => Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(title, style: CommonTextStyle.textStyleFontLatoRedNormal),
          const SizedBox(height: mPaddingXSmall),
          Divider(
            height: 1,
            thickness: 1,
            color: AppThemes.colorViettelRed,
          )
        ],
      );

  _itemDestinationPoint() => Obx(
        () => ItemNextDestination(
          isValidate: controller.destinationPointIsValid,
          textController: controller.destinationPointController,
        ),
      );

  _itemFinishTime() => Obx(
        () => ItemFinishTime(
          controller: controller.timeFinishController,
          timeController: _textTimeFinishDialogController,
          isValidate:
              controller.timeFinishIsValid && controller.timeFromToIsValid,
          onChanged: controller.changeTimeFinish,
          isExpanded: false,
        ),
      );

  _jobDescription() => Obx(
        () => JobDescription(
          controller: controller.jobDescriptionController,
          isValidate: controller.jobDescriptionIsValid,
          onChanged: controller.changeJobDescription,
        ),
      );

  _bottomButton(BuildContext context) => Padding(
        padding: const EdgeInsets.only(
          top: mPaddingXLarge,
          left: mPadding,
          right: mPadding,
          bottom: mPaddingLarge,
        ),
        child: Row(
          children: [
            Expanded(
              flex: 1,
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 4.0),
                child: CommonButton(
                  title: 'Hủy',
                  textColor: Colors.white,
                  bgColor: Color(0xffff4444),
                  onButtonClick: _onCancel,
                ),
              ),
            ),
            Expanded(
              flex: 1,
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 4.0),
                child: CommonButton(
                  title: 'Đồng ý',
                  textColor: Colors.white,
                  bgColor: Color(0xff2d9a59),
                  onButtonClick: () {
                    _onApprove(context);
                  },
                ),
              ),
            )
          ],
        ),
      );

  _onCancel() {
    Get.back();
  }

  _onApprove(BuildContext context) {
    controller.validate.value = true;
    if (controller.formIsValid) {
      // call api create order
      showConfirmDialog(
        context,
        '',
        titleChild: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              'Đặt xe',
              style: CommonTextStyle.textStyleFontLatoNormalBold,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'Bạn chắc chắn muốn tạo phiếu đặt xe?',
              style: CommonTextStyle.textStyleFontLatoNormal,
              textAlign: TextAlign.center,
            )
          ],
        ),
        yesCallBack: () {
          // call API
          controller.openCommand();
        },
      );
    } else {
      showErrorToast(
          error:
              'Bạn chưa điền đẩy đủ thông tin \n hoặc thông tin đã nhập chưa đúng!');
    }
  }
}
