import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:trackcarvcc/constants/style/style.dart';
import 'package:trackcarvcc/controllers/controllers.dart';
import 'package:trackcarvcc/routes/routes.dart';
import 'package:trackcarvcc/ui/address_selection/data/address_info.dart';

class ItemNextDestination extends GetView<OpenCommandController> {
  const ItemNextDestination(
      {Key key, this.textController, this.isValidate = true})
      : super(key: key);

  final TextEditingController textController;
  final bool isValidate;

  @override
  Widget build(BuildContext context) {
    return _itemDestinationPoint(context);
  }

  _itemDestinationPoint(BuildContext context) => Padding(
        padding: const EdgeInsets.only(top: mPaddingLarge),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.my_location_outlined,
                  color: AppThemes.colorViettelRed,
                ),
                const SizedBox(
                  width: mPadding,
                ),
                Text(
                  'Điểm đến tiếp theo',
                  style: CommonTextStyle.textStyleFontLatoNormal,
                ),
              ],
            ),
            const SizedBox(height: mPadding),
            GestureDetector(
              onTap: () {
                Get.toNamed(Routers.address).then((addressInfo) {
                  if (addressInfo is AddressInfo) {
                    final address = addressInfo.numbHouse +
                        '' +
                        addressInfo.ward +
                        ',' +
                        addressInfo.district +
                        ',' +
                        addressInfo.province +
                        ', Việt Nam';
                    textController.text = address;
                    controller.changeDestinationPoint(address);
                    controller.destinationProvince = addressInfo.province;
                  }
                });
              },
              child: Container(
                decoration: BoxDecoration(
                  color: AppThemes.colorViettelGray3,
                  borderRadius: const BorderRadius.all(
                    Radius.circular(mRadiusSmall),
                  ),
                  border: Border.all(
                      color: isValidate
                          ? AppThemes.colorViettelGray2
                          : AppThemes.colorViettelRed),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: TextField(
                        controller: textController,
                        enabled: false,
                        readOnly: true,
                        maxLines: 2,
                        decoration: InputDecoration(
                          hintText: 'Chọn điạ điểm',
                          border: InputBorder.none,
                          contentPadding: const EdgeInsets.all(mPadding),
                          hintStyle:
                              CommonTextStyle.textStyleFontLatoNormalHint,
                          isDense: true,
                        ),
                        autofocus: false,
                        style: CommonTextStyle.textStyleFontLatoNormal,
                        onChanged: (value) {},
                      ),
                    ),
                    Container(
                      width: 50.0,
                      height: 50.0,
                      padding: const EdgeInsets.all(mPadding),
                      margin: const EdgeInsets.only(bottom: 0),
                      //check password field type
                      child: Icon(Icons.navigate_next),
                    )
                  ],
                ),
              ),
            ),
          ],
        ),
      );
}
