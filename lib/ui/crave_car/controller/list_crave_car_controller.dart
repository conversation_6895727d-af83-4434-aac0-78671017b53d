import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:trackcarvcc/constants/constants.dart';
import 'package:trackcarvcc/models/models.dart';
import 'package:trackcarvcc/repository/api/api.dart';
import 'package:trackcarvcc/repository/preferences/data_center.dart';
import 'package:trackcarvcc/ui/crave_car/model/crave_car_response.dart';
import 'package:trackcarvcc/helpers/extensions.dart';

class ListCraveCarController extends GetxController {
  Api api;
  var appState = Rx<AppState>();

  ListCraveCarController(this.api);

  final TextEditingController searchController = TextEditingController();
  RxList<CraveCarDto> listCraveCar = RxList<CraveCarDto>();
  List<CraveCarDto> backupListCraveCar = List<CraveCarDto>();

  RxString status = RxString();
  int role;

  bool isDrive() {
    final userProfile = DataCenter.shared().getUserInfo();
    List<String> _roles = userProfile.roleCode != null ? userProfile.roleCode.split(";") : [];
    for (var roleCode in _roles) {
      if (roleCode == Constants.LAIXE) {
        return true;
      }
    }
    return false;
  }

  fetchData(int role, String code) async {
    try {
      appState.value = AppState.LOADING;
      CraveCarRequest request = CraveCarRequest(
          sysUserId: DataCenter.shared().getUserInfo().sysUserId.toString(), status: 1, code: code, role: role);
      final response = await api.getListBookCarPartner(request);
      final result = BaseApiResponse.fromJson(response);
      if (result.success) {
        final data = CraveCarResponse.fromJson(result.data);
        if (data.resultInfo.status == RESULT_OK) {
          backupListCraveCar = data.listBookCarPartner;
          listCraveCar.clear();
          listCraveCar.addAll(data.listBookCarPartner);
          appState.value = AppState.DONE;
        } else {
          appState.value = AppState.ERROR;
        }
      } else {
        appState.value = AppState.ERROR;
      }
    } catch (e) {
      appState.value = AppState.ERROR;
    }
  }

  getCheckPermission() async {
    try {
      appState.value = AppState.LOADING;
      CraveCarRequest request = CraveCarRequest(sysUserId: DataCenter.shared().getUserInfo().sysUserId.toString());
      final response = await api.getCheckPermission(request);
      final result = BaseApiResponse.fromJson(response);
      if (result.success) {
        final data = CraveCarResponse.fromJson(result.data);
        if (data.resultInfo.status == RESULT_OK) {
          role = data.role;
          fetchData(role, "");
        } else {
          appState.value = AppState.ERROR;
        }
      } else {
        appState.value = AppState.ERROR;
      }
    } catch (e) {
      appState.value = AppState.ERROR;
    }
  }

  updateStatusBookCarPartner(int bookCarPartnerId, int status) async {
    try {
      showLoadingDialog();
      CraveCarRequest request = CraveCarRequest(
          sysUserId: DataCenter.shared().getUserInfo().sysUserId.toString(),
          status: status,
          bookCarPartnerId: bookCarPartnerId);
      final response = await api.updateStatusBookCarPartner(request);
      final result = BaseApiResponse.fromJson(response);
      if (result.success) {
        final data = CraveCarResponse.fromJson(result.data);
        if (data.resultInfo.status == RESULT_OK) {
          dismissLoadingDialog();
          Fluttertoast.showToast(msg: data.resultInfo.message ?? "Xóa bản ghi thành công!");
          fetchData(role, "");
        } else {
          showErrorToast(error: 'NOK. vui lòng thử lại!!');
        }
      } else {
        showErrorToast(error: 'Có lỗi xảy ra. vui lòng thử lại!!');
      }
    } catch (e) {
      showErrorToast(error: 'Có lỗi xảy ra. vui lòng thử lại!!');
    }
  }
}
