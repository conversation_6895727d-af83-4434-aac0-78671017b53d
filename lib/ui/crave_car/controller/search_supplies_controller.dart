import 'dart:developer';

import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:trackcarvcc/constants/http_code.dart';
import 'package:trackcarvcc/models/base/base_api_response.dart';
import 'package:trackcarvcc/repository/api/api.dart';
import 'package:trackcarvcc/ui/crave_car/model/product.dart';

class SearchProductController extends GetxController {
  Api api;

  SearchProductController(this.api);

  RxList<Product> listProduct;
  Product product;

  @override
  void onInit() {
    super.onInit();
    listProduct = RxList<Product>();
    product = Product();
    getGoods("");
  }

  getGoods(String keySearch) async {
    try {
      final response = await api.getGoods(keySearch);
      final result = BaseApiResponse.fromJson(response);
      if (result.success) {
        final data = ProductResponse.fromJson(result.data);
        if (data.resultInfo.status == RESULT_OK) {
          listProduct.clear();
          listProduct.addAll(data.lstGood);
        } else {
          Fluttertoast.showToast(msg: data.resultInfo.message ?? "Có lỗi xảy ra!");
        }
      } else {
        Fluttertoast.showToast(msg: result.message ?? "Có lỗi xảy ra!");
      }
    } catch (e) {
      log(e.toString());
      Fluttertoast.showToast(msg: "Có lỗi xảy ra!");
    }
  }
}
