import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:trackcarvcc/constants/app_state.dart';
import 'package:trackcarvcc/constants/constants.dart';
import 'package:trackcarvcc/helpers/extensions.dart';
import 'package:trackcarvcc/helpers/string_utils.dart';
import 'package:trackcarvcc/models/models.dart';
import 'package:trackcarvcc/models/response/google_api_distance_response.dart';
import 'package:trackcarvcc/repository/api/api.dart';
import 'package:trackcarvcc/repository/preferences/data_center.dart';
import 'package:trackcarvcc/ui/crave_car/model/crave_car_response.dart';
import 'package:trackcarvcc/ui/crave_car/model/driver_partner_dto.dart';
import 'package:trackcarvcc/ui/crave_car/model/product.dart';
import 'package:url_launcher/url_launcher.dart';

class CraveCarController extends GetxController {
  Api api;

  CraveCarController(this.api);

  final appState = Rx<AppState>();

  TextEditingController timeStartController = TextEditingController();
  TextEditingController timeFinishController = TextEditingController();

  RxString userUnit = ''.obs;
  RxString userName = ''.obs;
  RxString userEmail = ''.obs;
  RxString userPhone = ''.obs;
  RxString startPoint = ''.obs;
  RxString destinationPoint = ''.obs;
  RxString timeStart = ''.obs;
  RxString timeFinish = ''.obs;
  RxString jobDescription = ''.obs;

  void changeTimeStart(String value) => timeStart.value = value;

  void changeTimeFinish(String value) => timeFinish.value = value;

  RxInt numberUserChosen = 0.obs;
  RxDouble weightCar = 0.0.obs;
  int typeBookCarChosen = 0;
  RxDouble expectDistance = 0.0.obs;
  RxString expectTime = ''.obs;

  CraveCarDto craveCarDto;

  var code = RxString();
  var partnerName = RxString();
  var typeBookCar = RxInt();
  var costEstimateController = TextEditingController();
  var codeController = TextEditingController();
  var contentWorkController = TextEditingController();
  var reasonController = TextEditingController();
  var typeBookCarPartner = RxString();
  var licenseCarPartner = RxString();
  var listStartPoint = RxList<CraveCarDto>();
  var listDestinationPoint = RxList<CraveCarDto>();
  var listContentWork = RxList<CraveCarDto>();
  var listTypeBookCarPartner = RxList<CraveCarDto>();
  var listCarVehiclePartner = RxList<CraveCarDto>();
  var listDriverPartner = RxList<DriverPartnerDTO>();
  var driverPartnerDTO = Rx<DriverPartnerDTO>();
  var listProduct = RxList<Product>();
  var licenseCarDto = Rx<CraveCarDto>();

  init() {
    timeStartController = TextEditingController(text: StringUtils.parseDateTime24Hour(DateTime.now()));
    changeTimeStart(StringUtils.parseDateTime24Hour(DateTime.now()));
    timeFinishController = TextEditingController(text: StringUtils.parseDateTime24Hour(DateTime.now()));
    changeTimeFinish(StringUtils.parseDateTime24Hour(DateTime.now()));
  }

  getGoogleApiDistance() async {
    try {
      showLoadingDialog();

      final response = await api.getExpectDistance(startPoint.value, destinationPoint.value);

      final result = BaseApiResponse.fromJson(response);
      if (result.success) {
        final data = GoogleApiDistanceResponse.fromJson(result.data);
        if (data.status == RESULT_OK) {
          // notify
          expectDistance.value = data.rows[0].elements[0].distance.value / 1000;
          expectTime.value = data.rows[0].elements[0].duration.text;
        } else {
          showErrorToast(error: 'Xảy ra lỗi khi lấy thông tin khoảng cách!');
        }
      } else {
        showErrorToast(error: 'Xảy ra lỗi khi lấy thông tin khoảng cách!');
      }
      dismissLoadingDialog();
    } catch (e) {
      showErrorToast(error: 'Xảy ra lỗi khi lấy thông tin khoảng cách!');
      dismissLoadingDialog();
    }
  }

  genCode() async {
    try {
      showLoadingDialog();
      final response = await api.genCode(DataCenter.shared().getUserInfo().sysUserId, DataCenter.shared().getUserInfo().sysGroupId);

      final result = BaseApiResponse.fromJson(response);
      if (result.success) {
        final data = CraveCarResponse.fromJson(result.data);
        if (data.resultInfo.status == RESULT_OK) {
          code.value = data.code;
        } else {
          showErrorToast(error: data.resultInfo.message ?? 'NOK');
        }
      } else {
        showErrorToast(error: 'Có lỗi xảy ra. vui lòng thử lại!!');
      }
      dismissLoadingDialog();
    } catch (e) {
      showErrorToast(error: 'Có lỗi xảy ra. vui lòng thử lại!!');
      dismissLoadingDialog();
    }
  }

  getToAddress(String key) async {
    listDestinationPoint.clear();
    try {
      // showLoadingDialog();
      final response = await api.getToAddress(key);

      final result = BaseApiResponse.fromJson(response);
      if (result.success) {
        final data = CraveCarResponse.fromJson(result.data);
        if (data.resultInfo.status == RESULT_OK) {
          listDestinationPoint.addAll(data.listToAddress);
        } else {
          showErrorToast(error: data.resultInfo.message ?? 'NOK');
        }
      } else {
        showErrorToast(error: 'Có lỗi xảy ra. vui lòng thử lại!!');
      }
      // dismissLoadingDialog();
    } catch (e) {
      showErrorToast(error: 'Có lỗi xảy ra. vui lòng thử lại!!');
      // dismissLoadingDialog();
    }
  }

  getFromAddress(String key) async {
    listStartPoint.clear();
    try {
      final response = await api.getFromAddress(key);

      final result = BaseApiResponse.fromJson(response);
      if (result.success) {
        final data = CraveCarResponse.fromJson(result.data);
        if (data.resultInfo.status == RESULT_OK) {
          listStartPoint.addAll(data.listFromAddress);
        } else {
          showErrorToast(error: data.resultInfo.message ?? 'NOK');
        }
      } else {
        showErrorToast(error: 'Có lỗi xảy ra. vui lòng thử lại!!');
      }
    } catch (e) {
      showErrorToast(error: 'Có lỗi xảy ra. vui lòng thử lại!!');
    }
  }

  getDescriptionWork() async {
    listContentWork.clear();
    try {
      final response = await api.getDescriptionWork();

      final result = BaseApiResponse.fromJson(response);
      if (result.success) {
        final data = CraveCarResponse.fromJson(result.data);
        if (data.resultInfo.status == RESULT_OK) {
          listContentWork.addAll(data.listContentWork);
        } else {
          showErrorToast(error: data.resultInfo.message ?? 'NOK');
        }
      } else {
        showErrorToast(error: 'Có lỗi xảy ra. vui lòng thử lại!!');
      }
    } catch (e) {
      showErrorToast(error: 'Có lỗi xảy ra. vui lòng thử lại!!');
    }
  }

  getTypeBookCarPartner() async {
    listTypeBookCarPartner.clear();
    try {
      final response = await api.getTypeBookCarPartner();

      final result = BaseApiResponse.fromJson(response);
      if (result.success) {
        final data = CraveCarResponse.fromJson(result.data);
        if (data.resultInfo.status == RESULT_OK) {
          listTypeBookCarPartner.addAll(data.listTypeBookCarPartner);
          if (craveCarDto != null) {
            listTypeBookCarPartner.forEach((element) {
              if (craveCarDto.typeBookCar.toString() == element.code) {
                typeBookCarPartner.value = element.name;
              }
            });
          }
        } else {
          showErrorToast(error: data.resultInfo.message ?? 'NOK');
        }
      } else {
        showErrorToast(error: 'Có lỗi xảy ra. vui lòng thử lại!!');
      }
    } catch (e) {
      showErrorToast(error: 'Có lỗi xảy ra. vui lòng thử lại!!');
    }
  }

  getLicenseCarPartner(String key) async {
    listCarVehiclePartner.clear();
    try {
      final response = await api.getLicenseCarPartner(key);

      final result = BaseApiResponse.fromJson(response);
      if (result.success) {
        final data = CraveCarResponse.fromJson(result.data);
        if (data.resultInfo.status == RESULT_OK) {
          listCarVehiclePartner.addAll(data.listCatVehiclePartner);
        } else {
          showErrorToast(error: data.resultInfo.message ?? 'NOK');
        }
      } else {
        showErrorToast(error: 'Có lỗi xảy ra. vui lòng thử lại!!');
      }
    } catch (e) {
      showErrorToast(error: 'Có lỗi xảy ra. vui lòng thử lại!!');
    }
  }

  getCostEstimate() async {
    if (destinationPoint.value == "" || startPoint.value == "") {
      return;
    }

    try {
      var body = CraveCarRequest(
          fromAddress: startPoint.value,
          toAddress: destinationPoint.value,
          licenseCarPartner: licenseCarPartner.value,
          typeBookCar: typeBookCar.value);
      final response = await api.getCostEstimate(body);

      final result = BaseApiResponse.fromJson(response);
      if (result.success) {
        final data = CraveCarResponse.fromJson(result.data);
        if (data.resultInfo.status == RESULT_OK) {
          costEstimateController.text = data.costEstimate.toString();
        } else {
          showErrorToast(error: data.resultInfo.message ?? 'NOK');
        }
      } else {
        showErrorToast(error: 'Có lỗi xảy ra. vui lòng thử lại!!');
      }
    } catch (e) {
      showErrorToast(error: 'Có lỗi xảy ra. vui lòng thử lại!!');
    }
  }

  getListBookCarPartnerDetail(int id) async {
    listProduct.clear();
    try {
      final response = await api.getListBookCarPartnerDetail(id);

      final result = BaseApiResponse.fromJson(response);
      if (result.success) {
        final data = CraveCarResponse.fromJson(result.data);
        if (data.resultInfo.status == RESULT_OK) {
          listProduct.addAll(data.listBookCarPartnerDetail);
        } else {
          showErrorToast(error: data.resultInfo.message ?? 'NOK');
        }
      } else {
        showErrorToast(error: 'Có lỗi xảy ra. vui lòng thử lại!!');
      }
    } catch (e) {
      showErrorToast(error: 'Có lỗi xảy ra. vui lòng thử lại!!');
    }
  }

  getListDriverCar(String keySearch) async {
    listDriverPartner.clear();
    try {
      final response = await api.getListDriverCar(keySearch);

      final result = BaseApiResponse.fromJson(response);
      if (result.success) {
        final data = CraveCarResponse.fromJson(result.data);
        if (data.resultInfo.status == RESULT_OK) {
          listDriverPartner.addAll(data.listDriverDTO);
          if (craveCarDto != null && craveCarDto.driverPartnerId != null) {
            for (var driver in listDriverPartner) {
              if (driver.driverPartnerId == craveCarDto.driverPartnerId) {
                driverPartnerDTO.value = driver;
                break;
              }
            }
          }
        } else {
          showErrorToast(error: data.resultInfo.message ?? 'NOK');
        }
      } else {
        showErrorToast(error: 'Có lỗi xảy ra. vui lòng thử lại!!');
      }
    } catch (e) {
      showErrorToast(error: 'Có lỗi xảy ra. vui lòng thử lại!!');
    }
  }

  fetchUserInfoAndPrepareInfo() async {
    try {
      showLoadingDialog();
      await Future.delayed(const Duration(milliseconds: 500));
      // get user info
      userUnit.value = DataCenter.shared().getUserInfo().departmentName + '-' + DataCenter.shared().getUserInfo().sysGroupName;
      userName.value = DataCenter.shared().getUserInfo().fullName;
      userEmail.value = DataCenter.shared().getUserInfo().email;
      userPhone.value = DataCenter.shared().getUserInfo().phoneNumber;

      dismissLoadingDialog();
    } catch (e) {
      showErrorToast(error: 'Xảy ra lỗi khi lấy thông tin!');
      dismissLoadingDialog();
    }
  }

  CraveCarRequest body() {
    return CraveCarRequest(
        sysUserId: DataCenter.shared().getUserInfo().sysUserId.toString(),
        sysGroupId: craveCarDto == null ? DataCenter.shared().getUserInfo().sysGroupId.toString() : null,
        bookCarPartnerDTO: CraveCarDto(
            createdBy: craveCarDto != null ? craveCarDto.createdBy : null,
            sysGroupLevel2: craveCarDto != null ? craveCarDto.sysGroupLevel2 : null,
            bookCarPartnerId: craveCarDto != null ? craveCarDto.bookCarPartnerId : null,
            sysGroupId: craveCarDto != null ? craveCarDto.sysGroupId : null,
            status: craveCarDto != null ? craveCarDto.status : null,
            code: code.value,
            fromAddress: startPoint.value,
            toAddress: destinationPoint.value,
            descriptionWork: contentWorkController.text,
            typeBookCar: typeBookCar.value,
            catVehiclePartnerId: licenseCarDto.value.catVehiclePartnerId,
            licenseCarPartner: licenseCarDto.value.licenseCarPartner,
            carTypeId: licenseCarDto.value.carTypeId,
            carTypeName: licenseCarDto.value.carTypeName,
            contractId: licenseCarDto.value.contractId,
            contractCode: licenseCarDto.value.contractCode,
            contractName: licenseCarDto.value.contractName,
            partnerName: licenseCarDto.value.partnerName,
            partnerCode: licenseCarDto.value.partnerCode,
            weight: weightCar.value,
            startTimeEstimate: timeStartController.text,
            endTimeEstimate: timeFinishController.text,
            stockTransCode: codeController.text,
            costEstimate: double.parse(costEstimateController.text),
            listBookCarPartnerDetail: listProduct));
  }

  addBookCarPartner() async {
    try {
      showLoadingDialog();
      final response = await api.addBookCarPartner(body());

      final result = BaseApiResponse.fromJson(response);
      if (result.success) {
        final data = CraveCarResponse.fromJson(result.data);
        if (data.resultInfo.status == RESULT_OK) {
          Get.back();
          Fluttertoast.showToast(msg: data.resultInfo.message ?? "Đã tạo phiếu xin xe đối tác!");
        } else {
          showErrorToast(error: data.resultInfo.message ?? 'NOK');
        }
      } else {
        showErrorToast(error: 'Có lỗi xảy ra. vui lòng thử lại!!');
      }
      dismissLoadingDialog();
    } catch (e) {
      showErrorToast(error: 'Có lỗi xảy ra. vui lòng thử lại!!');
      dismissLoadingDialog();
    }
  }

  updateBookCarPartner() async {
    try {
      showLoadingDialog();
      final response = await api.updateBookCarPartner(body());

      final result = BaseApiResponse.fromJson(response);
      if (result.success) {
        final data = CraveCarResponse.fromJson(result.data);
        if (data.resultInfo.status == RESULT_OK) {
          Get.back();
          Fluttertoast.showToast(msg: data.resultInfo.message ?? "Update thành công!");
        } else {
          showErrorToast(error: data.resultInfo.message ?? 'NOK');
        }
      } else {
        showErrorToast(error: 'Có lỗi xảy ra. vui lòng thử lại!!');
      }
      dismissLoadingDialog();
    } catch (e) {
      showErrorToast(error: 'Có lỗi xảy ra. vui lòng thử lại!!');
      dismissLoadingDialog();
    }
  }

  appProveRejectGDCNKT(CraveCarRequest body) async {
    try {
      showLoadingDialog();
      final response = await api.appProveRejectGDCNKT(body);

      final result = BaseApiResponse.fromJson(response);
      if (result.success) {
        final data = CraveCarResponse.fromJson(result.data);
        if (data.resultInfo.status == RESULT_OK) {
          Get.back();
          Fluttertoast.showToast(msg: data.resultInfo.message ?? "Update thành công!");
        } else {
          showErrorToast(error: data.resultInfo.message ?? 'NOK');
        }
      } else {
        showErrorToast(error: 'Có lỗi xảy ra. vui lòng thử lại!!');
      }
      dismissLoadingDialog();
    } catch (e) {
      showErrorToast(error: 'Có lỗi xảy ra. vui lòng thử lại!!');
      dismissLoadingDialog();
    }
  }

  appProveRejectQLTS(CraveCarRequest body) async {
    try {
      showLoadingDialog();
      final response = await api.appProveRejectQLTS(body);

      final result = BaseApiResponse.fromJson(response);
      if (result.success) {
        final data = CraveCarResponse.fromJson(result.data);
        if (data.resultInfo.status == RESULT_OK) {
          Get.back();
          Fluttertoast.showToast(msg: data.resultInfo.message ?? "Update thành công!");
        } else {
          showErrorToast(error: data.resultInfo.message ?? 'NOK');
        }
      } else {
        showErrorToast(error: 'Có lỗi xảy ra. vui lòng thử lại!!');
      }
      dismissLoadingDialog();
    } catch (e) {
      showErrorToast(error: 'Có lỗi xảy ra. vui lòng thử lại!!');
      dismissLoadingDialog();
    }
  }

  bool get timeFromToIsValid {
    if (timeStart.value.isNotEmpty && timeFinish.value.isNotEmpty) {
      DateTime toDate = StringUtils.isEmpty(timeStart.value) ? null : DateFormat('dd/MM/yyyy HH:mm').parse(timeStart.value);

      DateTime fromDate = StringUtils.isEmpty(timeFinish.value) ? null : DateFormat('dd/MM/yyyy HH:mm').parse(timeFinish.value);
      if (toDate != null && fromDate != null && fromDate.compareTo(toDate) >= 0) return true;
    }
    return false;
  }

  Future<void> makeAction(String url) async {
    if (await canLaunch(url)) {
      await launch(url);
    } else {
      showErrorToast(error: 'Không thể thực hiện cuộc gọi');
    }
  }

  bool get validate {
    if (startPoint.value == '') {
      showErrorToast(error: "Chọn điểm khởi hành!");
      return false;
    }
    if (destinationPoint.value == '') {
      showErrorToast(error: "Chọn điểm đến!");
      return false;
    }
    if (timeStartController.text == '') {
      showErrorToast(error: "Chọn thời gian khởi hành!");
      return false;
    }
    if (timeFinishController.text == '') {
      showErrorToast(error: "Chọn thời gian hoàn thành!");
      return false;
    }
    if (contentWorkController.text == '') {
      showErrorToast(error: "Nhập nội dung công việc!");
      return false;
    }
    if (typeBookCar.value == null) {
      showErrorToast(error: "Chọn kiểu đi!");
      return false;
    }
    if (licenseCarPartner.value == '') {
      showErrorToast(error: "Chọn xe!");
      return false;
    }

    for (var goods in listProduct) {
      if (goods.amount == null || goods.amount == 0) {
        showErrorToast(error: "Nhập đầy đủ thông tin số lượng hàng hóa!");
        return false;
      }
    }

    return true;
  }

  syncBookCarDtoInfo() async {
    try {
      showLoadingDialog();
      await Future.delayed(const Duration(milliseconds: 500));
      // sync user info
      userUnit.value = craveCarDto.sysGroupLevel2 ?? '';
      userName.value = craveCarDto.createdByName ?? '';
      userEmail.value = craveCarDto.email ?? '';
      userPhone.value = craveCarDto.phoneNumber ?? '';
      code.value = craveCarDto.code ?? '';
      destinationPoint.value = craveCarDto.toAddress ?? '';
      startPoint.value = craveCarDto.fromAddress ?? '';
      contentWorkController.text = craveCarDto.descriptionWork ?? '';
      typeBookCar.value = craveCarDto.typeBookCar ?? null;
      licenseCarDto.value = CraveCarDto(
          catVehiclePartnerId: craveCarDto.catVehiclePartnerId,
          licenseCarPartner: craveCarDto.licenseCarPartner,
          carTypeId: craveCarDto.carTypeId,
          carTypeName: craveCarDto.carTypeName,
          contractId: craveCarDto.contractId,
          contractCode: craveCarDto.contractCode,
          contractName: craveCarDto.contractName,
          partnerName: craveCarDto.partnerName,
          partnerCode: craveCarDto.partnerCode);
      weightCar.value = craveCarDto.weight ?? '';
      timeStartController.text = craveCarDto.startTimeEstimateStr ?? '';
      timeFinishController.text = craveCarDto.endTimeEstimateStr ?? '';
      costEstimateController.text = craveCarDto.costEstimate.toString() ?? '';
      codeController.text = craveCarDto.stockTransCode ?? '';
      licenseCarPartner.value = craveCarDto.licenseCarPartner ?? '';
      partnerName.value = craveCarDto.partnerName;
      getListBookCarPartnerDetail(craveCarDto.bookCarPartnerId);
      if (craveCarDto.driverPartnerId != null) {
        getListDriverCar("");
      }

      dismissLoadingDialog();
    } catch (e) {
      showErrorToast(error: 'Xảy ra lỗi khi đồng bộ thông tin!');
      dismissLoadingDialog();
    }
  }

  Future clear() async {
    timeStartController.clear();
    timeFinishController.clear();
    userUnit = ''.obs;
    userName = ''.obs;
    userEmail = ''.obs;
    userPhone = ''.obs;
    startPoint = ''.obs;
    destinationPoint = ''.obs;
    timeStart = ''.obs;
    timeFinish = ''.obs;
    contentWorkController.clear();
    listProduct.clear();
    listCarVehiclePartner.clear();
    listDestinationPoint.clear();
    listStartPoint.clear();
    listContentWork.clear();
    listTypeBookCarPartner.clear();
    code = RxString();
    partnerName = RxString();
    typeBookCar = RxInt();
    costEstimateController.clear();
    reasonController.clear();
    typeBookCarPartner = RxString();
    licenseCarPartner = RxString();
    licenseCarDto = Rx<CraveCarDto>();
    numberUserChosen = RxInt(0);
    weightCar = RxDouble(0.0);
    typeBookCarChosen = 0;
    expectDistance = RxDouble();
    expectTime = RxString('');
    craveCarDto = CraveCarDto();
  }
}
