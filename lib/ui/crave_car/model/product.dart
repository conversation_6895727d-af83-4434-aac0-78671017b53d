import 'package:trackcarvcc/models/base/result_info.dart';

class ProductResponse {
  ResultInfo resultInfo;
  List<Product> lstGood;

  ProductResponse({this.resultInfo, this.lstGood});

  ProductResponse.fromJson(Map<String, dynamic> json) {
    resultInfo = json['resultInfo'] != null ? new ResultInfo.fromJson(json['resultInfo']) : null;
    if (json['lstGood'] != null) {
      lstGood = <Product>[];
      json['lstGood'].forEach((v) {
        lstGood.add(new Product.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.resultInfo != null) {
      data['resultInfo'] = this.resultInfo.toJson();
    }
    if (this.lstGood != null) {
      data['lstGood'] = this.lstGood.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Product {
  String goodName;
  double amount;
  double weightGoods;
  int goodsId;
  String goodCode;
  String name;
  double weight;

  Product({this.goodName, this.amount, this.weightGoods});

  Product.fromJson(Map<String, dynamic> json) {
    goodName = json['goodName'];
    amount = json['amount'];
    weightGoods = json['weightGoods'];
    goodsId = json['goodsId'];
    goodCode = json['goodCode'];
    name = json['name'];
    weight = json['weight'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['goodName'] = this.goodName;
    data['amount'] = this.amount;
    data['weightGoods'] = this.weightGoods;
    data['goodsId'] = this.goodsId;
    data['goodCode'] = this.goodCode;
    data['name'] = this.name;
    data['weight'] = this.weight;
    return data;
  }
}
