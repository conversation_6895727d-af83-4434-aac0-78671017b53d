import 'package:trackcarvcc/models/base/result_info.dart';
import 'package:trackcarvcc/ui/crave_car/model/driver_partner_dto.dart';
import 'package:trackcarvcc/ui/crave_car/model/product.dart';

class CraveCarResponse {
  ResultInfo resultInfo;
  String code;
  int role;
  List<CraveCarDto> listContentWork;
  List<CraveCarDto> listTypeBookCarPartner;
  List<Product> listBookCarPartnerDetail;
  List<CraveCarDto> listFromAddress;
  List<CraveCarDto> listToAddress;
  double costEstimate;
  List<CraveCarDto> listCatVehiclePartner;
  List<CraveCarDto> listBookCarPartner;
  List<DriverPartnerDTO> listDriverDTO;

  CraveCarResponse({
    this.resultInfo,
    this.code,
    this.role,
    this.listContentWork,
    this.listTypeBookCarPartner,
    this.listBookCarPartnerDetail,
    this.listFromAddress,
    this.listToAddress,
    this.costEstimate,
    this.listCatVehiclePartner,
    this.listBookCarPartner,
    this.listDriverDTO,
  });

  CraveCarResponse.fromJson(Map<String, dynamic> json) {
    resultInfo = json['resultInfo'] != null ? new ResultInfo.fromJson(json['resultInfo']) : null;
    code = json['code'];
    role = json['role'];
    if (json['listBookCarPartner'] != null) {
      listBookCarPartner = new List<CraveCarDto>();
      json['listBookCarPartner'].forEach((v) {
        listBookCarPartner.add(new CraveCarDto.fromJson(v));
      });
    }
    if (json['listDriverDTO'] != null) {
      listDriverDTO = new List<DriverPartnerDTO>();
      json['listDriverDTO'].forEach((v) {
        listDriverDTO.add(new DriverPartnerDTO.fromJson(v));
      });
    }
    if (json['listBookCarPartnerDetail'] != null) {
      listBookCarPartnerDetail = new List<Product>();
      json['listBookCarPartnerDetail'].forEach((v) {
        listBookCarPartnerDetail.add(new Product.fromJson(v));
      });
    }
    if (json['listContentWork'] != null) {
      listContentWork = new List<CraveCarDto>();
      json['listContentWork'].forEach((v) {
        listContentWork.add(new CraveCarDto.fromJson(v));
      });
    }
    if (json['listTypeBookCarPartner'] != null) {
      listTypeBookCarPartner = new List<CraveCarDto>();
      json['listTypeBookCarPartner'].forEach((v) {
        listTypeBookCarPartner.add(new CraveCarDto.fromJson(v));
      });
    }
    if (json['listFromAddress'] != null) {
      listFromAddress = new List<CraveCarDto>();
      json['listFromAddress'].forEach((v) {
        listFromAddress.add(new CraveCarDto.fromJson(v));
      });
    }
    if (json['listToAddress'] != null) {
      listToAddress = new List<CraveCarDto>();
      json['listToAddress'].forEach((v) {
        listToAddress.add(new CraveCarDto.fromJson(v));
      });
    }
    costEstimate = json['costEstimate'];
    if (json['listCatVehiclePartner'] != null) {
      listCatVehiclePartner = new List<CraveCarDto>();
      json['listCatVehiclePartner'].forEach((v) {
        listCatVehiclePartner.add(new CraveCarDto.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.listBookCarPartner != null) {
      data['listBookCarPartner'] = this.listBookCarPartner.map((v) => v.toJson()).toList();
    }
    if (this.resultInfo != null) {
      data['resultInfo'] = this.resultInfo.toJson();
    }
    data['code'] = this.code;
    data['role'] = this.role;
    if (this.listContentWork != null) {
      data['listContentWork'] = this.listContentWork.map((v) => v.toJson()).toList();
    }
    if (this.listTypeBookCarPartner != null) {
      data['listTypeBookCarPartner'] = this.listTypeBookCarPartner.map((v) => v.toJson()).toList();
    }
    if (this.listDriverDTO != null) {
      data['listDriverDTO'] = this.listDriverDTO.map((v) => v.toJson()).toList();
    }
    if (this.listBookCarPartnerDetail != null) {
      data['listBookCarPartnerDetail'] = this.listBookCarPartnerDetail.map((v) => v.toJson()).toList();
    }
    if (this.listFromAddress != null) {
      data['listFromAddress'] = this.listFromAddress.map((v) => v.toJson()).toList();
    }
    if (this.listToAddress != null) {
      data['listToAddress'] = this.listToAddress.map((v) => v.toJson()).toList();
    }
    data['costEstimate'] = this.costEstimate;
    if (this.listCatVehiclePartner != null) {
      data['listCatVehiclePartner'] = this.listCatVehiclePartner.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class CraveCarDto {
  String defaultSortField;
  int messageColumn;
  int createdBy;
  int status;
  int role;
  bool isSize;
  String fromAddress;
  String toAddress;
  String licenseCar;
  String fullName;
  int fwmodelId;
  int appParamId;
  int bookCarPartnerId;
  String parOrder;
  String createdByName;
  String parType;
  String name;
  String code;
  int catVehiclePartnerId;
  String licenseCarPartner;
  int carTypeId;
  int carTypeName;
  int contractId;
  int typeBookCar;
  int sysGroupId;
  int driverPartnerId;
  String contractCode;
  String contractName;
  String partnerCode;
  String partnerName;
  double weight;
  String descriptionWork;
  String startTimeEstimate;
  String endTimeEstimate;
  String startTimeEstimateStr;
  String endTimeEstimateStr;
  String sysGroupLevel2;
  String email;
  String phoneNumber;
  String stockTransCode;
  double costEstimate;
  List<Product> listBookCarPartnerDetail;

  CraveCarDto(
      {this.defaultSortField,
      this.messageColumn,
      this.createdBy,
      this.isSize,
      this.typeBookCar,
      this.fromAddress,
      this.licenseCar,
      this.fullName,
      this.toAddress,
      this.licenseCarPartner,
      this.appParamId,
      this.bookCarPartnerId,
      this.createdByName,
      this.parOrder,
      this.parType,
      this.name,
      this.code,
      this.carTypeId,
      this.carTypeName,
      this.contractId,
      this.sysGroupId,
      this.contractCode,
      this.contractName,
      this.partnerCode,
      this.partnerName,
      this.weight,
      this.status,
      this.role,
      this.driverPartnerId,
      this.descriptionWork,
      this.startTimeEstimate,
      this.endTimeEstimate,
      this.startTimeEstimateStr,
      this.endTimeEstimateStr,
      this.costEstimate,
      this.listBookCarPartnerDetail,
      this.catVehiclePartnerId,
      this.stockTransCode,
      this.sysGroupLevel2,
      this.email,
      this.phoneNumber,
      this.fwmodelId});

  CraveCarDto.fromJson(Map<String, dynamic> json) {
    defaultSortField = json['defaultSortField'];
    messageColumn = json['messageColumn'];
    createdBy = json['createdBy'];
    typeBookCar = json['typeBookCar'];
    isSize = json['isSize'];
    licenseCar = json['licenseCar'];
    fullName = json['fullName'];
    createdByName = json['createdByName'];
    status = json['status'];
    fromAddress = json['fromAddress'];
    toAddress = json['toAddress'];
    licenseCarPartner = json['licenseCarPartner'];
    appParamId = json['appParamId'];
    bookCarPartnerId = json['bookCarPartnerId'];
    parOrder = json['parOrder'];
    stockTransCode = json['stockTransCode'];
    parType = json['parType'];
    name = json['name'];
    code = json['code'];
    driverPartnerId = json['driverPartnerId'];
    carTypeId = json['carTypeId'];
    carTypeName = json['carTypeName'];
    contractId = json['contractId'];
    sysGroupId = json['sysGroupId'];
    contractCode = json['contractCode'];
    contractName = json['contractName'];
    partnerCode = json['partnerCode'];
    partnerName = json['partnerName'];
    weight = json['weight'];
    descriptionWork = json['descriptionWork'];
    startTimeEstimate = json['startTimeEstimate'];
    endTimeEstimate = json['endTimeEstimate'];
    startTimeEstimateStr = json['startTimeEstimateStr'];
    endTimeEstimateStr = json['endTimeEstimateStr'];
    costEstimate = json['costEstimate'];
    catVehiclePartnerId = json['catVehiclePartnerId'];
    sysGroupLevel2 = json['sysGroupLevel2'];
    email = json['email'];
    phoneNumber = json['phoneNumber'];
    if (json['listBookCarPartnerDetail'] != null) {
      listBookCarPartnerDetail = new List<Product>();
      json['listBookCarPartnerDetail'].forEach((v) {
        listBookCarPartnerDetail.add(new Product.fromJson(v));
      });
    }
    fwmodelId = json['fwmodelId'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['defaultSortField'] = this.defaultSortField;
    data['typeBookCar'] = this.typeBookCar;
    data['messageColumn'] = this.messageColumn;
    data['createdBy'] = this.createdBy;
    data['licenseCar'] = this.licenseCar;
    data['fullName'] = this.fullName;
    data['createdByName'] = this.createdByName;
    data['isSize'] = this.isSize;
    data['fromAddress'] = this.fromAddress;
    data['status'] = this.status;
    data['toAddress'] = this.toAddress;
    data['licenseCarPartner'] = this.licenseCarPartner;
    data['appParamId'] = this.appParamId;
    data['bookCarPartnerId'] = this.bookCarPartnerId;
    data['parOrder'] = this.parOrder;
    data['parType'] = this.parType;
    data['name'] = this.name;
    data['code'] = this.code;
    data['driverPartnerId'] = this.driverPartnerId;
    data['carTypeId'] = this.carTypeId;
    data['carTypeName'] = this.carTypeName;
    data['contractId'] = this.contractId;
    data['sysGroupId'] = this.sysGroupId;
    data['contractCode'] = this.contractCode;
    data['contractName'] = this.contractName;
    data['stockTransCode'] = this.stockTransCode;
    data['partnerCode'] = this.partnerCode;
    data['partnerName'] = this.partnerName;
    data['weight'] = this.weight;
    data['descriptionWork'] = this.descriptionWork;
    data['startTimeEstimate'] = this.startTimeEstimate;
    data['endTimeEstimate'] = this.endTimeEstimate;
    data['startTimeEstimateStr'] = this.startTimeEstimateStr;
    data['endTimeEstimateStr'] = this.endTimeEstimateStr;
    data['costEstimate'] = this.costEstimate;
    data['catVehiclePartnerId'] = this.catVehiclePartnerId;
    data['sysGroupLevel2'] = this.sysGroupLevel2;
    data['email'] = this.email;
    data['phoneNumber'] = this.phoneNumber;
    if (this.listBookCarPartnerDetail != null) {
      data['listBookCarPartnerDetail'] = this.listBookCarPartnerDetail.map((v) => v.toJson()).toList();
    }
    data['fwmodelId'] = this.fwmodelId;
    return data;
  }
}

class CraveCarRequest {
  String fromAddress;
  String toAddress;
  String licenseCar;
  int typeBookCar;
  String sysUserId;
  String sysGroupId;
  int status;
  int bookCarPartnerId;
  String code;
  String reason;
  int role;
  String licenseCarPartner;
  String contractCode;
  String partnerCode;
  String partnerName;
  double weight;
  int contractId;
  double costEstimate;
  CraveCarDto bookCarPartnerDTO;
  int driverPartnerId;

  CraveCarRequest(
      {this.fromAddress,
      this.toAddress,
      this.licenseCar,
      this.typeBookCar,
      this.sysUserId,
      this.bookCarPartnerDTO,
      this.status,
      this.bookCarPartnerId,
      this.code,
      this.reason,
      this.role,
      this.licenseCarPartner,
      this.contractCode,
      this.partnerCode,
      this.partnerName,
      this.weight,
      this.contractId,
      this.costEstimate,
      this.driverPartnerId,
      this.sysGroupId});

  CraveCarRequest.fromJson(Map<String, dynamic> json) {
    fromAddress = json['fromAddress'];
    toAddress = json['toAddress'];
    licenseCar = json['licenseCar'];
    typeBookCar = json['typeBookCar'];
    sysUserId = json['sysUserId'];
    sysGroupId = json['sysGroupId'];
    status = json['status'];
    bookCarPartnerId = json['bookCarPartnerId'];
    code = json['code'];
    reason = json['reason'];
    role = json['role'];
    licenseCarPartner = json['licenseCarPartner'];
    contractCode = json['contractCode'];
    partnerCode = json['partnerCode'];
    partnerName = json['partnerName'];
    weight = json['weight'];
    contractId = json['contractId'];
    costEstimate = json['costEstimate'];
    driverPartnerId = json['driverPartnerId'];
    bookCarPartnerDTO = json['bookCarPartnerDTO'] != null ? new CraveCarDto.fromJson(json['bookCarPartnerDTO']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['fromAddress'] = this.fromAddress;
    data['toAddress'] = this.toAddress;
    data['licenseCar'] = this.licenseCar;
    data['typeBookCar'] = this.typeBookCar;
    data['sysUserId'] = this.sysUserId;
    data['sysGroupId'] = this.sysGroupId;
    data['status'] = this.status;
    data['bookCarPartnerId'] = this.bookCarPartnerId;
    data['code'] = this.code;
    data['reason'] = this.reason;
    data['role'] = this.role;
    data['licenseCarPartner'] = this.licenseCarPartner;
    data['contractCode'] = this.contractCode;
    data['partnerCode'] = this.partnerCode;
    data['partnerName'] = this.partnerName;
    data['weight'] = this.weight;
    data['contractId'] = this.contractId;
    data['costEstimate'] = this.costEstimate;
    data['driverPartnerId'] = this.driverPartnerId;
    if (this.bookCarPartnerDTO != null) {
      data['bookCarPartnerDTO'] = this.bookCarPartnerDTO.toJson();
    }
    return data;
  }
}
