class DriverPartnerDTO {
  String defaultSortField;
  int messageColumn;
  bool isSize;
  int totalRecord;
  int driverPartnerId;
  String driverPartnerName;
  String driverPartnerEmail;
  String phoneNumber;
  int contractId;
  String contractCode;
  String contractName;
  String partnerName;
  int status;
  String createdDate;
  int createdBy;
  int fwmodelId;

  DriverPartnerDTO(
      {this.defaultSortField,
      this.messageColumn,
      this.isSize,
      this.totalRecord,
      this.driverPartnerId,
      this.driverPartnerName,
      this.driverPartnerEmail,
      this.phoneNumber,
      this.contractId,
      this.contractCode,
      this.contractName,
      this.partnerName,
      this.status,
      this.createdDate,
      this.createdBy,
      this.fwmodelId});

  DriverPartnerDTO.fromJson(Map<String, dynamic> json) {
    defaultSortField = json['defaultSortField'];
    messageColumn = json['messageColumn'];
    isSize = json['isSize'];
    totalRecord = json['totalRecord'];
    driverPartnerId = json['driverPartnerId'];
    driverPartnerName = json['driverPartnerName'];
    driverPartnerEmail = json['driverPartnerEmail'];
    phoneNumber = json['phoneNumber'];
    contractId = json['contractId'];
    contractCode = json['contractCode'];
    contractName = json['contractName'];
    partnerName = json['partnerName'];
    status = json['status'];
    createdDate = json['createdDate'];
    createdBy = json['createdBy'];
    fwmodelId = json['fwmodelId'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['defaultSortField'] = this.defaultSortField;
    data['messageColumn'] = this.messageColumn;
    data['isSize'] = this.isSize;
    data['totalRecord'] = this.totalRecord;
    data['driverPartnerId'] = this.driverPartnerId;
    data['driverPartnerName'] = this.driverPartnerName;
    data['driverPartnerEmail'] = this.driverPartnerEmail;
    data['phoneNumber'] = this.phoneNumber;
    data['contractId'] = this.contractId;
    data['contractCode'] = this.contractCode;
    data['contractName'] = this.contractName;
    data['partnerName'] = this.partnerName;
    data['status'] = this.status;
    data['createdDate'] = this.createdDate;
    data['createdBy'] = this.createdBy;
    data['fwmodelId'] = this.fwmodelId;
    return data;
  }
}
