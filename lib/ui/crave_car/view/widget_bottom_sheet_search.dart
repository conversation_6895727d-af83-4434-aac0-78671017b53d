import 'package:flutter/material.dart';
import 'package:trackcarvcc/constants/style/style.dart';

class WidgetBottomSheetSearch extends StatefulWidget {
  const WidgetBottomSheetSearch({Key key, this.title, this.widget, this.onChanged, this.isVisibility = false})
      : super(key: key);

  final String title;
  final Widget widget;
  final ValueChanged<String> onChanged;
  final bool isVisibility;

  @override
  _WidgetBottomSheetSearchState createState() => _WidgetBottomSheetSearchState();
}

class _WidgetBottomSheetSearchState extends State<WidgetBottomSheetSearch> {
  @override
  Widget build(BuildContext context) {
    return _itemStartPoint(context);
  }

  _itemStartPoint(BuildContext context) => Padding(
        padding: const EdgeInsets.all(5),
        child: Column(
          children: [
            Row(children: <Widget>[
              Expanded(
                  child: Center(
                child: Text(
                  widget.title ?? "",
                  style: CommonTextStyle.textStyleFontLatoNormalBold,
                ),
              )),
              IconButton(
                icon: const Icon(
                  Icons.close,
                  size: 23,
                  color: AppThemes.colorViettelBlack,
                ),
                onPressed: () {
                  Navigator.of(context).pop();
                },
              )
            ]),
            Divider(color: AppThemes.colorViettelGray3, height: 1),
            widget.isVisibility
                ? SizedBox()
                : Row(
                    children: [
                      Expanded(
                        child: TextField(
                          // controller: _controller,
                          decoration: InputDecoration(
                            hintText: 'Tìm kiếm',
                            border: InputBorder.none,
                            contentPadding: const EdgeInsets.only(top: mPadding, bottom: mPadding, right: mPadding),
                            hintStyle: CommonTextStyle.textStyleFontLatoNormalHint,
                            isDense: true,
                          ),
                          cursorColor: AppThemes.colorViettelRed,
                          autofocus: false,
                          style: CommonTextStyle.textStyleFontLatoNormal,
                          onChanged: widget.onChanged,
                        ),
                      ),
                      Container(
                        width: 45,
                        height: 45,
                        padding: const EdgeInsets.all(mPadding),
                        margin: const EdgeInsets.only(bottom: 0),
                        //check password field type
                        child: IconButton(
                          onPressed: () {
                            //FocusScope.of(context).requestFocus(_focusNode);
                          },
                          icon: Icon(Icons.search),
                          padding: const EdgeInsets.all(0),
                        ),
                      )
                    ],
                  ),
            Divider(color: AppThemes.colorViettelGray3, height: 1),
            SizedBox(height: 10),
            Expanded(child: widget.widget)
          ],
        ),
      );
}
