import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:trackcarvcc/constants/style/style.dart';

class WidgetInput extends StatefulWidget {
  const WidgetInput(
      {Key key,
      this.label,
      this.iconData,
      this.controller,
      this.hindText,
      this.keyboardType,
      this.maxLength,
      this.maxLines,
      this.onChanged,
      this.inputFormatters,
      this.enabled = true,
      this.suffixIcon = true})
      : super(key: key);

  final String label;
  final IconData iconData;
  final TextEditingController controller;
  final String hindText;
  final TextInputType keyboardType;
  final int maxLength;
  final int maxLines;
  final bool enabled;
  final bool suffixIcon;
  final ValueChanged<String> onChanged;
  final List<TextInputFormatter> inputFormatters;

  @override
  _WidgetInputState createState() => _WidgetInputState();
}

class _WidgetInputState extends State<WidgetInput> {
  @override
  Widget build(BuildContext context) {
    return body(context);
  }

  body(BuildContext context) => Padding(
        padding: const EdgeInsets.only(top: mPaddingLarge),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Icon(
                      widget.iconData ?? Icons.label,
                      color: AppThemes.colorViettelRed,
                      size: 15,
                    ),
                    const SizedBox(
                      width: mPadding,
                    ),
                    Text(
                      widget.label ?? "",
                      style: CommonTextStyle.textStyleFontLatoNormal,
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: mPadding),
            Container(
              padding: EdgeInsets.only(left: 5),
              decoration: BoxDecoration(
                color: AppThemes.colorViettelGray3,
                borderRadius: const BorderRadius.all(
                  Radius.circular(mRadiusSmall),
                ),
                border: Border.all(color: AppThemes.colorViettelGray2),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: widget.controller,
                      keyboardType: widget.keyboardType,
                      maxLength: widget.maxLength,
                      inputFormatters: widget.inputFormatters,
                      enabled: widget.enabled,
                      style: TextStyle(fontSize: 14),
                      maxLines: widget.maxLines,
                      onChanged: widget.onChanged,
                      decoration: InputDecoration(hintText: widget.hindText ?? "Nhập dữ liệu", border: InputBorder.none),
                    ),
                  ),
                  widget.suffixIcon
                      ? Container(
                          width: 50.0,
                          height: 50.0,
                          padding: const EdgeInsets.all(mPadding),
                          margin: const EdgeInsets.only(bottom: 0),
                          //check password field type
                          child: Icon(Icons.edit),
                        )
                      : SizedBox()
                ],
              ),
            ),
          ],
        ),
      );
}
