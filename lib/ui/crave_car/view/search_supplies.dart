import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:trackcarvcc/ui/crave_car/controller/search_supplies_controller.dart';

class SearchSupplies extends SearchDelegate {
  SearchProductController controller = Get.find<SearchProductController>();

  @override
  List<Widget> buildActions(BuildContext context) {
    return <Widget>[
      IconButton(
          icon: Icon(Icons.close),
          onPressed: () {
            query = "";
          })
    ];
  }

  @override
  Widget buildLeading(BuildContext context) {
    return IconButton(
        icon: Icon(Icons.arrow_back_ios),
        onPressed: () {
          Get.back();
        });
  }

  @override
  Widget buildResults(BuildContext context) {
    return Container(
      child: Text("Result"),
    );
  }

  @override
  Widget buildSuggestions(BuildContext context) {
    controller.getGoods(query);

    return Obx(() => ListView.builder(
        itemCount: controller.listProduct.length,
        itemBuilder: (context, index) {
          return GestureDetector(
            onTap: () {
              Get.back(result: controller.listProduct[index]);
            },
            child: Card(
              child: Container(
                  padding: EdgeInsets.all(10),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      Text(controller.listProduct[index].goodName, style: TextStyle(fontSize: 15)),
                      SizedBox(height: 5),
                      Text("Mã hàng: ${controller.listProduct[index].goodCode}")
                    ],
                  )),
            ),
          );
        }));
  }
}
