import 'dart:ui';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:trackcarvcc/constants/app_state.dart';
import 'package:trackcarvcc/constants/style/style.dart';
import 'package:trackcarvcc/helpers/extensions.dart';
import 'package:trackcarvcc/repository/preferences/data_center.dart';
import 'package:trackcarvcc/routes/routes.dart';
import 'package:trackcarvcc/ui/crave_car/controller/list_crave_car_controller.dart';
import 'package:trackcarvcc/ui/login/widget/custom_text_form_field.dart';
import 'package:trackcarvcc/widget/circle_loading.dart';

class ListCraveCarPage extends GetView<ListCraveCarController> {
  @override
  Widget build(BuildContext context) {
    // TODO: implement build
    return GetBuilder(initState: (_) {
      controller.getCheckPermission();
    }, builder: (_) {
      return Scaffold(
          backgroundColor: Color(0xFFF5F5F5),
          appBar: AppBar(
              title: Text('Danh sách phiếu xuất'),
              leading: IconButton(
                icon: const Icon(
                  Icons.menu,
                  color: Colors.white,
                ),
                onPressed: () {
                  Scaffold.of(context).openDrawer();
                },
              ),
              centerTitle: true,
              backgroundColor: Color(0xFFEE0033),
              actions: [_buildFilter()]),
          body: Column(
            children: [
              //Search bar
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: CustomTextFormField(
                  controller: controller.searchController,
                  hintText: "Tìm kiếm",
                  prefixIcon: Icon(Icons.search),
                  onChanged: (value) {
                    controller.fetchData(controller.role, value);
                  },
                ),
              ),
              Expanded(
                child: Obx(() {
                  if (controller.appState() == AppState.LOADING) {
                    return _buildLoading();
                  }

                  if (controller.appState() == AppState.ERROR) {
                    return _buildErrorWidget();
                  }
                  if (controller.listCraveCar.isEmpty) {
                    return _buildNoDataWidget();
                  } else {
                    return ListView.builder(
                      itemCount: controller.listCraveCar.length,
                      padding: EdgeInsets.symmetric(horizontal: 5),
                      itemBuilder: (context, index) {
                        return buildItem(index, context);
                      },
                    );
                  }
                }),
              ),
            ],
          ),
          floatingActionButton: FloatingActionButton(
            backgroundColor: Color(0xFFEE0033),
            child: Icon(Icons.add, color: Colors.white),
            onPressed: () {
              Get.toNamed(Routers.crave_car_page).whenComplete(() => controller.getCheckPermission());
            },
          ));
    });
  }

  buildItem(int index, BuildContext context) {
    String status = "";
    Color colorStatus = Color(0xFFEE0033);
    switch (controller.listCraveCar[index].status) {
      case 0:
        status = "Đã hủy";
        colorStatus = Color(0xFFEE0033);
        break;
      case 1:
        status = "Mới tạo";
        colorStatus = Color(0xFF4F7FFA);
        break;
      case 2:
        status = "Chờ duyệt";
        colorStatus = Color(0xFFF2994A);
        break;
      case 3:
        status = "GĐ.CNKT phê duyệt";
        colorStatus = Color(0xFF00C851);
        break;
      case 4:
        status = "GĐ.CNKT từ chối";
        colorStatus = Color(0xFFEE0033);
        break;
      case 5:
        status = "P.Mua sắm phê duyệt";
        colorStatus = Color(0xFF408080);
        break;
      case 6:
        status = "P.Mua sắm từ chối";
        colorStatus = Color(0xFFEE0033);
        break;
      case 7:
        status = "TP.Mua sắm phê duyệt";
        colorStatus = Color(0xFF1F8C60);
        break;
      case 8:
        status = "TP.Mua sắm từ chối";
        colorStatus = Color(0xFFEE0033);
        break;
      default:
        status = "Chưa xác định";
        colorStatus = Color(0xFF23212F);
        break;
    }
    return GestureDetector(
      onTap: () {
        controller.listCraveCar[index].role = controller.role;
        Get.toNamed(Routers.crave_car_page, arguments: controller.listCraveCar[index])
            .whenComplete(() => controller.getCheckPermission());
      },
      child: Card(
        elevation: 5,
        child: Container(
          margin: EdgeInsets.all(10),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Row(
                children: <Widget>[
                  Expanded(
                      child: Text("${index + 1}. ${controller.listCraveCar[index].code}",
                          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 13))),
                  controller.listCraveCar[index].status == 1 &&
                          controller.listCraveCar[index].createdBy == DataCenter.shared().getUserInfo().sysUserId
                      ? GestureDetector(
                          onTap: () {
                            showConfirmDialog(context, 'Bạn có chắc chắn muốn xóa bản ghi?', yesCallBack: () {
                              controller.updateStatusBookCarPartner(controller.listCraveCar[index].bookCarPartnerId, 0);
                            });
                          },
                          child: Icon(Icons.clear))
                      : SizedBox()
                ],
              ),
              SizedBox(height: 10),
              Container(
                color: Color(0xfff9f9f9),
                // height: 100,
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Expanded(
                      flex: 1,
                      child: Padding(
                        padding: const EdgeInsets.all(12.0),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Xuất phát',
                              style: TextStyle(
                                color: Color(0xff969696),
                                fontSize: 14,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              controller.listCraveCar[index]?.fromAddress ?? '',
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                              style: TextStyle(
                                color: Colors.black,
                                fontSize: 14,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              controller.listCraveCar[index]?.startTimeEstimateStr ?? '',
                              style: TextStyle(
                                color: Color(0xffd58431),
                                fontSize: 12,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    Container(width: 1, height: 90, color: Color(0xFFE0E0E0)),
                    Expanded(
                      flex: 1,
                      child: Padding(
                        padding: const EdgeInsets.all(12.0),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Đích đến',
                              style: TextStyle(
                                color: Color(0xff969696),
                                fontSize: 14,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              controller.listCraveCar[index]?.toAddress ?? '',
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                              style: TextStyle(
                                color: Colors.black,
                                fontSize: 14,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              controller.listCraveCar[index]?.endTimeEstimateStr ?? '',
                              style: TextStyle(
                                color: Color(0xffd58431),
                                fontSize: 12,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(height: 5),
              Text("Người tạo: ${controller.listCraveCar[index].createdByName ?? ""}"),
              SizedBox(height: 5),
              Obx(() => Row(children: <Widget>[
                    Expanded(child: Text("Trạng thái: $status", style: TextStyle(color: colorStatus))),
                    Visibility(
                        visible: controller.listCraveCar[index].status == 1 &&
                            controller.listCraveCar[index].createdBy == DataCenter.shared().getUserInfo().sysUserId,
                        child: GestureDetector(
                            onTap: () {
                              showConfirmDialog(context, 'Bạn có chắc chắn muốn gửi bản ghi?', yesCallBack: () {
                                controller.updateStatusBookCarPartner(
                                    controller.listCraveCar[index].bookCarPartnerId, 2);
                              });
                            },
                            child: Icon(Icons.send, color: Colors.green)))
                  ])),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLoading() => Container(
        color: AppThemes.colorViettelGray3,
        child: LoadingCircle(),
      );

  Widget _buildNoDataWidget() => Center(
        child: Text("Không có dữ liệu!"),
      );

  Widget _buildErrorWidget() => Center(
        child: Text("Có lỗi xảy ra. Vui lòng thử lại sau."),
      );

  _buildFilter() {
    return PopupMenuButton(
        icon: Icon(Icons.filter_alt_sharp, color: Colors.white),
        onSelected: (value) {
          controller.listCraveCar.clear();
          switch (value) {
            case -1:
              controller.listCraveCar.addAll(controller.backupListCraveCar);
              break;
            case 1:
              controller.listCraveCar.addAll(controller.backupListCraveCar.where((order) => order.status == 1));
              break;
            case 2:
              controller.listCraveCar.addAll(controller.backupListCraveCar.where((order) => order.status == 2));
              break;
            case 3:
              controller.listCraveCar.addAll(controller.backupListCraveCar.where((order) => order.status == 3));
              break;
            case 4:
              controller.listCraveCar.addAll(controller.backupListCraveCar.where((order) => order.status == 4));
              break;
            case 5:
              controller.listCraveCar.addAll(controller.backupListCraveCar.where((order) => order.status == 5));
              break;
            case 6:
              controller.listCraveCar.addAll(controller.backupListCraveCar.where((order) => order.status == 6));
              break;
            case 7:
              controller.listCraveCar.addAll(controller.backupListCraveCar.where((order) => order.status == 7));
              break;
            case 8:
              controller.listCraveCar.addAll(controller.backupListCraveCar.where((order) => order.status == 8));
              break;
            case 0:
              controller.listCraveCar.addAll(controller.backupListCraveCar.where((order) => order.status == 0));
              break;
          }
        },
        itemBuilder: (context) => [
              PopupMenuItem(child: Text("Tất cả", style: TextStyle(fontSize: 14, color: Colors.black)), value: -1),
              PopupMenuItem(child: Text("Mới tạo", style: TextStyle(fontSize: 14, color: Colors.black)), value: 1),
              PopupMenuItem(child: Text("Chờ duyệt", style: TextStyle(fontSize: 14, color: Colors.black)), value: 2),
              PopupMenuItem(
                  child: Text("GĐ.CNKT phê duyệt", style: TextStyle(fontSize: 14, color: Colors.black)), value: 3),
              PopupMenuItem(
                  child: Text("GĐ.CNKT từ chối", style: TextStyle(fontSize: 14, color: Colors.black)), value: 4),
              PopupMenuItem(
                  child: Text("P.Mua sắm phê duyệt", style: TextStyle(fontSize: 14, color: Colors.black)), value: 5),
              PopupMenuItem(
                  child: Text("P.Mua sắm từ chối", style: TextStyle(fontSize: 14, color: Colors.black)), value: 6),
              PopupMenuItem(
                  child: Text("TP.Mua sắm phê duyệt", style: TextStyle(fontSize: 14, color: Colors.black)), value: 7),
              PopupMenuItem(
                  child: Text("TP.Mua sắm từ chối", style: TextStyle(fontSize: 14, color: Colors.black)), value: 8),
              PopupMenuItem(child: Text("Đã hủy", style: TextStyle(fontSize: 14, color: Colors.black)), value: 0),
            ]);
  }
}
