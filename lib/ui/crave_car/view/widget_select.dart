import 'package:flutter/material.dart';
import 'package:trackcarvcc/constants/style/style.dart';

class WidgetSelect extends StatefulWidget {
  const WidgetSelect({Key key, this.label, this.iconData, this.onTap, this.value}) : super(key: key);

  final String label;
  final IconData iconData;
  final GestureTapCallback onTap;
  final String value;

  @override
  _WidgetSelectState createState() => _WidgetSelectState();
}

class _WidgetSelectState extends State<WidgetSelect> {
  @override
  Widget build(BuildContext context) {
    return body(context);
  }

  body(BuildContext context) => Padding(
        padding: const EdgeInsets.only(top: mPaddingLarge),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Icon(
                      widget.iconData ?? Icons.label,
                      color: AppThemes.colorViettelRed,
                    ),
                    const SizedBox(
                      width: mPadding,
                    ),
                    Text(
                      widget.label ?? "",
                      style: CommonTextStyle.textStyleFontLatoNormal,
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: mPadding),
            GestureDetector(
              onTap: widget.onTap,
              child: Container(
                padding: EdgeInsets.only(left: 5),
                decoration: BoxDecoration(
                  color: AppThemes.colorViettelGray3,
                  borderRadius: const BorderRadius.all(
                    Radius.circular(mRadiusSmall),
                  ),
                  border: Border.all(color: AppThemes.colorViettelGray2),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(widget.value ?? "Chọn",
                          style:
                              TextStyle(color: widget.value == null ? Color(0xFFC2C2C2) : Colors.black, fontSize: 13)),
                    ),
                    Container(
                      width: 50.0,
                      height: 50.0,
                      padding: const EdgeInsets.all(mPadding),
                      margin: const EdgeInsets.only(bottom: 0),
                      child: widget.onTap != null ? Icon(Icons.navigate_next) : null,
                    )
                  ],
                ),
              ),
            ),
          ],
        ),
      );
}
