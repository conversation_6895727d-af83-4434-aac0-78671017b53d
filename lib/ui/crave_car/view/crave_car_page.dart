import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:trackcarvcc/constants/constants.dart';
import 'package:trackcarvcc/constants/style/style.dart';
import 'package:trackcarvcc/helpers/extensions.dart';
import 'package:trackcarvcc/repository/preferences/data_center.dart';
import 'package:trackcarvcc/ui/car_order_creation/widget/item_finish_time_widget.dart';
import 'package:trackcarvcc/ui/car_order_creation/widget/item_start_time_widget.dart';
import 'package:trackcarvcc/ui/crave_car/controller/crave_car_controller.dart';
import 'package:trackcarvcc/ui/crave_car/controller/search_supplies_controller.dart';
import 'package:trackcarvcc/ui/crave_car/model/crave_car_response.dart';
import 'package:trackcarvcc/ui/crave_car/model/product.dart';
import 'package:trackcarvcc/ui/crave_car/view/search_supplies.dart';
import 'package:trackcarvcc/ui/crave_car/view/widget_bottom_sheet_search.dart';
import 'package:trackcarvcc/ui/crave_car/view/widget_input.dart';
import 'package:trackcarvcc/ui/crave_car/view/widget_select.dart';
import 'package:trackcarvcc/widget/common_button.dart';

enum PageTypeCraveCar { CREATE_ORDER, UPDATE_ORDER }

// ignore: must_be_immutable
class CraveCarPage extends GetView<CraveCarController> {
  final TextEditingController _textTimeStartDialogController = TextEditingController(text: Constants.TIMER_DEFAULT);
  final TextEditingController _textTimeFinishDialogController = TextEditingController(text: Constants.TIMER_END_DEFAULT);

  final PageTypeCraveCar pageType;

  final CraveCarDto craveCarDto;

  CraveCarPage({this.pageType, this.craveCarDto});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<CraveCarController>(initState: (_) {
      if (pageType == PageTypeCraveCar.UPDATE_ORDER) {
        controller.craveCarDto = craveCarDto;
        WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
          controller.syncBookCarDtoInfo();
          controller.getToAddress("");
          controller.getFromAddress("");
          controller.getDescriptionWork();
          controller.getTypeBookCarPartner();
          controller.getLicenseCarPartner("");
        });
      } else {
        controller.init();
        WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
          controller.fetchUserInfoAndPrepareInfo();
          controller.genCode();
          controller.getToAddress("");
          controller.getFromAddress("");
          controller.getDescriptionWork();
          controller.getTypeBookCarPartner();
          controller.getLicenseCarPartner("");
        });
      }
    }, builder: (_) {
      return Scaffold(
        appBar: AppBar(
          backgroundColor: AppThemes.colorViettelRed,
          brightness: Brightness.light,
          elevation: 1.0,
          leading: IconButton(
            icon: Icon(
              Icons.arrow_back,
              color: Colors.white,
            ),
            onPressed: () {
              Get.back();
            },
          ),
          centerTitle: true,
          title: Text(
            pageType == PageTypeCraveCar.CREATE_ORDER ? 'Xin xe đối tác' : 'Cập nhật thông tin phiếu xin xe',
            style: CommonTextStyle.textStyleFontLatoLargeBoldWhite,
          ),
        ),
        backgroundColor: Colors.white,
        body: _body(context),
      );
    });
  }

  _body(BuildContext context) => GestureDetector(
        onTap: () {
          FocusScope.of(context).requestFocus(FocusNode());
        },
        child: Container(
          padding: const EdgeInsets.only(left: mPaddingLarge, right: mPaddingLarge, bottom: mPaddingLarge),
          child: Stack(
            children: [
              SingleChildScrollView(
                child: Column(
                  children: [
                    const SizedBox(
                      height: mPaddingLarge,
                    ),
                    _userCreationInfo(),
                    const SizedBox(
                      height: mPaddingXXLarge,
                    ),
                    _carOrderCreationInfo(context),
                    const SizedBox(
                      height: mPaddingXXLarge * 2.5,
                    ),
                  ],
                ),
              ),
              pageType == PageTypeCraveCar.CREATE_ORDER ? Positioned(bottom: 0, left: 0, right: 0, child: _buttonSave(context)) : SizedBox(),
              Positioned(
                  bottom: 0,
                  left: 0,
                  right: 0,
                  child: Row(children: <Widget>[
                    pageType == PageTypeCraveCar.UPDATE_ORDER &&
                            craveCarDto != null &&
                            craveCarDto.status == 1 &&
                            craveCarDto.createdBy == DataCenter.shared().getUserInfo().sysUserId
                        ? Expanded(child: _buttonSave(context))
                        : SizedBox(),
                    pageType == PageTypeCraveCar.UPDATE_ORDER &&
                            (craveCarDto.role == 1 && craveCarDto.status == 2 ||
                                craveCarDto.role == 2 && craveCarDto.status == 3 ||
                                craveCarDto.role == 3 && craveCarDto.status == 5)
                        ? Expanded(child: _buttonReject(context))
                        : SizedBox(),
                    pageType == PageTypeCraveCar.UPDATE_ORDER &&
                            (craveCarDto.role == 1 && craveCarDto.status == 2 ||
                                craveCarDto.role == 2 && craveCarDto.status == 3 ||
                                craveCarDto.role == 3 && craveCarDto.status == 5)
                        ? Expanded(child: _buttonApprove(context))
                        : SizedBox()
                  ])),
            ],
          ),
        ),
      );

  /// ==============================
  /// User creation info
  _userCreationInfo() => Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _title(title: 'Thông tin người tạo'),
          Obx(() => _itemUserCreationInfo(label: 'Đơn vị', userInfo: controller.userUnit.value)),
          Obx(() => _itemUserCreationInfo(label: 'Họ tên', userInfo: controller.userName.value)),
          Obx(() => _itemUserCreationInfo(label: 'Email', userInfo: controller.userEmail.value)),
          Obx(() => _itemUserCreationInfo(
              label: 'Số điện thoại',
              userInfo: controller.userPhone.value,
              userInfoColor: AppThemes.colorViettelRed,
              onClick: () {
                controller.makeAction('tel:${controller.userPhone.value}');
              }))
        ],
      );

  _itemUserCreationInfo({String label, String userInfo, VoidCallback onClick, Color userInfoColor}) => InkResponse(
        onTap: () {
          if (onClick != null) {
            onClick();
          }
        },
        child: Column(
          children: [
            Padding(
              padding: const EdgeInsets.only(top: mPaddingXMedium, bottom: mPaddingXMedium),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    label,
                    style: CommonTextStyle.textStyleFontLatoNormal,
                  ),
                  Flexible(
                    child: Text(
                      userInfo,
                      maxLines: 2,
                      textAlign: TextAlign.right,
                      style: userInfoColor != null
                          ? CommonTextStyle.textStyleFontLatoNormalBold.copyWith(color: userInfoColor)
                          : CommonTextStyle.textStyleFontLatoNormalBold,
                    ),
                  )
                ],
              ),
            ),
            Divider(
              height: 1,
              thickness: 1,
              color: AppThemes.colorViettelGray3,
            )
          ],
        ),
      );

  _carOrderCreationInfo(BuildContext context) => Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _title(title: 'Thông tin đặt xe'),
          Obx(() => WidgetSelect(label: "Mã phiếu đặt xe", iconData: Icons.code, value: controller.code.value)),
          Obx(() => WidgetSelect(
              label: "Điểm khởi hành",
              iconData: Icons.location_on_outlined,
              value: controller.startPoint.value,
              onTap: () {
                if (pageType == PageTypeCraveCar.CREATE_ORDER ||
                    (pageType == PageTypeCraveCar.UPDATE_ORDER &&
                        craveCarDto.createdBy == DataCenter.shared().getUserInfo().sysUserId &&
                        craveCarDto.status == 1)) {
                  Get.bottomSheet(
                      WidgetBottomSheetSearch(
                          title: "Điểm khởi hành",
                          widget: _itemStartPoint(),
                          onChanged: (value) {
                            controller.getFromAddress(value);
                          }),
                      backgroundColor: Colors.white);
                }
              })),
          Obx(() => WidgetSelect(
              label: "Điểm đến",
              iconData: Icons.location_on_outlined,
              value: controller.destinationPoint.value,
              onTap: () {
                if (pageType == PageTypeCraveCar.CREATE_ORDER ||
                    (pageType == PageTypeCraveCar.UPDATE_ORDER &&
                        craveCarDto.createdBy == DataCenter.shared().getUserInfo().sysUserId &&
                        craveCarDto.status == 1)) {
                  Get.bottomSheet(
                      WidgetBottomSheetSearch(
                          title: "Điểm đến",
                          widget: _itemDestinationPoint(),
                          onChanged: (value) {
                            controller.getToAddress(value);
                          }),
                      backgroundColor: Colors.white);
                }
              })),
          _itemTimeRange(),
          WidgetInput(
              label: "Nội dung công việc",
              enabled: pageType == PageTypeCraveCar.CREATE_ORDER ||
                  (pageType == PageTypeCraveCar.UPDATE_ORDER &&
                      craveCarDto.createdBy == DataCenter.shared().getUserInfo().sysUserId &&
                      craveCarDto.status == 1),
              iconData: Icons.sticky_note_2_outlined,
              maxLines: 3,
              controller: controller.contentWorkController,
              hindText: "Nhập nội dung"),
          Obx(() => WidgetSelect(
              label: "Chọn kiểu đi",
              iconData: Icons.compare_arrows,
              value: controller.typeBookCarPartner.value,
              onTap: () {
                if (pageType == PageTypeCraveCar.CREATE_ORDER ||
                    (pageType == PageTypeCraveCar.UPDATE_ORDER &&
                        craveCarDto.createdBy == DataCenter.shared().getUserInfo().sysUserId &&
                        craveCarDto.status == 1)) {
                  Get.bottomSheet(WidgetBottomSheetSearch(title: "Chọn kiểu đi", isVisibility: true, widget: _itemTypeBookCarPartner()),
                      backgroundColor: Colors.white);
                }
              })),
          Row(children: <Widget>[
            Expanded(
                child: Obx(() => WidgetSelect(
                    label: "Chọn xe",
                    iconData: Icons.directions_car,
                    value: controller.licenseCarPartner.value,
                    onTap: () {
                      if (pageType == PageTypeCraveCar.CREATE_ORDER ||
                          (pageType == PageTypeCraveCar.UPDATE_ORDER && craveCarDto.role == 2 && craveCarDto.status == 3) ||
                          (craveCarDto.createdBy == DataCenter.shared().getUserInfo().sysUserId && craveCarDto.status == 1)) {
                        Get.bottomSheet(
                            WidgetBottomSheetSearch(
                                title: "Chọn xe",
                                widget: _itemCarVehiclePartner(),
                                onChanged: (value) {
                                  controller.getLicenseCarPartner(value);
                                }),
                            backgroundColor: Colors.white);
                      }
                    }))),
            SizedBox(width: 5),
            Expanded(
                child: Obx(() => WidgetSelect(label: "Trọng lượng", iconData: Icons.line_weight_sharp, value: controller.weightCar.value.toString())))
          ]),
          WidgetInput(
              label: "Chi phí dự kiến",
              enabled: pageType == PageTypeCraveCar.UPDATE_ORDER && craveCarDto != null && craveCarDto.role == 2 && craveCarDto.status == 3,
              iconData: Icons.monetization_on_outlined,
              controller: controller.costEstimateController,
              hindText: "Nhập chi phí dự kiến"),
          WidgetInput(
              label: "Mã phiếu",
              enabled: pageType == PageTypeCraveCar.CREATE_ORDER ||
                  (pageType == PageTypeCraveCar.UPDATE_ORDER && craveCarDto.createdBy == DataCenter.shared().getUserInfo().sysUserId),
              iconData: Icons.code,
              controller: controller.codeController,
              hindText: "Nhập mã phiếu"),
          Obx(() => WidgetSelect(label: "Đối tác", iconData: Icons.people_alt_rounded, value: controller.partnerName.value ?? "")),
          craveCarDto != null && craveCarDto.role == 2 && craveCarDto.status == 3
              ? Obx(() => WidgetSelect(
                  label: "Chọn lái xe",
                  iconData: Icons.car_rental,
                  value: controller.driverPartnerDTO.value != null ? controller.driverPartnerDTO.value.driverPartnerName : null,
                  onTap: () {
                    controller.getListDriverCar("");
                    Get.bottomSheet(
                        WidgetBottomSheetSearch(
                            title: "Chọn lái xe",
                            widget: _itemDriverPartner(),
                            onChanged: (value) {
                              controller.getListDriverCar(value);
                            }),
                        backgroundColor: Colors.white);
                  }))
              : SizedBox(),
          SizedBox(height: 10),
          _itemListProduct(context)
        ],
      );

  _itemListProduct(BuildContext context) {
    return Column(
      children: <Widget>[
        GestureDetector(
          onTap: () async {
            if (pageType == PageTypeCraveCar.CREATE_ORDER ||
                (pageType == PageTypeCraveCar.UPDATE_ORDER &&
                    craveCarDto.createdBy == DataCenter.shared().getUserInfo().sysUserId &&
                    craveCarDto.status == 1)) {
              // Get.dialog(
              //   showDialogAddProduct(),
              // );

              Get.lazyPut(() => SearchProductController(controller.api));
              var result = await showSearch(context: context, delegate: SearchSupplies()) as Product;

              if (result != null) {
                for (var product in controller.listProduct) {
                  if (result.name == product.name && result.goodCode == product.goodCode) {
                    Fluttertoast.showToast(msg: "Vật tư đã được chọn!");
                    return;
                  }
                }
                controller.listProduct.add(result);
              }
            }
          },
          child: Row(children: <Widget>[
            Icon(Icons.list, color: AppThemes.colorViettelRed),
            SizedBox(width: 5),
            Expanded(child: Text("Danh sách hàng hóa", style: CommonTextStyle.textStyleFontLatoNormal)),
            Icon(Icons.add, color: Colors.green)
          ]),
        ),
        SizedBox(height: 5),
        Obx(() => controller.listProduct.length != 0
            ? Column(
                children: controller.listProduct.map((element) {
                  var position = controller.listProduct.indexOf(element);
                  return itemGood(position, element);
                }).toList(),
              )
            : SizedBox())
      ],
    );
  }

  Widget itemGood(int position, Product element) {
    return Stack(
      children: <Widget>[
        Card(
          margin: EdgeInsets.symmetric(vertical: 10),
          color: AppThemes.colorViettelGray3,
          child: Row(
            children: <Widget>[
              Text("${position + 1}. "),
              Expanded(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Text("${element.goodName ?? ""}", style: TextStyle(color: Color(0xFF172B4D), fontWeight: FontWeight.bold)),
                    Text("Mã hàng: ${element.goodCode ?? ""}"),
                    Row(
                      children: <Widget>[
                        Expanded(
                            child: WidgetInput(
                          suffixIcon: false,
                          onChanged: (value) {
                            element.amount = double.parse(value);
                          },
                          controller: TextEditingController(text: element.amount?.toString()),
                          keyboardType: TextInputType.numberWithOptions(decimal: true),
                          inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'(^\d*\.?\d*)'))],
                          label: "S.lượng",
                          hindText: "1, 2, 3 ...",
                          iconData: Icons.confirmation_number_outlined,
                        )),
                        SizedBox(width: 5),
                        Expanded(
                            child: WidgetInput(
                          suffixIcon: false,
                          onChanged: (value) {
                            element.weightGoods = double.parse(value);
                            element.weight = double.parse(value);
                          },
                          controller: TextEditingController(text: element.weightGoods?.toString()),
                          label: "K.lượng",
                          keyboardType: TextInputType.number,
                          hindText: "Tấn...",
                          iconData: Icons.confirmation_number_outlined,
                        )),
                        SizedBox(width: 5)
                      ],
                    ),
                    SizedBox(height: 5)
                  ],
                ),
              )
            ],
          ),
        ),
        Positioned(
          right: 0,
          child: GestureDetector(
            onTap: () {
              controller.listProduct.remove(element);
            },
            child: Container(
              width: 20,
              height: 20,
              decoration: new BoxDecoration(
                color: Colors.black,
                shape: BoxShape.circle,
              ),
              child: new Icon(
                Icons.clear,
                size: 15,
                color: Colors.white,
              ),
            ),
          ),
        )
      ],
    );
  }

  _itemStartPoint() => Obx(
        () => ListView.builder(
            itemCount: controller.listStartPoint.length,
            itemBuilder: (context, index) {
              return Padding(
                  padding: const EdgeInsets.all(mPadding),
                  child: GestureDetector(
                      onTap: () {
                        controller.startPoint.value = controller.listStartPoint[index].fromAddress;
                        controller.getCostEstimate();
                        Get.back();
                      },
                      child: Text(
                        controller.listStartPoint[index].fromAddress,
                        style: CommonTextStyle.textStyleFontLatoNormal,
                      )));
            }),
      );

  _itemDestinationPoint() => Obx(
        () => ListView.builder(
            itemCount: controller.listDestinationPoint.length,
            itemBuilder: (context, index) {
              return Padding(
                  padding: const EdgeInsets.all(mPadding),
                  child: GestureDetector(
                      onTap: () {
                        controller.destinationPoint.value = controller.listDestinationPoint[index].toAddress;
                        controller.getCostEstimate();
                        Get.back();
                      },
                      child: Text(
                        controller.listDestinationPoint[index].toAddress,
                        style: CommonTextStyle.textStyleFontLatoNormal,
                      )));
            }),
      );

  _itemTypeBookCarPartner() => Obx(
        () => ListView.builder(
            itemCount: controller.listTypeBookCarPartner.length,
            itemBuilder: (context, index) {
              return Padding(
                  padding: const EdgeInsets.all(mPadding),
                  child: GestureDetector(
                      onTap: () {
                        controller.typeBookCarPartner.value = controller.listTypeBookCarPartner[index].name;
                        controller.typeBookCar.value = int.parse(controller.listTypeBookCarPartner[index].code);
                        controller.getCostEstimate();
                        Get.back();
                      },
                      child: Text(
                        controller.listTypeBookCarPartner[index].name,
                        style: CommonTextStyle.textStyleFontLatoNormal,
                      )));
            }),
      );

  _itemDriverPartner() => Obx(
        () => ListView.builder(
            itemCount: controller.listDriverPartner.length,
            itemBuilder: (context, index) {
              return Padding(
                  padding: const EdgeInsets.all(mPadding),
                  child: GestureDetector(
                      onTap: () {
                        controller.driverPartnerDTO.value = controller.listDriverPartner[index];
                        Get.back();
                      },
                      child: Text(
                        controller.listDriverPartner[index].driverPartnerName,
                        style: CommonTextStyle.textStyleFontLatoNormal,
                      )));
            }),
      );

  _itemCarVehiclePartner() => Obx(
        () => ListView.builder(
            itemCount: controller.listCarVehiclePartner.length,
            itemBuilder: (context, index) {
              return Padding(
                  padding: const EdgeInsets.all(mPadding),
                  child: GestureDetector(
                      onTap: () {
                        controller.licenseCarDto.value = controller.listCarVehiclePartner[index];
                        controller.licenseCarPartner.value = controller.listCarVehiclePartner[index].licenseCarPartner;
                        controller.weightCar.value = controller.listCarVehiclePartner[index].weight;
                        controller.partnerName.value = controller.listCarVehiclePartner[index].partnerName;
                        controller.getCostEstimate();
                        Get.back();
                      },
                      child: Row(children: <Widget>[
                        Expanded(
                            child: Text(
                          "${controller.listCarVehiclePartner[index].licenseCarPartner}",
                          style: CommonTextStyle.textStyleFontLatoNormal,
                        )),
                        Expanded(
                            child: Text(
                          "${controller.listCarVehiclePartner[index].weight}",
                          style: CommonTextStyle.textStyleFontLatoNormal,
                          textAlign: TextAlign.center,
                        ))
                      ])));
            }),
      );

  _itemTimeRange() => Container(
        width: double.infinity,
        padding: const EdgeInsets.only(top: mPaddingLarge),
        child: Row(
          children: [
            _itemStartTime(),
            const SizedBox(
              width: mPaddingLarge,
            ),
            _itemFinishTime()
          ],
        ),
      );

  _itemStartTime() => Obx(
        () => ItemStartTime(
          isClick: pageType == PageTypeCraveCar.CREATE_ORDER ||
              (pageType == PageTypeCraveCar.UPDATE_ORDER &&
                  craveCarDto.createdBy == DataCenter.shared().getUserInfo().sysUserId &&
                  craveCarDto.status == 1),
          controller: controller.timeStartController,
          timeController: _textTimeStartDialogController,
          isValidate: controller.timeFromToIsValid,
          onChanged: controller.changeTimeStart,
        ),
      );

  _itemFinishTime() => Obx(
        () => ItemFinishTime(
          isClick: pageType == PageTypeCraveCar.CREATE_ORDER ||
              (pageType == PageTypeCraveCar.UPDATE_ORDER &&
                  craveCarDto.createdBy == DataCenter.shared().getUserInfo().sysUserId &&
                  craveCarDto.status == 1),
          controller: controller.timeFinishController,
          timeController: _textTimeFinishDialogController,
          isValidate: controller.timeFromToIsValid,
          onChanged: controller.changeTimeFinish,
        ),
      );

  _buttonSave(BuildContext context) => Padding(
        padding: const EdgeInsets.only(top: mPaddingXLarge, right: 2, left: 2),
        child: CommonButton(
          title: pageType == PageTypeCraveCar.CREATE_ORDER ? 'Đặt xe' : 'Cập nhật',
          textColor: Colors.white,
          onButtonClick: () {
            FocusScope.of(context).requestFocus(FocusNode());
            if (controller.validate) {
              showConfirmDialog(context,
                  pageType == PageTypeCraveCar.CREATE_ORDER ? 'Bạn có chắc chắn muốn tạo phiếu?' : 'Bạn có chắc chắn với thông tin vừa cập nhật?',
                  yesCallBack: () {
                pageType == PageTypeCraveCar.CREATE_ORDER ? controller.addBookCarPartner() : controller.updateBookCarPartner();
              });
            } else {
              showErrorToast(error: 'Bạn chưa điền đẩy đủ thông tin \n hoặc thông tin đã nhập chưa đúng!');
            }
          },
        ),
      );

  _buttonApprove(BuildContext context) => Padding(
        padding: const EdgeInsets.only(top: mPaddingXLarge, right: 2, left: 2),
        child: CommonButton(
          title: 'Phê duyệt',
          textColor: Colors.white,
          onButtonClick: () {
            FocusScope.of(context).requestFocus(FocusNode());
            showConfirmDialog(context, 'Bạn có chắc chắn xác nhận phiếu yêu cầu?', yesCallBack: () {
              CraveCarRequest body = CraveCarRequest(
                  sysUserId: DataCenter.shared().getUserInfo().sysUserId.toString(),
                  bookCarPartnerId: controller.craveCarDto.bookCarPartnerId,
                  status: controller.craveCarDto.role == 1
                      ? 3
                      : controller.craveCarDto.role == 2
                          ? 5
                          : controller.craveCarDto.role == 3
                              ? 7
                              : null,
                  driverPartnerId: controller.driverPartnerDTO.value != null ? controller.driverPartnerDTO.value.driverPartnerId : null,
                  licenseCarPartner: controller.craveCarDto.role == 2 ? controller.licenseCarDto.value.licenseCarPartner : null,
                  contractId: controller.craveCarDto.role == 2 ? controller.licenseCarDto.value.contractId : null,
                  contractCode: controller.craveCarDto.role == 2 ? controller.licenseCarDto.value.contractCode : null,
                  partnerCode: controller.craveCarDto.role == 2 ? controller.licenseCarDto.value.partnerCode : null,
                  partnerName: controller.craveCarDto.role == 2 ? controller.licenseCarDto.value.partnerName : null,
                  weight: controller.craveCarDto.role == 2 ? controller.weightCar.value : null,
                  costEstimate: controller.craveCarDto.role == 2 ? double.parse(controller.costEstimateController.text) : null);
              controller.craveCarDto.role == 1 ? controller.appProveRejectGDCNKT(body) : controller.appProveRejectQLTS(body);
            });
          },
        ),
      );

  _buttonReject(BuildContext context) => Padding(
        padding: const EdgeInsets.only(top: mPaddingXLarge, right: 2, left: 2),
        child: CommonButton(
          title: 'Từ chối',
          textColor: Colors.white,
          onButtonClick: () {
            FocusScope.of(context).requestFocus(FocusNode());
            showInputConfirmDialog(context, "Từ chối", 'Bạn có chắc chắn muốn từ chối phiếu yêu cầu?', yesCallBack: (value) {
              CraveCarRequest body = CraveCarRequest(
                  sysUserId: DataCenter.shared().getUserInfo().sysUserId.toString(),
                  bookCarPartnerId: controller.craveCarDto.bookCarPartnerId,
                  status: controller.craveCarDto.role == 1
                      ? 4
                      : controller.craveCarDto.role == 2
                          ? 6
                          : controller.craveCarDto.role == 3
                              ? 8
                              : null,
                  reason: value);
              controller.craveCarDto.role == 1 ? controller.appProveRejectGDCNKT(body) : controller.appProveRejectQLTS(body);
            });
          },
        ),
      );

  _title({String title}) => Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(title, style: CommonTextStyle.textStyleFontLatoRedNormal),
          const SizedBox(height: mPaddingXSmall),
          Divider(
            height: 1,
            thickness: 1,
            color: AppThemes.colorViettelRed,
          )
        ],
      );
}
