import 'package:flutter/material.dart';
import 'package:trackcarvcc/constants/style/style.dart';

class ItemPoint extends StatelessWidget {
  final IconData icon;
  final String label;
  final String text;

  const ItemPoint({Key key, this.icon, this.label, this.text})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: mPaddingLarge),
      child: Column(
        children: [
          Row(
            children: [
              Icon(
                icon,
                color: AppThemes.colorViettelRed,
              ),
              const SizedBox(
                width: mPadding,
              ),
              Text(
                label ?? '',
                style: CommonTextStyle.textStyleFontLatoNormal,
              ),
            ],
          ),
          const SizedBox(height: mPadding),
          Container(
            // height: 50.0,
            alignment: Alignment.centerLeft,
            padding: const EdgeInsets.all(mPadding),
            decoration: BoxDecoration(
              color: AppThemes.colorViettelGray3,
              borderRadius: const BorderRadius.all(
                Radius.circular(mRadiusSmall),
              ),
              border: Border.all(color: AppThemes.colorViettelGray2),
            ),
            child: Text(
              text ?? '',
              style: CommonTextStyle.textStyleFontLatoNormal,
            ),
          ),
        ],
      ),
    );
  }
}
