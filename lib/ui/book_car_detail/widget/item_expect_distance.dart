import 'package:flutter/material.dart';
import 'package:trackcarvcc/constants/style/style.dart';
import 'package:trackcarvcc/models/models.dart';

class ItemExpectDistance extends StatelessWidget {
  final LstBookCarDto bookCar;

  const ItemExpectDistance({Key key, this.bookCar}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: mPaddingLarge),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.add_road_outlined,
                color: AppThemes.colorViettelRed,
              ),
              const SizedBox(width: mPadding),
              Text(
                bookCar.internalProvince != 1 ? 'Đi ngoại tỉnh' : 'Đi nội tỉnh',
                style: CommonTextStyle.textStyleFontLatoNormal,
              ),
            ],
          ),
          const SizedBox(height: mPadding),
          Text(
            'Dự kiến quãng đường là: ' +
                bookCar.estimateDistance.toString() +
                ' km',
            style: CommonTextStyle.textStyleFontLatoNormal,
          ),
          const SizedBox(height: mPadding),
          Text(
            'Dự kiến thời gian là: ${bookCar?.estimateTime ?? ''}',
            style: CommonTextStyle.textStyleFontLatoNormal,
          ),
        ],
      ),
    );
  }
}
