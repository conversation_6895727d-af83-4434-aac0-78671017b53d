import 'package:flutter/material.dart';
import 'package:trackcarvcc/constants/style/style.dart';
import 'package:trackcarvcc/models/models.dart';

class ItemUserAccept extends StatelessWidget {
  final List<UserLogin> listUserManager;

  const ItemUserAccept({Key key, this.listUserManager}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: mPaddingLarge),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.verified_user,
                color: AppThemes.colorViettelRed,
              ),
              const SizedBox(
                width: mPadding,
              ),
              Text(
                'Người phê duyệt',
                style: CommonTextStyle.textStyleFontLatoNormal,
              ),
            ],
          ),
          const SizedBox(height: mPadding),
          Container(
            decoration: BoxDecoration(
              color: AppThemes.colorViettelGray3,
              borderRadius: const BorderRadius.all(
                Radius.circular(mRadiusSmall),
              ),
              border: Border.all(color: AppThemes.colorViettelGray2),
            ),
            child: _buildListTagForm(),
          ),
        ],
      ),
    );
  }

  _buildListTagForm() {
    if (listUserManager == null || listUserManager.isEmpty) {
      return Container();
    }
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(mPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: listUserManager
            .map((item) => _buildTagItemForm(
                  item.fullName,
                  item.sysUserId,
                  _getStatus(item.status),
                  _getColor(item.status),
                ))
            .toList()
            .cast<Widget>(),
      ),
    );
  }

  _buildTagItemForm(String userName, int userId, String status, Color color) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4.0),
      child: Chip(
        label: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(userName, style: CommonTextStyle.textStyleFontLatoNormal),
            const SizedBox(width: 4),
            Text(
              status,
              style: CommonTextStyle.textStyleFontLatoNormal.copyWith(
                color: color,
                fontStyle: FontStyle.italic,
                fontSize: mFontSizeSmall,
              ),
            ),
          ],
        ),
        labelPadding: const EdgeInsets.symmetric(horizontal: mPaddingSmall),
        backgroundColor: AppThemes.colorViettelGray2.withOpacity(0.5),
        materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(mRadiusSmall),
        ),
        padding: const EdgeInsets.only(left: 0, right: 0, bottom: 0, top: 0),
      ),
    );
  }

  String _getStatus(String status) {
    switch (status) {
      case "1":
        return 'Đang chờ duyệt';
      case "2":
        return 'Đã được duyệt';
      case "3":
        return 'Từ chối';
      case "4":
        return 'Yêu cầu sửa';
      case "5":
        return 'Đã đóng lệnh';
      case "6":
        return 'Chờ đóng lệnh';
      default:
        return '';
    }
  }

  Color _getColor(String status) {
    switch (status) {
      case "1":
        return Colors.grey;
      case "2":
        return Colors.green;
      case "3":
        return Colors.red;
      case "4":
        return Color(0xFFFFA726);
      case "5":
        return Colors.green;
      case "6":
        return Colors.grey;
      default:
        return Colors.grey;
    }
  }
}
