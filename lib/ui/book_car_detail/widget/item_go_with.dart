import 'package:flutter/material.dart';
import 'package:trackcarvcc/constants/style/style.dart';
import 'package:trackcarvcc/models/models.dart';

class ItemGoWith extends StatelessWidget {
  final List<UserLogin> listUserTogeter;

  const ItemGoWith({Key key, this.listUserTogeter}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: mPaddingLarge),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.person_outline,
                color: AppThemes.colorViettelRed,
              ),
              const SizedBox(
                width: mPadding,
              ),
              Text(
                'Người đi cùng',
                style: CommonTextStyle.textStyleFontLatoNormal,
              ),
              const SizedBox(
                width: mPadding,
              ),
              Text(
                '(đã chọn ${listUserTogeter?.length ?? 0} người',
                style: CommonTextStyle.textStyleFontLatoRedNormal,
              ),
            ],
          ),
          const SizedBox(height: mPadding),
          Container(
            decoration: BoxDecoration(
              color: AppThemes.colorViettelGray3,
              borderRadius: const BorderRadius.all(
                Radius.circular(mRadiusSmall),
              ),
              border: Border.all(color: AppThemes.colorViettelGray2),
            ),
            child: _buildListTagForm(),
          ),
        ],
      ),
    );
  }

  _buildListTagForm() {
    if (listUserTogeter == null || listUserTogeter.isEmpty) {
      return Container();
    }
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(mPadding),
      child: Wrap(
        spacing: 6.0,
        runSpacing: 6.0,
        direction: Axis.horizontal,
        children: listUserTogeter
            .map((item) => _buildTagItemForm(item.fullName, item.sysUserId))
            .toList()
            .cast<Widget>(),
      ),
    );
  }

  _buildTagItemForm(String userName, int userId) {
    return Chip(
      label: Text(userName, style: CommonTextStyle.textStyleFontLatoNormal),
      labelPadding: const EdgeInsets.symmetric(horizontal: mPaddingSmall),
      backgroundColor: AppThemes.colorViettelGray2.withOpacity(0.5),
      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(mRadiusSmall),
      ),
      padding: const EdgeInsets.only(left: 0, right: 0, bottom: 0, top: 0),
    );
  }
}
