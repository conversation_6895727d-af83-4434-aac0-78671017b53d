import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:trackcarvcc/constants/constants.dart';
import 'package:trackcarvcc/constants/style/style.dart';
import 'package:trackcarvcc/controllers/controllers.dart';
import 'package:trackcarvcc/helpers/extensions.dart';
import 'package:trackcarvcc/helpers/string_utils.dart';
import 'package:trackcarvcc/models/models.dart';
import 'package:trackcarvcc/repository/api/urls.dart';
import 'package:trackcarvcc/routes/routes.dart';
import 'package:trackcarvcc/ui/book_car_detail/widget/item_expect_distance.dart';
import 'package:trackcarvcc/ui/book_car_detail/widget/item_go_with.dart';
import 'package:trackcarvcc/ui/book_car_detail/widget/item_point.dart';
import 'package:trackcarvcc/ui/book_car_detail/widget/item_user_accept.dart';
import 'package:trackcarvcc/ui/book_car_detail/widget/search_car_widget.dart';
import 'package:trackcarvcc/ui/book_car_detail/widget/search_driver_widget.dart';
import 'package:trackcarvcc/widget/circle_loading.dart';
import 'package:trackcarvcc/widget/common_button.dart';
import 'package:trackcarvcc/widget/view_file_attachment.dart';

class BookCarDetailPage extends GetView<BookCarDetailController> {
  BookCarDetailPage({this.bookCar, this.typeMenu});

  final LstBookCarDto bookCar;
  final int typeMenu;

  @override
  Widget build(BuildContext context) {
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      controller.getListAttachment(bookCar.bookCarId);
    });
    return Scaffold(
      appBar: AppBar(
        backgroundColor: AppThemes.colorViettelRed,
        brightness: Brightness.light,
        elevation: 1.0,
        leading: IconButton(
          icon: const Icon(
            Icons.arrow_back,
            color: Colors.white,
          ),
          onPressed: () {
            Get.back();
          },
        ),
        centerTitle: true,
        title: Text(
          'Thông tin đặt xe',
          style: CommonTextStyle.textStyleFontLatoLargeBoldWhite,
        ),
      ),
      backgroundColor: Colors.white,
      body: GetBuilder<BookCarDetailController>(
        initState: (_) {
          controller.bookCar = bookCar;
          controller.typeMenu = typeMenu;
          controller.init();
        },
        builder: (_) {
          return Obx(() {
            if (controller.appState() == AppState.LOADING) {
              return _buildLoading();
            }

            if (controller.appState() == AppState.ERROR) {
              return _buildErrorWidget();
            }
            return _body(context);
          });
        },
      ),
    );
  }

  Widget _buildLoading() => Container(
        color: Colors.white,
        child: LoadingCircle(),
      );

  Widget _buildErrorWidget() => Center(
        child: Text("Có lỗi xảy ra. Vui lòng thử lại sau."),
      );

  _body(BuildContext context) => GestureDetector(
        onTap: () {
          FocusScope.of(context).requestFocus(FocusNode());
        },
        child: Stack(
          children: [
            Positioned(
              top: 0,
              bottom: controller.hideBottom ? 0 : 84,
              left: 0,
              right: 0,
              child: SingleChildScrollView(
                physics: BouncingScrollPhysics(),
                padding: const EdgeInsets.only(
                    left: mPaddingLarge, right: mPaddingLarge),
                child: Column(
                  children: [
                    const SizedBox(
                      height: mPaddingLarge,
                    ),
                    _vehicleRouteInfo(),
                    _carInfoAndDriver(context),
                    _userCreationInfo(),
                    const SizedBox(
                      height: mPaddingXXLarge,
                    ),
                    _carOrderCreationInfo(),
                    const SizedBox(
                      height: mPaddingLarge,
                    ),
                  ],
                ),
              ),
            ),
            _bottomButton(context),
          ],
        ),
      );

  _vehicleRouteInfo() => Offstage(
        offstage: typeMenu != Constants.TYPE_ACCEPT_WORK,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _title(title: 'Thông tin lộ trình xe'),
            const SizedBox(height: 8),
            Card(
              elevation: 8.0,
              color: Colors.white,
              margin: const EdgeInsets.all(8.0),
              shadowColor: Colors.grey,
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Flexible(
                          child: Text(
                            'Lộ trình, khoảng cách, thời gian dự kiến ban đầu',
                            style: TextStyle(
                              fontSize: mFontSizeMedium,
                              color: Color(0xff99cc00),
                            ),
                          ),
                        ),
                        IconButton(
                          onPressed: () {
                            controller.getApiMatricTest();
                          },
                          icon: Icon(
                            Icons.remove_red_eye,
                            color: Color(0xff008577),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Flexible(
                          child: Text(
                            'Lộ trình, khoảng cách, thời gian dự kiến hiện tại',
                            style: TextStyle(
                              fontSize: mFontSizeMedium,
                              color: Color(0xffFFA726),
                            ),
                          ),
                        ),
                        IconButton(
                          onPressed: () {
                            controller.openNavigationCurrent();
                          },
                          icon: Icon(
                            Icons.remove_red_eye,
                            color: Color(0xff008577),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 8),
          ],
        ),
      );

  _carInfoAndDriver(BuildContext context) => Offstage(
        offstage: !(typeMenu == Constants.TYPE_ARRANGE_CAR_MANAGER ||
            !StringUtils.isEmpty(bookCar.licenseCar) ||
            !StringUtils.isEmpty(bookCar.driverName)),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _title(title: 'Thông tin xe & lái xe'),
            const SizedBox(height: 8),
            GestureDetector(
              onTap: typeMenu != Constants.TYPE_ARRANGE_CAR_MANAGER ||
                      (typeMenu == Constants.TYPE_ARRANGE_CAR_MANAGER &&
                          (bookCar.statusCaptainCar != "1" ||
                              bookCar.toAddressExtend != null))
                  ? null
                  : () {
                      showModalBottomSheet(
                        context: context,
                        isScrollControlled: true,
                        backgroundColor: Colors.transparent,
                        builder: (BuildContext context) {
                          return SearchCar(
                            isPairing: controller.pairingCar() == 1,
                            onChanged: (car) {
                              controller.changeCarLicence(car);
                            },
                          );
                        },
                      );
                    },
              child:
                  Obx(() => _itemCarInfo('Xe', controller.carLicence() ?? '')),
            ),
            GestureDetector(
              onTap: typeMenu != Constants.TYPE_ARRANGE_CAR_MANAGER ||
                      (typeMenu == Constants.TYPE_ARRANGE_CAR_MANAGER &&
                          (bookCar.statusCaptainCar != "1" ||
                              bookCar.toAddressExtend != null))
                  ? null
                  : () {
                      showModalBottomSheet(
                        context: context,
                        isScrollControlled: true,
                        backgroundColor: Colors.transparent,
                        builder: (BuildContext context) {
                          return SearchDriver(
                            isPairing: controller.pairingCar() == 1,
                            onChanged: (driver) {
                              controller.changeCarDriver(
                                  driver.driverName,
                                  driver.phoneNumberDriver,
                                  driver.driverId,
                                  driver.driverCode);
                            },
                          );
                        },
                      );
                    },
              child: Obx(
                () => _itemCarInfo(
                  'Người lái',
                  controller.carDriver() ?? '',
                  phone: controller.driverPhoneNumber() ?? '',
                  onClick: () {
                    controller
                        .makeAction('tel:${controller.driverPhoneNumber()}');
                  },
                ),
              ),
            ),
            Offstage(
              offstage:
                  !("1" == bookCar.typeBookCar || "2" == bookCar.typeBookCar),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                    'Ghép xe',
                    style: CommonTextStyle.textStyleFontLatoNormal,
                  ),
                  Obx(
                    () => Checkbox(
                      value: controller.pairingCar() != null
                          ? controller.pairingCar() == 1
                          : false,
                      onChanged: typeMenu !=
                                  Constants.TYPE_ARRANGE_CAR_MANAGER ||
                              (typeMenu == Constants.TYPE_ARRANGE_CAR_MANAGER &&
                                  (bookCar.statusCaptainCar != "1" ||
                                      bookCar.toAddressExtend != null))
                          ? null
                          : (isSelected) {
                              controller.pairingCar.value = isSelected ? 1 : 0;
                              if (!isSelected) {
                                controller.carLicence.value = '';
                                controller.carDriver.value = '';
                                controller.driverPhoneNumber.value = '';
                              }
                            },
                      checkColor: AppThemes.colorViettelRed,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 8),
          ],
        ),
      );

  _itemCarInfo(String label, String value,
          {String phone, VoidCallback onClick}) =>
      Container(
        margin: const EdgeInsets.only(bottom: 8),
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(color: AppThemes.colorViettelGray3),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              label,
              style: CommonTextStyle.textStyleFontLatoNormal,
            ),
            Spacer(),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  value ?? '',
                  maxLines: 2,
                  style: CommonTextStyle.textStyleFontLatoNormalBold,
                ),
                const SizedBox(height: 4),
                Offstage(
                  offstage: phone == null,
                  child: GestureDetector(
                    onTap: onClick,
                    child: Text(
                      phone ?? '',
                      style: CommonTextStyle.textStyleFontLatoNormalBold
                          .copyWith(color: AppThemes.colorViettelRed),
                    ),
                  ),
                ),
              ],
            ),
            Offstage(
              offstage: typeMenu != Constants.TYPE_ARRANGE_CAR_MANAGER,
              child: const SizedBox(width: 4),
            ),
            Offstage(
              offstage: typeMenu != Constants.TYPE_ARRANGE_CAR_MANAGER,
              child: Icon(Icons.keyboard_arrow_right, size: 32),
            ),
          ],
        ),
      );

  /// ==============================
  /// User creation info
  _userCreationInfo() => Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _title(title: 'Thông tin người tạo'),
          _itemUserCreationInfo(
              label: 'Đơn vị', userInfo: bookCar.departmentName),
          _itemUserCreationInfo(
              label: 'Họ tên', userInfo: bookCar.fullName),
          _itemUserCreationInfo(
              label: 'Email', userInfo: bookCar.email),
          _itemUserCreationInfo(
              label: 'Số điện thoại',
              userInfo: bookCar.phoneNumber ?? '',
              userInfoColor: AppThemes.colorViettelRed,
              onClick: () {
                controller.makeAction('tel:${bookCar.phoneNumber}');
              }),
        ],
      );

  _title({String title}) => Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(title, style: CommonTextStyle.textStyleFontLatoRedNormal),
          const SizedBox(height: mPaddingXSmall),
          Divider(
            height: 1,
            thickness: 1,
            color: AppThemes.colorViettelRed,
          )
        ],
      );

  _itemUserCreationInfo(
          {String label,
          String userInfo,
          VoidCallback onClick,
          Color userInfoColor}) =>
      InkResponse(
        onTap: onClick,
        child: Column(
          children: [
            Padding(
              padding: const EdgeInsets.only(
                  top: mPaddingXMedium, bottom: mPaddingXMedium),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    label,
                    style: CommonTextStyle.textStyleFontLatoNormal,
                  ),
                  Flexible(
                    child: Text(
                      userInfo,
                      maxLines: 2,
                      style: userInfoColor != null
                          ? CommonTextStyle.textStyleFontLatoNormalBold
                              .copyWith(color: userInfoColor)
                          : CommonTextStyle.textStyleFontLatoNormalBold,
                    ),
                  )
                ],
              ),
            ),
            Divider(
              height: 1,
              thickness: 1,
              color: AppThemes.colorViettelGray3,
            )
          ],
        ),
      );

  _carOrderCreationInfo() => Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _title(title: 'Thông tin đặt xe'),
          ItemPoint(
            icon: Icons.location_on_outlined,
            label: 'Điểm khởi hành',
            text: bookCar.fromAddress,
          ),
          // ItemPoint(
          //   icon: Icons.my_location_outlined,
          //   label: 'Điểm đến',
          //   text: bookCar.toAddress,
          // ),
          _bookAddress(bookCar),
          const SizedBox(height: mPadding),
          Text(
            'File đính kèm',
            style: CommonTextStyle.textStyleFontLatoNormal,
          ),
          if ((controller.listFile ?? []).isNotEmpty)
            Container(
              height: 100,
              child: ListView.builder(
                itemBuilder: (_, index) {
                  return Stack(
                    children: <Widget>[
                      InkWell(
                        onTap: () {
                          Get.to(
                            ViewFileAttachment(
                              fileUrl: BASE_URL +
                                  "BookCarRestService/service/showImage?fileName=" +
                                  controller.listFile[index].imagePath,
                              fileType: controller
                                  .listFile[index].filePath
                                  .split(".")
                                  .last ==
                                  "pdf"
                                  ? FileType.pdf
                                  : FileType.image,
                              downloadFile: () {
                                controller.downloadFile(
                                  filePath: controller
                                      .listFile[index].imagePath,
                                  fileName: controller
                                      .listFile[index].filePath
                                      .split("/")
                                      .last,
                                );
                              },
                            ),
                          );
                        },
                        child: Container(
                          margin: const EdgeInsets.only(right: 10.0),
                          padding: EdgeInsets.only(right: 5, top: 5),
                          child: new ClipRRect(
                            borderRadius: BorderRadius.circular(8.0),
                            child: Image.network(
                              BASE_URL +
                                  "BookCarRestService/service/showImage?fileName=" +
                                  controller.listFile[index].imagePath,
                              errorBuilder: (BuildContext context,
                                  Object exception,
                                  StackTrace stackTrace) {
                                return Image.asset(file_pdf);
                              },
                              fit: BoxFit.contain,
                            ),
                          ),
                        ),
                      ),
                      Positioned(
                        right: 10,
                        child: ClipOval(
                          child: Material(
                            color: Color(0xFF9B9B9B), // Button color
                            child: InkWell(
                              splashColor: Colors.red, // Splash color
                              onTap: () {
                                controller.listFile.removeAt(index);
                              },
                              child: SizedBox(
                                  width: 20,
                                  height: 20,
                                  child: Icon(
                                    Icons.clear,
                                    size: 15,
                                  )),
                            ),
                          ),
                        ),
                      )
                    ],
                  );
                },
                itemCount: (controller.listFile ?? []).length,
                shrinkWrap: true,
                scrollDirection: Axis.horizontal,
              ),
            ),
          ItemPoint(
            icon: Icons.category,
            label: 'Phục vụ cho lĩnh vực',
            text: bookCar.serveFieldName,
          ),
          Offstage(
            offstage: bookCar.toAddressExtend == null,
            child: ItemPoint(
              icon: Icons.my_location_outlined,
              label: 'Điểm đến tiếp theo',
              text: bookCar.toAddressExtend,
            ),
          ),
          Offstage(
            offstage: bookCar.contentExtend == null,
            child: ItemPoint(
              icon: Icons.sticky_note_2_outlined,
              label: 'Nội dung công việc tiếp theo',
              text: bookCar.contentExtend,
            ),
          ),
          ItemExpectDistance(bookCar: bookCar),
          _itemTimeRange(),
          ItemPoint(
            icon: Icons.sticky_note_2_outlined,
            label: 'Nội dung công việc',
            text: bookCar.content,
          ),
          ItemPoint(
            icon: Icons.directions_car,
            label: 'Loại xe',
            text: bookCar.carTypeName,
          ),
          ItemPoint(
            icon: Icons.compare_arrows,
            label: 'Kiểu đi',
            text: _typeBookCar(),
          ),
          ItemPoint(
            icon: Icons.line_weight_sharp,
            label: 'Trọng lượng(tấn)',
            text: bookCar.goodsWeight.toString(),
          ),
          _itemGoWith(),
          _itemUserAccept(),
        ],
      );

  _bookAddress(LstBookCarDto bookCar) => Padding(
        padding: const EdgeInsets.only(top: mPaddingLarge),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.my_location_outlined,
                  color: AppThemes.colorViettelRed,
                ),
                const SizedBox(
                  width: mPadding,
                ),
                Text(
                  'Danh sách điểm đến',
                  style: CommonTextStyle.textStyleFontLatoNormal,
                ),
              ],
            ),
            const SizedBox(height: mPadding),
            ListView.separated(
              itemCount: bookCar.bookCarAddress.length,
              separatorBuilder: (context, index) => const SizedBox(height: mPadding),
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemBuilder: (context, index) {
                var item = bookCar.bookCarAddress[index];
                return Container(
                  decoration: BoxDecoration(
                    color: AppThemes.colorViettelGray3,
                    borderRadius: const BorderRadius.all(
                      Radius.circular(mRadiusSmall),
                    ),
                    border: Border.all(color: (item.toAddress ?? '').isNotEmpty ? AppThemes.colorViettelGray2 : AppThemes.colorViettelRed),
                  ),
                  padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  child: Row(
                    children: [
                      Icon(Icons.location_on, color: AppThemes.colorViettelGray2),
                      const SizedBox(
                        width: mPadding,
                      ),
                      Expanded(
                        child: Text(
                          item.toAddress ?? '',
                          style: CommonTextStyle.textStyleFontLatoNormal.copyWith(
                            color: (item.toAddress ?? '').isEmpty ? AppThemes.colorViettelGray2 : AppThemes.colorViettelGray1,
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ],
        ),
      );

  _itemTimeRange() => Row(
        children: [
          _itemStartTime(),
          const SizedBox(width: mPaddingLarge),
          _itemFinishTime()
        ],
      );

  _itemStartTime() => Expanded(
        flex: 1,
        child: ItemPoint(
          icon: Icons.lock_clock,
          label: 'Khởi hành lúc',
          text: bookCar.startTime,
        ),
      );

  _itemFinishTime() => Expanded(
        flex: 1,
        child: ItemPoint(
          icon: Icons.lock_clock,
          label: 'Đến lúc',
          text: bookCar.endTimeExtend ?? bookCar.endTime,
        ),
      );

  _itemGoWith() => Obx(
        () => ItemGoWith(listUserTogeter: controller.listUserTogeter()),
      );

  _itemUserAccept() => Obx(
        () => ItemUserAccept(listUserManager: controller.listUserManager()),
      );

  String _typeBookCar() {
    switch (bookCar.typeBookCar) {
      case "1":
        return 'Một chiều';
      case "2":
        return 'Hai chiều';
      case "3":
        return 'Phát sinh';
      case "4":
        return 'Đặc biệt';
      case "5":
        return 'Xe tải';
      default:
        return '';
    }
  }

  _bottomButton(BuildContext context) => Positioned(
        bottom: 0,
        left: 0,
        right: 0,
        child: Offstage(
          offstage: controller.hideBottom,
          child: Padding(
            padding: const EdgeInsets.only(
              top: mPaddingXLarge,
              left: mPadding,
              right: mPadding,
              bottom: mPaddingLarge,
            ),
            child: Row(
              children: [
                controller.approveStatus
                    ? Expanded(
                        flex: 1,
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 4.0),
                          child: CommonButton(
                            title: controller.approveText,
                            textColor: Colors.white,
                            bgColor: Color(0xff2d9a59),
                            onButtonClick: _onApprove,
                          ),
                        ),
                      )
                    : SizedBox(),
                controller.denyStatus
                    ? Expanded(
                        flex: 1,
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 4.0),
                          child: CommonButton(
                            title: 'Từ chối',
                            textColor: Colors.white,
                            bgColor: Color(0xffff4444),
                            onButtonClick: _onDeny,
                          ),
                        ),
                      )
                    : SizedBox(),
                controller.editStatus
                    ? Expanded(
                        flex: 1,
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 4.0),
                          child: CommonButton(
                            title: 'Yêu cầu sửa',
                            textColor: Colors.white,
                            bgColor: Color(0xffff8800),
                            onButtonClick: _onEdit,
                          ),
                        ),
                      )
                    : SizedBox(),
                controller.closeStatus
                    ? Expanded(
                        flex: 1,
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 4.0),
                          child: CommonButton(
                            title: 'Đóng lệnh',
                            textColor: Colors.white,
                            bgColor: Color(0xffd58431),
                            onButtonClick: controller.closeBookCar,
                          ),
                        ),
                      )
                    : SizedBox(),
                controller.openStatus
                    ? Expanded(
                        flex: 1,
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 4.0),
                          child: CommonButton(
                            title: 'Mở lệnh',
                            textColor: Colors.white,
                            bgColor: Color(0xff2d9a59),
                            onButtonClick: _onOpenCommand,
                          ),
                        ),
                      )
                    : SizedBox(),
              ],
            ),
          ),
        ),
      );

  _onApprove() {
    if (typeMenu == Constants.TYPE_ARRANGE_CAR_MANAGER) {
      if (StringUtils.isEmpty(controller.carLicence())) {
        showErrorToast(error: 'Vui lòng chọn xe');
        return;
      }
      if (StringUtils.isEmpty(controller.carDriver())) {
        showErrorToast(error: 'Vui lòng chọn người lái');
        return;
      }
    }
    showInputConfirmDialog(Get.context, 'Phê duyệt', 'Ghi chú',
        yesCallBack: (value) {
      controller.swichFetchApi(2, value);
    });
  }

  _onDeny() {
    showInputConfirmDialog(Get.context, 'Lý do', 'Vui lòng nhập lý do từ chối?',
        requiredInput: true, yesCallBack: (value) {
      controller.swichFetchApi(3, value);
    });
  }

  _onEdit() {
    showInputConfirmDialog(Get.context, 'Yêu cầu sửa', 'Lý do yêu cầu sửa?',
        requiredInput: true, yesCallBack: (value) {
      controller.swichFetchApi(4, value);
    });
  }

  _onOpenCommand() async {
    final result = await Get.toNamed(Routers.open_command, arguments: {
      Constants.KEY_BOOK_CAR: bookCar,
      Constants.KEY_LIST_TOGETHER: controller.listUserTogeter
    });

    if (result != null && result) {
      Get.back(result: true);
    }
  }
}
