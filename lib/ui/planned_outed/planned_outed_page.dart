import 'package:flutter/material.dart';
import 'package:get/get.dart';
// import 'package:google_maps_controller/google_maps_controller.dart';
import 'package:trackcarvcc/constants/constants.dart';
import 'package:trackcarvcc/constants/style/style.dart';
import 'package:trackcarvcc/ui/planned_outed/planned_outed_controller.dart';
import 'package:vtmap_gl/vtmap_gl.dart';

class PlannedRouted extends GetView<PlannedOutedController> {
  const PlannedRouted({
    Key key,
    this.points,
  }) : super(key: key);

  // final String startPoint;
  // final String destinationPoint;
  final List<String> points;

  @override
  Widget build(BuildContext context) {
    return GetBuilder(
      didChangeDependencies: (_) {
        controller.checkPermissions();
      },
      initState: (state) async {
        // await controller.init(
        //   points: points,
        // );
      },
      builder: (_) {
        return Scaffold(
          appBar: AppBar(
            backgroundColor: AppThemes.colorViettelRed,
            brightness: Brightness.light,
            elevation: 1.0,
            leading: Icon<PERSON>utton(
              icon: const Icon(
                Icons.arrow_back,
                color: Colors.white,
              ),
              onPressed: () {
                Get.back();
              },
            ),
            centerTitle: true,
            title: Text(
              'Thông tin tuyến đường',
              style: CommonTextStyle.textStyleFontLatoLargeBoldWhite,
            ),
          ),
          // body: GoogleMaps(
          //   controller: controller.googleMapController,
          // ),
          body: VTMap(
            accessToken: Constants.ACCESS_TOKEN,
            onMapCreated: (c) {
              // controller.googleMapController = c;
              controller.onMapCreated(c, points);
            },
            initialCameraPosition: CameraPosition(target: LatLng(15.96130913439005, 108.1949226475952), zoom: 4),

          ),
        );
      },
    );
  }
}
