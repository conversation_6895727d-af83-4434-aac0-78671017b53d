import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'package:flutter_polyline_points/flutter_polyline_points.dart';

// import 'package:geocode/geocode.dart';
import 'package:get/get.dart';

// import 'package:google_maps_controller/google_maps_controller.dart';
import 'package:location/location.dart';
import 'package:trackcarvcc/constants/constants.dart';
import 'package:trackcarvcc/constants/style/style.dart';

// import 'package:trackcarvcc/constants/constants.dart';
// import 'package:trackcarvcc/models/models.dart';
// import 'package:trackcarvcc/models/response/google_address_dto.dart';
import 'package:trackcarvcc/repository/api/api.dart';
import 'package:trackcarvcc/helpers/extensions.dart';
import 'package:geocoding/geocoding.dart' as geocoding;
import 'package:trackcarvcc/widget/confirm_dialog.dart';
import 'package:vtmap_gl/vtmap_gl.dart';

class PlannedOutedController extends GetxController {
  Api api;

  PlannedOutedController(this.api);

  // var originLatitude = 0.0;
  // var originLongitude = 0.0;
  // var destLatitude = 0.0;
  // var destLongitude = 0.0;

  // final polylinePoints = Rx<PolylinePoints>(PolylinePoints());
  // final polyLines = RxMap<PolylineId, Polyline>({});
  List<SymbolOptions> markers = [];
  List<WayPoint> wayPoints = [];
  // List<LatLng> polylineCoordinates = [];

  // final kGooglePlex = Rx<CameraPosition>();

  // final googleMapController = Rx<Completer<GoogleMapController>>(Completer());
  MapboxMapController mapboxMapController;
  Location location = Location();
  LocationData _locationData;

  // init({
  //   List<String> points,
  // }) async {
  //   googleMapController = GoogleMapsController(
  //     mapType: MapType.normal,
  //     onMapCreated: (_) async {
  //       await getLocation();
  //     },
  //     myLocationEnabled: true,
  //     compassEnabled: true,
  //     myLocationButtonEnabled: false,
  //     zoomControlsEnabled: true,
  //   );
  //
  //   await setUpMarkers(points);
  //   //
  // }

  Future<void> onMapCreated(
    MapboxMapController controller,
    List<String> points,
  ) async {
    mapboxMapController = controller;
    await setUpMarkers(points);
  }

  setUpMarkers(List<String> points) async {
    // for (var item in points) {
    //   await getMarkers(item);
    // }

    for (int i = 0; i < points.length; i++) {
      await getMarkers(
        points[i],
        i == 0
            ? ic_location_maker
            : i == points.length - 1
                ? ic_location_maker
                : ic_location_maker,
        i + 1,
      );
    }

    // getWayPoints(points);

    mapboxMapController.addSymbols(markers);
    wayPoints.addAll(wayPoints.toList());
    mapboxMapController.buildRoute(wayPoints: wayPoints, options: VTMapOptions(
        access_token: Constants.ACCESS_TOKEN,
        mode: VTMapNavigationMode.driving,
        simulateRoute: true,
      language: "vi",
    ));
    // mapboxMapController.startNavigation(
    //     wayPoints: wayPoints,
    //     options: VTMapOptions(
    //         access_token: Constants.ACCESS_TOKEN,
    //         mode:
    //         VTMapNavigationMode.driving,
    //         simulateRoute: true,
    //         language: "vi"));

    await moveCamera(wayPoints.last.latitude, wayPoints.last.longitude);
    // _addPolyLine(polylineCoordinates);
  }

  getMarkers(String point, String icon, int index) async {
    try {
      List<geocoding.Location> locations = await geocoding.locationFromAddress(point);
      markers.add(_getSymbolOptions(locations.first.latitude, locations.first.longitude)
          // Marker(
          //   markerId: MarkerId('$point'),
          //   icon: icon,
          //   // icon: BitmapDescriptor.defaultMarkerWithHue(90),
          //   position: LatLng(
          //     locations.first.latitude,
          //     locations.first.longitude,
          //   ),
          //   onTap: () {
          //       showDialog(
          //         context: Get.context,
          //         builder: (BuildContext context) {
          //           return ConfirmDialog(
          //             type: TypeConfirmDialog.TYPE_NORMAL,
          //             // title: 'Điểm ${index}',
          //             titleChild: Text(
          //               'Điểm ${index}: $point',
          //               style: TextStyle(
          //                 fontSize: 16,
          //                 fontWeight: FontWeight.w500,
          //               ),
          //             ),
          //             onButtonClick: () {
          //               // Get.back();
          //             },
          //           );
          //         },
          //       );
          //   },
          // ),
          );

      wayPoints.add(
        WayPoint(
          name: 'Điểm $index: $point',
          latitude: locations.first.latitude,
          longitude: locations.first.longitude,
        ),
      );
    } catch (e) {
      showErrorToast(error: 'Xảy ra lỗi khi lấy thông tin vị trí!');
      // dismissLoadingDialog();
    }
  }

  SymbolOptions _getSymbolOptions(double lat, double lng, {String iconImage = car_connection}) {
    LatLng center = LatLng(lat, lng);
    LatLng geometry = LatLng(center.latitude, center.longitude);
    return SymbolOptions(
      geometry: geometry,
      iconImage: iconImage,
    );
  }

  Future moveCamera(double lat, double lng) async {
    await mapboxMapController.moveCamera(
      CameraUpdate.newCameraPosition(
        CameraPosition(
          target: LatLng(lat, lng),
          zoom: 17,
        ),
      ),
    );
    // addMarker(lat, lng);
  }

  /// location
  Future getLocation() async {
    try {
      _locationData = await location.getLocation();
      await mapboxMapController.moveCamera(
        CameraUpdate.newCameraPosition(
          CameraPosition(
            target: LatLng(
              _locationData.latitude,
              _locationData.longitude,
            ),
            zoom: 17,
          ),
        ),
      );
    } on PlatformException catch (err) {
      print('$err');
    }
  }

  Future checkPermissions() async {
    final PermissionStatus permissionGrantedResult = await location.hasPermission();
    if (permissionGrantedResult == PermissionStatus.granted || permissionGrantedResult == PermissionStatus.grantedLimited) {
      await getLocation();
    } else {
      await _requestPermission();
    }
  }

  Future _requestPermission() async {
    await location.requestPermission();
  }

  // void _getPolyline(PointLatLng origin, PointLatLng dest) async {
  //   List<LatLng> polyline = [];
  //
  //   PolylineResult result = await PolylinePoints().getRouteBetweenCoordinates(
  //     "AIzaSyArTdhzQmN1HTzNumYvNaKd_foe0MMRgqQ",
  //     origin,
  //     dest,
  //     // travelMode: TravelMode.driving,
  //     // optimizeWaypoints: true,
  //   );
  //
  //   if (result.points.isNotEmpty) {
  //     result.points.forEach((PointLatLng point) {
  //       polyline.add(LatLng(point.latitude, point.longitude));
  //     });
  //   } else {
  //     showErrorToast(
  //       error: 'Đã vướt quá giới hạn tính toán quãng đường',
  //     );
  //     // print(result.errorMessage);
  //   }
  //   _addPolyLine(polyline);
  // }

  // _addPolyLine(List<LatLng> polylineCoordinates) {
  //   PolylineId id = PolylineId("${polylineCoordinates.last}");
  //   Polyline _polyline = Polyline(polylineId: id, points: polylineCoordinates, width: 4, color: AppThemes.colorViettelRed);
  //   mapboxMapController.addPolyline(_polyline);
  //   // polyLines[id] = _polyline;
  // }
}
