import 'dart:convert';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_absolute_path/flutter_absolute_path.dart';
import 'package:get/get.dart';
import 'package:multi_image_picker2/multi_image_picker2.dart';
import 'package:trackcarvcc/constants/images.dart';
import 'package:trackcarvcc/repository/api/urls.dart';

import 'image_info.dart';

// ignore: must_be_immutable
class SelectImages extends StatefulWidget {
  List<Asset> images = List<Asset>();
  final RxList<ImageInformation> listImage;
  final List<int> listImageDelete;
  final String title;
  final int maxImage;
  final bool isDeleteImage;

  SelectImages({Key key, this.listImage, this.listImageDelete, @required this.title, this.maxImage = 10, this.isDeleteImage = true})
      : super(key: key);

  @override
  _SelectImagesState createState() => _SelectImagesState();
}

class _SelectImagesState extends State<SelectImages> {
  @override
  void initState() {
    // requestPermissions();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      alignment: Alignment.centerLeft,
      child: Obx(() => buildGridView()),
    );
  }

  Row titleRow(String title) {
    return Row(
      children: <Widget>[Text(title, style: TextStyle(color: Colors.black)), Icon(Icons.camera_alt)],
    );
  }

  Widget buildGridView() {
    double getAspectRatio(int originalSize, double desiredSize) => desiredSize / originalSize;
    return SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Container(
            padding: const EdgeInsets.only(right: 10.0, bottom: 10),
            child: new Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                GestureDetector(
                  onTap: loadAssets,
                  child: Row(
                    children: <Widget>[
                      Text(widget.title, style: TextStyle(color: Colors.black)),
                      SizedBox(width: 10),
                      Icon(Icons.camera_alt)
                    ],
                  ),
                ),
                widget.listImage == null || widget.listImage.length == 0
                    ? Container()
                    : Container(
                    height: 100,
                    child: ListView.builder(
                      scrollDirection: Axis.horizontal,
                      shrinkWrap: true,
                      itemBuilder: (BuildContext context, int index) => new Stack(
                        children: <Widget>[
                          Container(
                              margin: const EdgeInsets.only(right: 10.0),
                              padding: EdgeInsets.only(right: 5, top: 5),
                              child: new ClipRRect(
                                  borderRadius: BorderRadius.circular(8.0),
                                  child:
                                  widget.listImage[index].imagePath != null && widget.listImage[index].imagePath.length > 0
                                      ? Image.network(BASE_URL_IMAGE + widget.listImage[index].imagePath.replaceAll("\\", "/"),
                                      errorBuilder: (BuildContext context, Object exception, StackTrace stackTrace) {
                                        return Image.asset(mImageNo);
                                      })
                                      : Image.memory(Base64Decoder().convert(widget.listImage[index].base64String)))),
                          Positioned(
                            right: 10,
                            child: ClipOval(
                              child: Material(
                                color: Color(0xFF9B9B9B), // Button color
                                child: InkWell(
                                  splashColor: Colors.red, // Splash color
                                  onTap: () {
                                    widget.listImage.removeAt(index);
                                    widget.images.removeAt(index);
                                    // if (widget.listImage[index].utilAttachDocumentId != 0) {
                                    //   widget.listImageDelete
                                    //       .add(widget.listImage[index].utilAttachDocumentId);
                                    // }
                                  },
                                  child: widget.isDeleteImage?SizedBox(
                                      width: 20,
                                      height: 20,
                                      child: Icon(
                                        Icons.clear,
                                        size: 15,
                                      )):SizedBox(),
                                ),
                              ),
                            ),
                          )
                        ],
                      ),
                      itemCount: widget.listImage.length,
                    )),
              ],
            )));
  }

  Future<void> loadAssets() async {
    List<Asset> resultList = List<Asset>();
    try {
      resultList = await MultiImagePicker.pickImages(
        maxImages: widget.maxImage - widget.listImage.length,
        enableCamera: true,
        selectedAssets: widget.images,
        materialOptions: MaterialOptions(
          actionBarColor: "#abcdef",
          actionBarTitle: "Select image",
          allViewTitle: "All Photos",
          useDetailsView: false,
          selectCircleStrokeColor: "#2E9AFE",
          selectionLimitReachedText: "Bạn đã chon đủ số lượng ảnh tối đa",
        ),
      );
    } on Exception catch (e) {
      print('LOG_AIO' + e.toString());
    }

    for (var asset in resultList) {
      int maxWidth = 140;
      int height = ((maxWidth * asset.originalHeight) / asset.originalWidth).round();

      ByteData byteData = await asset.getThumbByteData(maxWidth, height, quality: 80);
    }

    // If the widget was removed from the tree while the asynchronous platform
    // message was in flight, we want to discard the reply rather than calling
    // setState to update our non-existent appearance.
    if (!mounted) return;

    setState(() {
      widget.images = resultList;
      // widget.images.addAll(resultList);
      convertImagesContractBase64(resultList);
    });
  }

  getImageFileFromAsset(String path) async {
    final file = File(path);
    return file;
  }

  convertImagesContractBase64(List<Asset> resultList) async {
    // widget.listImage.removeWhere((element) => !element.isDataServer);
    for (int i = 0; i < resultList.length; i++) {
      var path2 = await FlutterAbsolutePath.getAbsolutePath(resultList[i].identifier);
      var file = await getImageFileFromAsset(path2);
      var base64Image = base64Encode(file.readAsBytesSync());
      widget.listImage.add(ImageInformation(imageName: resultList[i].name, base64String: base64Image, imagePath: path2));
    }

    return widget.listImage;
  }
}
