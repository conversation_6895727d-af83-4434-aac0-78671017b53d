class ImageInformation {
  int utilAttachDocumentId;
  String imageName;
  String base64String;
  String imagePath;
  int objectId;
  String createdDate;
  String createdUserName;

  ImageInformation({this.utilAttachDocumentId,
    this.imageName,
    this.base64String,
    this.imagePath,
    this.objectId,
    this.createdDate,
    this.createdUserName,
  });

  ImageInformation.fromJson(Map<String, dynamic> json) {
    utilAttachDocumentId = json['utilAttachDocumentId'];
    imageName = json['imageName'];
    base64String = json['base64String'];
    imagePath = json['imagePath'];
    objectId = json['objectId'];
    createdDate = json['createdDate'];
    createdUserName = json['createdUserName'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['utilAttachDocumentId'] = this.utilAttachDocumentId;
    data['imageName'] = this.imageName;
    data['base64String'] = this.base64String;
    data['imagePath'] = this.imagePath;
    data['objectId'] = this.objectId;
    data['createdDate'] = this.createdDate;
    data['createdUserName'] = this.createdUserName;
    return data;
  }
}