import 'dart:io';
import 'dart:typed_data' show Uint8List;

// import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:trackcarvcc/utils/download_util.dart';

class ImportFileBottomSheet extends StatefulWidget {
  final double maxHeight;
  final double maxWidth;
  final int imageQuality;
  final CameraDevice preferredCameraDevice;
  final void Function(Uint8List) onImage;
  final Function(File file) onSelectImage;
  final void Function(File) onFileSelected;
  final EdgeInsets bottomSheetPadding;
  final Function onChooseImageSource;
  final bool canChooseImage;
  final bool isLocation;
  final List<String> filesType;

  ImportFileBottomSheet({
    Key key,
    this.maxHeight,
    this.maxWidth,
    this.imageQuality,
    this.preferredCameraDevice = CameraDevice.rear,
    this.onImage,
    this.onFileSelected,
    this.bottomSheetPadding,
    this.onChooseImageSource,
    this.isLocation = false,
    this.canChooseImage = false,
    this.onSelectImage,
    this.filesType,
  })  : assert(null != onImage || null != onFileSelected),
        super(key: key);

  @override
  _ImportFileBottomSheetState createState() => _ImportFileBottomSheetState();
}

class _ImportFileBottomSheetState extends State<ImportFileBottomSheet> {
  bool _isPickingImage = false;
  String _currentAddress;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
  }

  Future<void> _pickFile() async {
    // var isValidate = await DownloadUtil.permissionServicesImage();
    //
    // if (isValidate[Permission.storage].isPermanentlyDenied || isValidate[Permission.camera].isPermanentlyDenied) {
    //   return;
    // }
    // final result = await FilePicker.platform.pickFiles(
    //   type: FileType.custom,
    //   allowedExtensions: widget.filesType ??
    //       ['pdf', 'PDF'],
    // );
    // final imageFile = result?.files?.single?.path != null
    //     ? File(result.files.single.path)
    //     : null;
    // assert(null != imageFile);
    // widget.onFileSelected(imageFile);
  }

  Future<void> _onPickImage(ImageSource source) async {
    if (_isPickingImage) return;
    _isPickingImage = true;

    if (widget.onChooseImageSource != null) {
      widget.onChooseImageSource();
    }

    final imagePicker = ImagePicker();
    var isValidate = await DownloadUtil.permissionServicesImage();

    if (isValidate[Permission.storage].isPermanentlyDenied || isValidate[Permission.camera].isPermanentlyDenied) {
      return;
    }

    final pickedFile = await imagePicker.getImage(
      source: source,
      maxHeight: widget.maxHeight,
      maxWidth: widget.maxWidth,
      imageQuality: widget.imageQuality,
      preferredCameraDevice: widget.preferredCameraDevice,
    );

    _isPickingImage = false;
    if (null != pickedFile) {
      final fileSelected = File(pickedFile.path ?? '');
      widget.onSelectImage?.call(fileSelected);
    }
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async => !_isPickingImage,
      child: Container(
        padding: widget.bottomSheetPadding,
        child: Wrap(
          children: <Widget>[
            ListTile(
              leading: Icon(Icons.add_photo_alternate),
              title: Text('Chọn ảnh'),
              onTap: () => _onPickImage(ImageSource.gallery),
            ),
            ListTile(
              leading: Icon(Icons.post_add_outlined),
              title: Text('Chọn tệp'),
              onTap: () async {
                Navigator.pop(context);
                await _pickFile();
              },
            ),
          ],
        ),
      ),
    );
  }
}
