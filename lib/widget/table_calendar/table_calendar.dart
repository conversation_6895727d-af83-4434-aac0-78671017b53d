//  Copyright (c) 2019 Aleksander Woźniak
//  Licensed under Apache License v2.0

library table_calendar;

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'src/simple_gesture_detector/simple_gesture_detector.dart';

part 'src/calendar.dart';
part 'src/calendar_controller.dart';
part 'src/customization/calendar_builders.dart';
part 'src/customization/calendar_style.dart';
part 'src/customization/days_of_week_style.dart';
part 'src/customization/header_style.dart';
part 'src/widgets/cell_widget.dart';
part 'src/widgets/custom_icon_button.dart';
