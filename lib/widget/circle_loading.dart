import 'package:flutter/cupertino.dart';

class LoadingCircle extends StatefulWidget {
  const LoadingCircle({
    Key key,
    this.color = const Color(0xFF2DC09C),
    this.size = 50.0,
    this.itemBuilder,
    this.duration = const Duration(milliseconds: 1200),
    this.controller,
  })  : assert(
            !(itemBuilder is IndexedWidgetBuilder && color is Color) &&
                !(itemBuilder == null && color == null),
            'You should specify either a itemBuilder or a color'),
        assert(size != null),
        super(key: key);

  final Color color;
  final double size;
  final IndexedWidgetBuilder itemBuilder;
  final Duration duration;
  final AnimationController controller;

  @override
  _LoadingCircleState createState() => _LoadingCircleState();
}

class _LoadingCircleState extends State<LoadingCircle>
    with SingleTickerProviderStateMixin {
  @override
  Widget build(BuildContext context) {
    return Center(
      child: Container(
        height: widget.size,
        width: widget.size,
        child: const Center(
          child: CupertinoActivityIndicator(
            radius: 14.0,
          ),
        ),
      ),
    );
  }
}
