import 'package:flutter/material.dart';
import 'package:trackcarvcc/constants/style/style.dart';

class FormFieldWidget extends StatelessWidget {
  final String labelText;

  final double labelSize;

  final Widget child;

  final bool isRequired;

  final Widget mobileCall;

  final TextStyle textStyle;

  const FormFieldWidget({
    Key key,
    this.labelText,
    this.child,
    this.isRequired = false,
    this.labelSize = 12,
    this.mobileCall,
    this.textStyle,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Text(
                  labelText,
                  style: textStyle == null ? CommonTextStyle.textStyleFontLatoNormalBold.copyWith(fontSize: labelSize) : textStyle,
                ),
                SizedBox(width: 5),
                isRequired
                    ? Text(
                        '*',
                        style: TextStyle(color: Colors.red),
                      )
                    : SizedBox(),
              ],
            ),
          ],
        ),
        <PERSON><PERSON><PERSON><PERSON>(height: 5),
        child,
        <PERSON>zedBox(height: 10),
      ],
    );
  }
}

class CustomFormField extends StatelessWidget {
  final Widget child;

  final FormFieldState<dynamic> field;

  const CustomFormField({
    Key key,
    @required this.field,
    @required this.child,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: _buildBody(),
    );
  }

  _buildBody() {
    List<Widget> widgets = [
      child,
    ];
    if (field.hasError) {
      widgets.addAll([
        SizedBox(width: 8),
        Padding(
          padding: EdgeInsets.only(left: 12),
          child: Text(
            field.errorText,
            style: CommonTextStyle.textStyleFontLatoNormal.copyWith(
              fontSize: 13,
              color: Colors.red,
              fontStyle: FontStyle.italic,
            ),
          ),
        ),
      ]);
    }

    return widgets;
  }
}
