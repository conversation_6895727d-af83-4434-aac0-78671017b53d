import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import 'form_field.dart';

class FormSelect extends StatelessWidget {
  final String titleForm;

  final String valueSelect;

  final IconData iconData;

  final MaterialColor color;

  final GestureTapCallback onTap;

  final bool isRequired;

  final TextStyle textStyle;

  const FormSelect({
    Key key,
    this.titleForm,
    this.valueSelect,
    this.onTap,
    this.iconData,
    this.color,
    this.isRequired = false,
    this.textStyle,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return FormFieldWidget(
      labelText: titleForm ?? '',
      isRequired: isRequired,
      textStyle: textStyle ?? null,
      child: ConstrainedBox(
        constraints: BoxConstraints(
          minWidth: double.infinity,
        ),
        child: InkWell(
          onTap: onTap,
          child: Container(
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(5),
                border: Border.all(
                  color: Color(0XFFececec),
                  width: 2,
                ),
                color: Colors.white),
            padding: EdgeInsets.symmetric(
              horizontal: 5,
              vertical: 12,
            ),
            child: Row(
              children: <Widget>[
                Expanded(
                  flex: 1,
                  child: Center(
                    child: Text(
                      valueSelect ?? 'Chọn',
                      softWrap: false,
                      style: TextStyle(
                        fontSize: 12,
                        color: valueSelect != null ? Colors.black : Color(0xFFC2C2C2),
                      ),
                    ),
                  ),
                ),
                iconData != null
                    ? Icon(
                        iconData,
                        color: color ?? Colors.grey,
                        size: 20,
                      )
                    : SizedBox()
              ],
            ),
          ),
        ),
      ),
    );
  }
}
