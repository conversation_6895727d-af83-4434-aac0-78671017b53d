import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:trackcarvcc/constants/style/style.dart';
import 'package:trackcarvcc/helpers/extensions.dart';

import 'common_button.dart';

typedef OnOKClick(String time);

// ignore: must_be_immutable
class InputWorkingHereDialog extends StatelessWidget {
  InputWorkingHereDialog({Key key, this.onOKClick, this.timeDiffInDays})
      : super(key: key);

  final OnOKClick onOKClick;
  final int timeDiffInDays;

  TextEditingController controller = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Dialog(
        backgroundColor: Colors.white,
        elevation: mRadiusSmall,
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: _content(),
        ));
  }

  _content() => Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            'Thời gian nâng cẩ<PERSON> (Đơn vị: giờ)',
            style: CommonTextStyle.textStyleFontLatoBigBold,
          ),
          const SizedBox(
            height: mPadding,
          ),
          Text(
            'Thời gian cho phép: ${timeDiffInDays * 8} giờ',
            style: CommonTextStyle.textStyleFontLatoNormal,
          ),
          const SizedBox(
            height: mPaddingLarge,
          ),
          TextField(
            controller: controller,
            enabled: true,
            readOnly: false,
            maxLines: 1,
            keyboardType: TextInputType.number,
            decoration: InputDecoration(
              hintText: 'Nhập thời gian nâng cẩu',
              border: OutlineInputBorder(
                  borderSide: BorderSide(color: AppThemes.colorViettelGray2)),
              contentPadding: const EdgeInsets.all(mPadding),
              hintStyle: CommonTextStyle.textStyleFontLatoNormalHint,
              isDense: true,
            ),
            autofocus: false,
            style: CommonTextStyle.textStyleFontLatoNormal,
            onChanged: (value) {},
          ),
          const SizedBox(
            height: mPaddingLarge,
          ),
          Row(
            children: [
              Expanded(
                flex: 1,
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 4.0),
                  child: CommonButton(
                    title: 'OK',
                    textColor: Colors.white,
                    bgColor: AppThemes.colorViettelRed,
                    onButtonClick: () {
                      _onOK();
                    },
                  ),
                ),
              ),
              Expanded(
                flex: 1,
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 4.0),
                  child: CommonButton(
                    title: 'Hủy',
                    textColor: Colors.white,
                    bgColor: AppThemes.colorViettelGray2,
                    onButtonClick: () {
                      Get.back();
                    },
                  ),
                ),
              )
            ],
          ),
        ],
      );

  _onOK() {
    if (controller.text.isEmpty) {
      showErrorToast(error: 'Bạn chưa nhập thời gian nâng');
    } else {
      var timeLimited = int.parse(controller.text);
      if (timeLimited > timeDiffInDays * 8) {
        showErrorToast(
            error:
                'Thời gian bạn nhập đã quá thời gian cho phép (${timeLimited - timeDiffInDays * 8}h)');
      } else {
        onOKClick(controller.text);
      }
    }
  }
}
