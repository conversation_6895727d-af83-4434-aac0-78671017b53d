import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart';

enum FileType { image, pdf, video, audio }

class ViewFileAttachment extends StatefulWidget {
  const ViewFileAttachment({
    Key key,
    this.downloadFile,
    this.fileType,
    this.fileUrl,
  }) : super(key: key);

  final FileType fileType;
  final String fileUrl;
  final Function downloadFile;

  @override
  State<ViewFileAttachment> createState() => _PolicySmartOtpState();
}

class _PolicySmartOtpState extends State<ViewFileAttachment> {
  final GlobalKey<SfPdfViewerState> _pdfViewerKey = GlobalKey();

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text("Xem file đính kèm"),
        backgroundColor: Colors.red,
        leading: IconButton(
          icon: Icon(Icons.arrow_back),
          onPressed: () {
            Navigator.pop(context);
          },
        ),
        actions: [
          IconButton(
            icon: Icon(Icons.download_sharp),
            onPressed: () {
              widget.downloadFile();
            },
          ),
        ],
      ),
      body: SafeArea(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            if(widget.fileType == FileType.image)
            Image.network(
              widget.fileUrl,
              fit: BoxFit.cover,
              width: double.infinity,
            ),
            if(widget.fileType == FileType.pdf)
            SfPdfViewer.network(
              widget.fileUrl,
              key: _pdfViewerKey,
              canShowScrollStatus: true,
              pageSpacing: 2.0,
            ),
          ],
        ),
      ),
    );
  }
}
