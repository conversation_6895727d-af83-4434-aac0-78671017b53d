import 'package:flutter/material.dart';
import 'package:trackcarvcc/constants/style/style.dart';

final _textLoginButtonTextStyle =
    CommonTextStyle.textStyleFontLatoNormal.copyWith(
  color: Colors.white,
);

const _heightButton = 56.0;

typedef ButtonCallback = void Function();

class CommonButton extends StatefulWidget {
  const CommonButton({
    this.title,
    this.heightButton,
    this.onButtonClick,
    this.widthButton,
    this.bgColor,
    this.textColor,
    this.borderColor,
  });

  final String title;
  final double widthButton;
  final double heightButton;
  final Color bgColor;
  final Color textColor;
  final Color borderColor;
  final ButtonCallback onButtonClick;

  @override
  _CommonButtonState createState() => _CommonButtonState();
}

class _CommonButtonState extends State<CommonButton> {
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: widget.widthButton ?? double.infinity,
      height: widget.heightButton ?? _heightButton,
      child: FlatButton(
        color: widget.bgColor ?? AppThemes.colorViettelRed,
        shape: RoundedRectangleBorder(
                side: widget.borderColor != null
                    ? BorderSide(
                        color: widget.borderColor,
                        width: 1,
                      )
                    : BorderSide.none,
                borderRadius:
                    const BorderRadius.all(Radius.circular(mRadiusSmall)),
              ),
        onPressed: () {
          widget.onButtonClick();
        },
        child: Center(
          child: Text(
            widget.title ?? '',
            style: widget.textColor == null
                ? _textLoginButtonTextStyle
                : _textLoginButtonTextStyle.copyWith(color: widget.textColor),
          ),
        ),
      ),
    );
  }
}
