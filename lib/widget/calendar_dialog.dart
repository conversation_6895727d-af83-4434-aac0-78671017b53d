
import 'package:flutter/material.dart';
import 'package:mask_text_input_formatter/mask_text_input_formatter.dart';

import 'common_button.dart';
import 'table_calendar/table_calendar.dart';
import 'package:trackcarvcc/constants/style/style.dart';

const _marginHorizontal = 16.0;
const _marginVertical = 17.0;
const _sizeIconChervon = 17.0;

class CalendarDialog extends StatefulWidget {
  const CalendarDialog(
      {Key key,
      this.textTimerController,
      this.isEditTimer = false,
      this.initialSelectedDay,
      this.startDate,
      this.endDate})
      : super(key: key);

//  final VoidCallback onButtonClick;

  final TextEditingController textTimerController;
  final bool isEditTimer;
  final DateTime initialSelectedDay;
  final DateTime startDate;
  final DateTime endDate;

  @override
  _CalendarDialogState createState() => _CalendarDialogState();
}

class _CalendarDialogState extends State<CalendarDialog> {
  CalendarController _calendarController;

  final FocusNode focusNode = FocusNode();

  @override
  void initState() {
    _calendarController = CalendarController();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.white,
      elevation: mRadiusSmall,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(mRadiusLarge),
      ),
      child: GestureDetector(
        onTap: () {
          FocusScope.of(context).requestFocus(FocusNode());
        },
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.fromLTRB(0, 0, 0, _marginVertical),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                TableCalendar(
                  calendarController: _calendarController,
                  locale: 'vi_VN',
                  initialSelectedDay:
                      widget.initialSelectedDay ?? DateTime.now(),
                  startingDayOfWeek: StartingDayOfWeek.sunday,
                  startDay: widget.startDate,
                  endDay: widget.endDate,
                  calendarStyle: CalendarStyle(
                    selectedColor: AppThemes.colorViettelRed,
                    todayColor: AppThemes.colorViettelRed.withOpacity(0.7),
                    markersColor: Colors.brown[700],
                    outsideDaysVisible: false,
                    weekdayStyle:
                        CommonTextStyle.textStyleFontLatoNormalGray1,
                    weekendStyle:
                        CommonTextStyle.textStyleFontLatoRedNormal,
                  ),
                  availableGestures: AvailableGestures.horizontalSwipe,
                  daysOfWeekStyle: DaysOfWeekStyle(
                    weekdayStyle:
                        CommonTextStyle.textStyleFontLatoNormalGray1,
                    weekendStyle:
                        CommonTextStyle.textStyleFontLatoRedNormal,
                  ),
                  headerStyle: HeaderStyle(
                    formatButtonVisible: false,
                    centerHeaderTitle: true,
                    titleTextStyle:
                        CommonTextStyle.textStyleFontLatoNormalBold,
                    formatButtonTextStyle:
                        CommonTextStyle.textStyleFontLatoNormal,
                    rightChevronIcon: SizedBox(
                      width: _sizeIconChervon,
                      height: _sizeIconChervon,
                      child: Icon(Icons.chevron_right),
                    ),
                    leftChevronIcon: SizedBox(
                      width: _sizeIconChervon,
                      height: _sizeIconChervon,
                      child: Icon(Icons.chevron_left),
                    ),
                    formatButtonDecoration: BoxDecoration(
                      color: Colors.deepOrange[400],
                      borderRadius: BorderRadius.circular(16.0),
                    ),
                  ),
                ),
                _buildTimeForm(),
                const SizedBox(height: mPaddingLarge),
                Padding(
                  padding: const EdgeInsets.only(
                      left: _marginHorizontal, right: _marginHorizontal),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: CommonButton(
                          title: 'OK',
                          onButtonClick: () {
                            // pop selected day
                            Navigator.of(context)
                                .pop(_calendarController.selectedDay);
                          },
                        ),
                      ),
                      const SizedBox(width: mPadding),
                      Expanded(
                        child: CommonButton(
                          title: 'Hủy',
                          bgColor: AppThemes.colorViettelGray3,
                          textColor: Colors.black,
                          onButtonClick: () {
                            Navigator.pop(context);
                          },
                        ),
                      ),
                    ],
                  ),
                )
              ],
            ),
          ),
        ),
      ),
    );
  }

  _buildTimeForm() {
    if (!widget.isEditTimer) {
      return Container();
    }
    return Center(
      child: Padding(
        padding: const EdgeInsets.only(
          left: _marginHorizontal,
          right: _marginHorizontal,
          bottom: mPaddingXMedium,
        ),
        child: Container(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Text(
                'Thời gian',
                style: CommonTextStyle.textStyleFontLatoNormal,
              ),
              Container(
                width: 86,
                child: TextFormField(
                  scrollPadding: const EdgeInsets.all(0),
                  cursorColor: Colors.grey,
                  focusNode: focusNode,
                  controller: widget.textTimerController,
                  style: CommonTextStyle.textStyleFontLatoNormal,
                  keyboardType: TextInputType.number,
                  textAlign: TextAlign.center,
                  enableInteractiveSelection: false,
                  inputFormatters: [
                    SpecialMaskTextInputFormatter(
                        initialText: widget.textTimerController.text)
                  ],
                  decoration: InputDecoration(
                    filled: true,
                    fillColor: Colors.black12,
                    contentPadding: const EdgeInsets.all(8.0),
                    isDense: true,
                    border: InputBorder.none,
                    enabledBorder: OutlineInputBorder(
                      borderRadius: const BorderRadius.all(
                          Radius.circular(mRadiusSmall)),
                      borderSide: BorderSide(color: AppThemes.colorViettelRed02),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: const BorderRadius.all(
                          Radius.circular(mRadiusSmall)),
                      borderSide: BorderSide(color: AppThemes.colorViettelRed02),
                    ),
                    errorStyle: CommonTextStyle.textStyleFontLatoRedNormal,
                    hintStyle: CommonTextStyle.textStyleFontLatoNormalHint,
                    errorBorder: InputBorder.none,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class SpecialMaskTextInputFormatter extends MaskTextInputFormatter {
  static String maskA = '##:@#';
  static String maskB = '#:@#';

// ignore: sort_constructors_first
  SpecialMaskTextInputFormatter({String initialText})
      : super(
            mask: maskA,
            filter: {'#': RegExp(r'[0-9]'), '@': RegExp(r'[0-5]')},
            initialText: initialText);

  @override
  TextEditingValue formatEditUpdate(
      TextEditingValue oldValue, TextEditingValue newValue) {
    final components = newValue.text.split(':');
    if (newValue.text.isNotEmpty && components.length == 1) {
      final int intNewValue = int.parse(newValue.text);
      if (newValue.text.startsWith('0')) {
        super.updateMask(
          mask: '##:@#',
          filter: {'#': RegExp(r'[0-9]'), '@': RegExp(r'[0-5]')},
        );
      } else if (intNewValue.toString().length < 2 && intNewValue > 2) {
        super.updateMask(
          mask: '#:@#',
          filter: {'#': RegExp(r'[0-9]'), '@': RegExp(r'[0-5]')},
        );
      } else if (intNewValue.toString().length < 2 && intNewValue == 2) {
        super.updateMask(
          mask: '#*:@#',
          filter: {
            '#': RegExp(r'[0-9]'),
            '@': RegExp(r'[0-5]'),
            '*': RegExp(r'[0-3]')
          },
        );
      } else {
        super.updateMask(
          mask: '##:@#',
          filter: {'#': RegExp(r'[0-9]'), '@': RegExp(r'[0-5]')},
        );
      }
    }
    return super.formatEditUpdate(oldValue, newValue);
  }
}
