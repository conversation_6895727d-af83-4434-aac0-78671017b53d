import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:trackcarvcc/constants/style/style.dart';

import 'common_button.dart';

const _marginHorizontal = 18.0;
const _marginVertical = 24.0;
const _bgColorNegativeButton = Color(0xFFE1E1E1);
const _textColorNegativeButton = Color(0xFF3C3E5A);

enum TypeConfirmDialog { TYPE_NORMAL, TYPE_CONFIRM, TYPE_INPUT_CONFIRM }

class ConfirmDialog extends StatefulWidget {
  const ConfirmDialog({
    Key key,
    @required this.type,
    this.title,
    this.titleChild,
    this.useIconCheck = false,
    @required this.onButtonClick,
  }) : super(key: key);

  final TypeConfirmDialog type; // Type of confirm dialog
  final String title; // Title dialog is normal text
  final Widget titleChild; // Title dialog is widget
  final VoidCallback onButtonClick; // Handle Ok and positive button click
  final bool useIconCheck; // use Icon Check dialog

  @override
  _ConfirmDialogState createState() => _ConfirmDialogState();
}

class _ConfirmDialogState extends State<ConfirmDialog> {
  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.white,
      elevation: mRadiusSmall,
      child: Padding(
        padding: const EdgeInsets.fromLTRB(_marginHorizontal, _marginVertical,
            _marginHorizontal, _marginVertical),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            widget.titleChild ??
                Text(
                  widget.title,
                  style: CommonTextStyle.textStyleFontLatoNormalBold,
                  textAlign: TextAlign.center,
                ),
            !widget.useIconCheck
                ? Container()
                : Column(
                    children: [
                      const SizedBox(height: mPaddingLarge),
                      Icon(Icons.verified_outlined, size: 50,)
                    ],
                  ),
            const SizedBox(height: mPaddingLarge),
            _buildActionButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButton() {
    Widget actionButton;
    switch (widget.type) {
      case TypeConfirmDialog.TYPE_NORMAL:
        actionButton = CommonButton(
          title: 'OK',
          onButtonClick: () {
            Navigator.pop(context);
            if (widget.onButtonClick != null) {
              widget.onButtonClick();
            }
          },
        );
        break;
      case TypeConfirmDialog.TYPE_CONFIRM:
        actionButton = Row(
          children: [
            Expanded(
              flex: 1,
              child: CommonButton(
                title: 'OK',
                onButtonClick: () {
                  Navigator.pop(context);
                  if (widget.onButtonClick != null) {
                    widget.onButtonClick();
                  }
                },
              ),
            ),
            const SizedBox(width: mPaddingLarge),
            Expanded(
              flex: 1,
              child: CommonButton(
                title: 'Huỷ',
                bgColor: _bgColorNegativeButton,
                textColor: _textColorNegativeButton,
                onButtonClick: () {
                  Navigator.pop(context);
                },
              ),
            ),
          ],
        );
        break;
      case TypeConfirmDialog.TYPE_INPUT_CONFIRM:
        actionButton = Container();
        break;
    }
    return actionButton;
  }
}
