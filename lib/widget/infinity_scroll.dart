import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

class InfinityScroll extends StatefulWidget {
  final Widget child;

  final RefreshController refreshController;

  final Function onLoading;

  final Function onRefresh;

  final EdgeInsetsGeometry padding;

  const InfinityScroll({
    Key key,
    @required this.refreshController,
    @required this.onLoading,
    @required this.onRefresh,
    @required this.child,
    this.padding,
  }) : super(key: key);

  @override
  _InfinityScrollState createState() => _InfinityScrollState();
}

class _InfinityScrollState extends State<InfinityScroll> {
  GlobalKey _refresherKey = GlobalKey();

  @override
  Widget build(BuildContext context) {
    return SmartRefresher(
      controller: widget.refreshController,
      key: _refresherKey,
      enablePullDown: true,
      enablePullUp: true,
      onRefresh: widget.onRefresh,
      onLoading: widget.onLoading,
      header: WaterDropMaterialHeader(),
      footer: CustomFooter(
        height: 20,
        builder: (BuildContext context, LoadStatus mode) {
          if (mode == LoadStatus.canLoading) {
            return Container(
              child: Center(
                child: CupertinoActivityIndicator(),
              ),
            );
          }
          return SizedBox(
            width: 0,
          );
        },
      ),
      child: Padding(
        padding: widget.padding ??
            EdgeInsets.symmetric(
              horizontal: 20,
            ),
        child: SingleChildScrollView(
          child: widget.child,
        ),
      ),
    );
  }
}
