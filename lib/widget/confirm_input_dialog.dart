import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:trackcarvcc/constants/style/style.dart';
import 'package:trackcarvcc/widget/common_button.dart';

const _marginHorizontal = 18.0;
const _marginVertical = 24.0;
const _bgColorNegativeButton = Color(0xFFE1E1E1);
const _textColorNegativeButton = Color(0xFF3C3E5A);

class ConfirmInputDialog extends StatefulWidget {
  const ConfirmInputDialog({
    Key key,
    this.title,
    this.content,
    @required this.onButtonClick,
    this.requiredInput = false,
  }) : super(key: key);

  final String title;
  final String content;
  final Function onButtonClick;
  final bool requiredInput;

  @override
  _ConfirmInputDialogState createState() => _ConfirmInputDialogState();
}

class _ConfirmInputDialogState extends State<ConfirmInputDialog> {
  TextEditingController _controller = TextEditingController();
  bool _validate = false;

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.white,
      elevation: mRadiusSmall,
      child: Padding(
        padding: const EdgeInsets.fromLTRB(_marginHorizontal, _marginVertical,
            _marginHorizontal, _marginVertical),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              widget.title ?? '',
              style: CommonTextStyle.textStyleFontLatoNormalBold,
              textAlign: TextAlign.left,
            ),
            const SizedBox(height: 4),
            Text(
              widget.content ?? '',
              style: CommonTextStyle.textStyleFontLatoNormal,
              textAlign: TextAlign.left,
            ),
            const SizedBox(height: 4),
            Container(
              alignment: Alignment.centerLeft,
              decoration: BoxDecoration(
                color: AppThemes.colorViettelGray3,
                borderRadius: const BorderRadius.all(
                  Radius.circular(mRadiusSmall),
                ),
                border: Border.all(
                  color: _validate
                      ? AppThemes.colorViettelRed
                      : AppThemes.colorViettelGray2,
                ),
              ),
              child: TextField(
                controller: _controller,
                decoration: InputDecoration(
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.all(mPadding),
                  isDense: true,
                ),
                onChanged: (value) {
                  _validate = value.trim().isNotEmpty;
                  setState(() {});
                },
                autofocus: false,
                style: CommonTextStyle.textStyleFontLatoNormal,
              ),
            ),
            const SizedBox(height: mPaddingLarge),
            _buildActionButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButton() {
    return Row(
      children: [
        Expanded(
          flex: 1,
          child: CommonButton(
            title: 'Xác nhận',
            onButtonClick: () {
              if (_controller.text.trim().isEmpty && widget.requiredInput) {
                _validate = false;
                setState(() {});
                showErrorToast();
                return;
              }
              Navigator.pop(context);
              if (widget.onButtonClick != null) {
                widget.onButtonClick(_controller.text.trim());
              }
            },
          ),
        ),
        const SizedBox(width: mPaddingLarge),
        Expanded(
          flex: 1,
          child: CommonButton(
            title: 'Huỷ',
            bgColor: _bgColorNegativeButton,
            textColor: _textColorNegativeButton,
            onButtonClick: () {
              Navigator.pop(context);
            },
          ),
        ),
      ],
    );
  }

  showErrorToast() {
    Fluttertoast.showToast(
        msg: 'Chưa nhập lý do',
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.BOTTOM,
        timeInSecForIosWeb: 1,
        // textColor: Colors.white,
        fontSize: 16.0);
  }
}
