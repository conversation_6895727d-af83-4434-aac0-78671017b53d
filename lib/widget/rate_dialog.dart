import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:get/get.dart';
import 'package:trackcarvcc/constants/style/style.dart';
import 'package:trackcarvcc/helpers/extensions.dart';
import 'package:trackcarvcc/widget/common_button.dart';

typedef OnRating(int rating, String value);

// ignore: must_be_immutable
class RateDialog extends StatelessWidget {
  RateDialog({Key key, this.onRating}) : super(key: key);

  final TextEditingController _controller = TextEditingController();
  int rating;
  final OnRating onRating;

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.white,
      elevation: mRadiusSmall,
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(10.0),
              color: Color(0xffff9100),
              alignment: Alignment.center,
              child: Text(
                '<PERSON><PERSON><PERSON> giá chuyến đi',
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 18.0,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            const SizedBox(height: 8.0),
            RatingBar.builder(
              initialRating: 0,
              minRating: 1,
              direction: Axis.horizontal,
              allowHalfRating: false,
              itemCount: 5,
              itemPadding: EdgeInsets.symmetric(horizontal: 4.0),
              itemBuilder: (context, _) => Icon(
                Icons.star,
                color: AppThemes.colorViettelRed,
              ),
              onRatingUpdate: (rating) {
                this.rating = rating.toInt();
              },
            ),
            const SizedBox(height: 8.0),
            TextField(
              controller: _controller,
              maxLines: 5,
              decoration: InputDecoration(
                hintText: 'Nhập nhận xét',
              ),
            ),
            const SizedBox(height: 8.0),
            Padding(
              padding: const EdgeInsets.only(
                top: mPaddingXLarge,
                left: mPadding,
                right: mPadding,
                bottom: mPaddingLarge,
              ),
              child: Row(
                children: [
                  Expanded(
                    flex: 1,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 4.0),
                      child: CommonButton(
                        title: 'Hủy',
                        textColor: Colors.white,
                        bgColor: Color(0xffff4444),
                        onButtonClick: _onCancel,
                      ),
                    ),
                  ),
                  Expanded(
                    flex: 1,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 4.0),
                      child: CommonButton(
                        title: 'Đồng ý',
                        textColor: Colors.white,
                        bgColor: Color(0xff2d9a59),
                        onButtonClick: () {
                          _onApprove(context);
                        },
                      ),
                    ),
                  )
                ],
              ),
            )
          ],
        ),
      ),
    );
  }

  _onCancel() {
    Get.back();
  }

  _onApprove(BuildContext context) {
    if (rating <= 0) {
      showErrorToast(error: 'Bạn vui lòng đánh giá chuyến đi!');
    } else if (rating < 4) {
      if (_controller.text.trim().isEmpty) {
        showErrorToast(error: 'Bạn phải nhập lý do!');
      } else {
        if (onRating != null) {
          onRating(rating, _controller.text.trim());
        }
        Get.back();
      }
    } else {
      if (onRating != null) {
        onRating(rating, _controller.text.trim());
      }
      Get.back();
    }
  }
}
