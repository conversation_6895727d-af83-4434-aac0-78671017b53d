import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:trackcarvcc/widget/circle_loading.dart';
import 'package:trackcarvcc/widget/confirm_dialog.dart';
import 'package:trackcarvcc/widget/confirm_input_dialog.dart';

extension LoadingState on Widget {
  showErrorToast({String error}) {
    Fluttertoast.showToast(
        msg: error ?? 'Có lỗi xảy ra. Vui lòng thử lại sau.',
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.BOTTOM,
        timeInSecForIosWeb: 1,
        // textColor: Colors.white,
        fontSize: 16.0);
  }

  showConfirmDialog(BuildContext context, String message,
      {Function yesCallBack, Widget titleChild}) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return ConfirmDialog(
          type: TypeConfirmDialog.TYPE_CONFIRM,
          onButtonClick: yesCallBack,
          title: message,
          titleChild: titleChild,
        );
      },
    );
  }

  showInputConfirmDialog(BuildContext context, String title, String content,
      {Function yesCallBack, bool requiredInput = false}) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return ConfirmInputDialog(
          onButtonClick: yesCallBack,
          title: title,
          content: content,
          requiredInput: requiredInput,
        );
      },
    );
  }

  void showLoadingDialog() {
    Get.dialog(
      LoadingCircle(),
      barrierDismissible: false,
      barrierColor: Colors.grey.withOpacity(0.3),
    );
  }

  void dismissLoadingDialog() {
    Get.back();
  }
}

extension LoadingController on GetxController {
  void showLoadingDialog() {
    Get.dialog(
      LoadingCircle(),
      barrierDismissible: false,
      barrierColor: Colors.grey.withOpacity(0.3),
    );
  }

  void dismissLoadingDialog() {
    Get.back();
  }

  showErrorToast({String error}) {
    Fluttertoast.showToast(
        msg: error ?? 'Có lỗi xảy ra. Vui lòng thử lại sau.',
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.BOTTOM,
        timeInSecForIosWeb: 1,
        // textColor: Colors.white,
        fontSize: 16.0);
  }

  showSuccessToast({String msg}) {
    Fluttertoast.showToast(
        msg: msg ?? 'Thành công!',
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.CENTER,
        timeInSecForIosWeb: 1,
        backgroundColor: Colors.green,
        textColor: Colors.white,
        fontSize: 16.0);
  }

  void showCustomDialog({Widget dialogContent}) {
    Get.dialog(
      dialogContent,
      barrierDismissible: true,
      barrierColor: Colors.grey.withOpacity(0.3),
    );
  }
}
