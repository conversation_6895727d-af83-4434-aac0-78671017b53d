import 'package:intl/intl.dart';

class StringUtils {
  static bool isEmpty(String str) {
    return str == null || str.trim().isEmpty;
  }

  static String eventDateFormat(DateTime date, String time) {
    final hourAndMin = time.split(':');
    final currentDate = DateTime(
        date?.year,
        date?.month,
        date?.day,
        int.parse(hourAndMin.first),
        (hourAndMin.length > 1) ? int.parse(hourAndMin.last) : 0);
    return parseDateTime24Hour(currentDate);
  }

  static String parseDateTime24Hour(DateTime time) {
    try{
      return DateFormat('dd/MM/yyyy HH:mm').format(time.toLocal());
    } catch(e) {
      print('exception1: ${e.toString()}');
      return '';
    }
  }

  static String parseStartDateTimeDDMMYYYY(DateTime time) {
    return DateFormat('dd/MM/yyyy').format(time.toLocal()) + ' 00:00';
  }

  static String parseFinishDateTimeDDMMYYYY(DateTime time) {
    return DateFormat('dd/MM/yyyy').format(time.toLocal()) + ' 23:59';
  }

  static String parseDateTime24Hour2(String timeString) {
    try {
      try {
        final dateTime = DateFormat('dd/MM/yyyy HH:mm').parse(timeString);
        return DateFormat('dd/MM/yyyy HH:mm').format(dateTime.toLocal());
      } catch (e) {
        final dateTime = DateFormat('dd/MM/yyyy - HH:mm').parse(timeString);
        return DateFormat('dd/MM/yyyy HH:mm').format(dateTime.toLocal());
      }
    } catch (e) {
      print('exception: ${e.toString()}');
      return '';
    }
  }

  static DateTime formatDateTime(String timeString) {
    try {
      try {
        final dateTime = DateFormat('dd/MM/yyyy HH:mm:ss').parse(timeString);
        return dateTime;
      } catch (e) {
        final dateTime = DateFormat('dd/MM/yyyy - HH:mm:ss').parse(timeString);
        return dateTime;
      }
    } catch (e) {
      print('exception: ${e.toString()}');
      return null;
    }
  }

  static String removeDiacritics(String str) {
    var withDia =
        'ÀÁÂÃÄÅàáâãäåÒÓÔÕÕÖØƠòóôõöøơÈÉÊËèéêëðÇçđĐÌÍÎÏìíîïÙÚÛÜùúûüÑñŠšŸÿýŽž';
    var withoutDia =
        'AAAAAAaaaaaaOOOOOOOOoooooooEEEEeeeeeCcdDIIIIiiiiUUUUuuuuNnSsYyyZz';

    for (int i = 0; i < withDia.length; i++) {
      str = str.replaceAll(withDia[i], withoutDia[i]);
    }

    return str;
  }
}
