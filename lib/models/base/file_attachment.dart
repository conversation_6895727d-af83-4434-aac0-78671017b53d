class FileAttachment{
  String name;
  String filePath;
  String imagePath;

  FileAttachment({
    this.name,
    this.filePath,
    this.imagePath,
  });

  FileAttachment.fromJson(Map<String, dynamic> json) {
    name = json['name'];
    filePath = json['filePath'];
    imagePath = json['imagePath'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['name'] = name;
    data['filePath'] = filePath;
    data['imagePath'] = imagePath;
    return data;
  }
}