class UserLogin {
  UserLogin({
    this.defaultSortField,
    this.messageColumn,
    this.sysUserId,
    this.loginName,
    this.fullName,
    this.employeeCode,
    this.email,
    this.phoneNumber,
    this.namePhone,
    this.sysGroupName,
    this.sysGroupId,
    this.departmentName,
    this.token,
    this.roleCode,
    this.departmentId,
    this.fwmodelId,
    this.isChosen = false,
    this.status,
    this.flag,
    this.fromDate,
    this.toDate,
  });

  String defaultSortField;
  int messageColumn;
  int sysUserId;
  String loginName;
  String fullName;
  String employeeCode;
  String email;
  String phoneNumber;
  String namePhone;
  String sysGroupName;
  int sysGroupId;
  String departmentName;
  String token;
  String roleCode;
  int departmentId;
  int fwmodelId;
  bool isChosen = false;
  String status = '';
  int flag;
  Null fromDate;
  Null toDate;

  factory UserLogin.fromJson(Map<String, dynamic> json) => UserLogin(
        defaultSortField: json["defaultSortField"],
        messageColumn: json["messageColumn"],
        sysUserId: json["sysUserId"],
        loginName: json["loginName"],
        fullName: json["fullName"],
        employeeCode: json["employeeCode"],
        email: json["email"],
        phoneNumber: json["phoneNumber"],
        namePhone: json["namePhone"],
        sysGroupName: json["sysGroupName"],
        sysGroupId: json["sysGroupId"],
        departmentName: json["departmentName"],
        token: json["token"],
        roleCode: json["roleCode"],
        departmentId: json["departmentId"],
        fwmodelId: json["fwmodelId"],
        status: json["status"],
        flag: json["flag"],
        fromDate: json["fromDate"],
        toDate: json["toDate"],
      );

  Map<String, dynamic> toJson() {
    Map<String, dynamic> data = {
    "defaultSortField": defaultSortField,
    "messageColumn": messageColumn,
    "sysUserId": sysUserId,
    "loginName": loginName,
    "fullName": fullName,
    "employeeCode": employeeCode,
    "email": email,
    "phoneNumber": phoneNumber,
    "namePhone": namePhone,
    "sysGroupName": sysGroupName,
    "sysGroupId": sysGroupId,
    "departmentName": departmentName,
    "token": token,
    "roleCode": roleCode,
    "departmentId": departmentId,
    "fwmodelId": fwmodelId,
    "status": status,
    "flag": flag,
    "fromDate": fromDate,
    "toDate": toDate,
    };
    data.removeWhere((key, value) => key == null || value == null);
    return data;
  }
}
