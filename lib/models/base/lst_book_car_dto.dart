import 'dart:convert';

import 'file_attachment.dart';

class LstBookCarDto {
  LstBookCarDto({
    this.defaultSortField,
    this.messageColumn,
    this.bookCarId,
    this.sysUserId,
    this.loginName,
    this.fullName,
    this.email,
    this.phoneNumber,
    this.sysGroupId,
    this.sysGroupName,
    this.departmentId,
    this.departmentName,
    this.carTypeId,
    this.carTypeName,
    this.code,
    this.startTime,
    this.endTime,
    this.fromAddress,
    this.fromProvinceId,
    this.toAddress,
    this.managerStaffId,
    this.managerStaffName,
    this.managerStaffEmail,
    this.numPersonTogether,
    this.status,
    this.createdDateView,
    this.typeBookCar,
    this.content,
    this.statusManage,
    this.statusDriver,
    this.internalProvince,
    this.score,
    this.goodsWeight,
    this.flag,
    this.fromDate,
    this.toDate,
    this.estimateDistance,
    this.estimateTime,
    this.fwmodelId,
    this.reasonManage,
    this.reasonCaptainCar,
    this.reasonManagerCar,
    this.reasonDriverBoard,
    this.reasonAdministrative,
    this.reasonViceManager,
    this.reasonTthtPqlts,
    this.statusCaptainCar,
    this.statusDriverBoard,
    this.statusManagerCar,
    this.statusViceManager,
    this.statusTthtPqlts,
    this.statusAdministrative,
    this.managerCarName,
    this.tthtPqltsName,
    this.captainCarName,
    this.driverBoardName,
    this.administrativeName,
    this.viceManagerName,
    this.driverName,
    this.licenseCar,
    this.carId,
    this.driverId,
    this.driverCode,
    this.tthtPqltsId,
    this.captainCarId,
    this.phoneNumberDriver,
    this.pairingCar,
    this.toAddressExtend,
    this.contentExtend,
    this.endTimeExtend,
    this.nextPersionId,
    this.truckApproveDtx,
    this.truckApproveQlx,
    this.truckApproveTtht,
    this.weight,
    this.longtitudeCar,
    this.latitudeCar,
    this.onCommandCar,
    this.onCommandDriver,
    this.longtitudeDriver,
    this.latitudeDriver,
    this.driverEmail,
    this.scoreText,
    this.managerCarEmail,
    this.managerCarId,
    this.captainCarEmail,
    this.viceManagerId,
    this.workingHere,
    this.administrativeEmail,
    this.administrativeId,
    this.driverBoardEmail,
    this.driverBoardId,
    this.viceManagerEmail,
    this.tthtPqltsEmail,
    this.serveField,
    this.serveFieldName,
    this.startPoint,
    this.endPoint,
    this.routePlans,
    this.bookCarAddress,
    this.listAttachment,
  });

  String defaultSortField;
  int messageColumn;
  int bookCarId;
  int sysUserId;
  String loginName;
  String fullName;
  String email;
  String phoneNumber;
  int sysGroupId;
  String sysGroupName;
  int departmentId;
  String departmentName;
  int carTypeId;
  String carTypeName;
  String code;
  String startTime;
  String endTime;
  String fromAddress;
  int fromProvinceId;
  String toAddress;
  List<BookCarAddress> bookCarAddress;
  int managerStaffId;
  String managerStaffName;
  String managerStaffEmail;
  int numPersonTogether;
  String status;
  String createdDateView;
  String typeBookCar;
  String content;
  String statusManage;
  String statusDriver;
  int internalProvince;
  int score;
  String scoreText;
  double goodsWeight;
  int flag;
  dynamic fromDate;
  dynamic toDate;
  double estimateDistance;
  String estimateTime;
  int fwmodelId;
  String reasonManage;
  String reasonCaptainCar;
  String reasonManagerCar;
  String reasonDriverBoard;
  String reasonAdministrative;
  String reasonViceManager;
  String reasonTthtPqlts;
  String statusCaptainCar;
  String statusDriverBoard;
  String statusManagerCar;
  String statusViceManager;
  String statusTthtPqlts;
  String statusAdministrative;
  String managerCarName;
  String tthtPqltsName;
  int tthtPqltsId;
  String captainCarName;
  String driverBoardName;
  String administrativeName;
  String viceManagerName;
  String driverName;
  int carId;
  String licenseCar;
  int driverId;
  int captainCarId;
  String driverCode;
  String phoneNumberDriver;
  int pairingCar = 0;
  String toAddressExtend;
  String contentExtend;
  String endTimeExtend;
  String nextPersionId;
  int truckApproveDtx;
  int truckApproveQlx;
  int truckApproveTtht;
  double weight;

  String longtitudeCar;
  String latitudeCar;
  String onCommandCar;
  String onCommandDriver;
  String longtitudeDriver;
  String latitudeDriver;
  String driverEmail;
  String managerCarEmail;
  int managerCarId;
  String captainCarEmail;
  int viceManagerId;
  double workingHere;

  String administrativeEmail;
  int administrativeId;
  String driverBoardEmail;
  int driverBoardId;
  String viceManagerEmail;
  String tthtPqltsEmail;
  String serveField;
  String serveFieldName;
  LatLongDto startPoint;
  LatLongDto endPoint;
  List<LatLongDto> routePlans;
  List<FileAttachment> listAttachment;

  factory LstBookCarDto.fromJson(Map<String, dynamic> json) => LstBookCarDto(
        defaultSortField: json["defaultSortField"] == null ? null : json["defaultSortField"],
        messageColumn: json["messageColumn"] == null ? null : json["messageColumn"],
        bookCarId: json["bookCarId"] == null ? null : json["bookCarId"],
        sysUserId: json["sysUserId"] == null ? null : json["sysUserId"],
        loginName: json["loginName"] == null ? null : json["loginName"],
        fullName: json["fullName"] == null ? null : json["fullName"],
        email: json["email"] == null ? null : json["email"],
        phoneNumber: json["phoneNumber"] == null ? null : json["phoneNumber"],
        sysGroupId: json["sysGroupId"] == null ? null : json["sysGroupId"],
        sysGroupName: json["sysGroupName"] == null ? null : json["sysGroupName"],
        departmentId: json["departmentId"] == null ? null : json["departmentId"],
        departmentName: json["departmentName"] == null ? null : json["departmentName"],
        carTypeId: json["carTypeId"] == null ? null : json["carTypeId"],
        carTypeName: json["carTypeName"] == null ? null : json["carTypeName"],
        code: json["code"] == null ? null : json["code"],
        startTime: json["startTime"] == null ? null : json["startTime"],
        endTime: json["endTime"] == null ? null : json["endTime"],
        fromAddress: json["fromAddress"] == null ? null : json["fromAddress"],
        fromProvinceId: json["fromProvinceId"] == null ? null : json["fromProvinceId"],
        toAddress: json["toAddress"] == null ? null : json["toAddress"],
        managerStaffId: json["managerStaffId"] == null ? null : json["managerStaffId"],
        managerStaffName: json["managerStaffName"] == null ? null : json["managerStaffName"],
        managerStaffEmail: json["managerStaffEmail"] == null ? null : json["managerStaffEmail"],
        numPersonTogether: json["numPersonTogether"] == null ? null : json["numPersonTogether"],
        status: json["status"] == null ? null : json["status"],
        createdDateView: json["createdDateView"] == null ? null : json["createdDateView"],
        typeBookCar: json["typeBookCar"] == null ? null : json["typeBookCar"],
        content: json["content"] == null ? null : json["content"],
        statusManage: json["statusManage"] == null ? null : json["statusManage"],
        statusDriver: json["statusDriver"] == null ? null : json["statusDriver"],
        internalProvince: json["internalProvince"] == null ? null : json["internalProvince"],
        score: json["score"] == null ? null : json["score"],
        goodsWeight: json["goodsWeight"] == null ? null : json["goodsWeight"],
        flag: json["flag"] == null ? null : json["flag"],
        fromDate: json["fromDate"],
        toDate: json["toDate"],
        estimateDistance: json["estimateDistance"] == null ? null : json["estimateDistance"],
        estimateTime: json["estimateTime"] == null ? null : json["estimateTime"],
        fwmodelId: json["fwmodelId"] == null ? null : json["fwmodelId"],
        reasonManage: json["reasonManage"] == null ? null : json["reasonManage"],
        reasonCaptainCar: json["reasonCaptainCar"] == null ? null : json["reasonCaptainCar"],
        reasonManagerCar: json["reasonManagerCar"] == null ? null : json["reasonManagerCar"],
        reasonDriverBoard: json["reasonDriverBoard"] == null ? null : json["reasonDriverBoard"],
        reasonAdministrative: json["reasonAdministrative"] == null ? null : json["reasonAdministrative"],
        reasonViceManager: json["reasonViceManager"] == null ? null : json["reasonViceManager"],
        reasonTthtPqlts: json["reasonTthtPqlts"] == null ? null : json["reasonTthtPqlts"],
        statusCaptainCar: json["statusCaptainCar"] == null ? null : json["statusCaptainCar"],
        statusDriverBoard: json["statusDriverBoard"] == null ? null : json["statusDriverBoard"],
        statusManagerCar: json["statusManagerCar"] == null ? null : json["statusManagerCar"],
        statusViceManager: json["statusViceManager"] == null ? null : json["statusViceManager"],
        statusTthtPqlts: json["statusTthtPqlts"] == null ? null : json["statusTthtPqlts"],
        statusAdministrative: json["statusAdministrative"] == null ? null : json["statusAdministrative"],
        managerCarName: json["managerCarName"] == null ? null : json["managerCarName"],
        tthtPqltsName: json["tthtPqltsName"] == null ? null : json["tthtPqltsName"],
        captainCarName: json["captainCarName"] == null ? null : json["captainCarName"],
        driverBoardName: json["driverBoardName"] == null ? null : json["driverBoardName"],
        administrativeName: json["administrativeName"] == null ? null : json["administrativeName"],
        viceManagerName: json["viceManagerName"] == null ? null : json["viceManagerName"],
        driverName: json["driverName"] == null ? null : json["driverName"],
        licenseCar: json["licenseCar"] == null ? null : json["licenseCar"],
        carId: json["carId"] == null ? null : json["carId"],
        driverId: json["driverId"] == null ? null : json["driverId"],
        driverCode: json["driverCode"] == null ? null : json["driverCode"],
        tthtPqltsId: json["tthtPqltsId"] == null ? null : json["tthtPqltsId"],
        captainCarId: json["captainCarId"] == null ? null : json["captainCarId"],
        phoneNumberDriver: json["phoneNumberDriver"] == null ? null : json["phoneNumberDriver"],
        pairingCar: json["pairingCar"] == null ? 0 : json["pairingCar"],
        toAddressExtend: json["toAddressExtend"] == null ? null : json["toAddressExtend"],
        contentExtend: json["contentExtend"] == null ? null : json["contentExtend"],
        endTimeExtend: json["endTimeExtend"] == null ? null : json["endTimeExtend"],
        nextPersionId: json["nextPersionId"] == null ? null : json["nextPersionId"],
        truckApproveDtx: json["truckApproveDtx"] == null ? null : json["truckApproveDtx"],
        truckApproveQlx: json["truckApproveQlx"] == null ? null : json["truckApproveQlx"],
        truckApproveTtht: json["truckApproveTtht"] == null ? null : json["truckApproveTtht"],
        weight: json["weight"] == null ? null : json["weight"],
        longtitudeCar: json['longtitudeCar'] == null ? null : json['longtitudeCar'],
        latitudeCar: json['latitudeCar'] == null ? null : json['latitudeCar'],
        onCommandCar: json['onCommandCar'] == null ? null : json['onCommandCar'],
        onCommandDriver: json['onCommandDriver'] == null ? null : json['onCommandDriver'],
        longtitudeDriver: json['longtitudeDriver'] == null ? null : json['longtitudeDriver'],
        latitudeDriver: json['latitudeDriver'] == null ? null : json['latitudeDriver'],
        driverEmail: json['driverEmail'] == null ? null : json['driverEmail'],
        scoreText: json['scoreText'] == null ? null : json['scoreText'],
        managerCarEmail: json['managerCarEmail'] == null ? null : json['managerCarEmail'],
        managerCarId: json['managerCarId'] == null ? null : json['managerCarId'],
        captainCarEmail: json['captainCarEmail'] == null ? null : json['captainCarEmail'],
        viceManagerId: json['viceManagerId'] == null ? null : json['viceManagerId'],
        workingHere: json['workingHere'] == null ? null : json['workingHere'],
        administrativeEmail: json['administrativeEmail'] == null ? null : json['administrativeEmail'],
        administrativeId: json['administrativeId'] == null ? null : json['administrativeId'],
        driverBoardEmail: json['driverBoardEmail'] == null ? null : json['driverBoardEmail'],
        driverBoardId: json['driverBoardId'] == null ? null : json['driverBoardId'],
        viceManagerEmail: json['viceManagerEmail'] == null ? null : json['viceManagerEmail'],
        tthtPqltsEmail: json['tthtPqltsEmail'] == null ? null : json['tthtPqltsEmail'],
        serveField: json['serveField'] == null ? null : json['serveField'],
        serveFieldName: json['serveFieldName'] == null ? null : json['serveFieldName'],
        startPoint: json['startPoint'] != null ? LatLongDto.fromJson(json['startPoint']) : null,
        endPoint: json['endPoint'] != null ? LatLongDto.fromJson(json['endPoint']) : null,
        routePlans: json['routePlans'] != null ? (json['routePlans'] as List).map((i) => LatLongDto.fromJson(i)).toList() : null,
        bookCarAddress: json['bookCarAddress'] != null ? (json['bookCarAddress'] as List).map((i) => BookCarAddress.fromJson(i)).toList() : null,
        listAttachment: json['listAttachment'] != null ? (json['listAttachment'] as List).map((i) => FileAttachment.fromJson(i)).toList() : null,
      );

  Map<String, dynamic> toJson() {
    Map<String, dynamic> map = {
      "defaultSortField": defaultSortField == null ? null : defaultSortField,
      "messageColumn": messageColumn == null ? null : messageColumn,
      "bookCarId": bookCarId == null ? null : bookCarId,
      "sysUserId": sysUserId == null ? null : sysUserId,
      "loginName": loginName == null ? null : loginName,
      "fullName": fullName == null ? null : fullName,
      "email": email == null ? null : email,
      "phoneNumber": phoneNumber == null ? null : phoneNumber,
      "sysGroupId": sysGroupId == null ? null : sysGroupId,
      "sysGroupName": sysGroupName == null ? null : sysGroupName,
      "departmentId": departmentId == null ? null : departmentId,
      "departmentName": departmentName == null ? null : departmentName,
      "carTypeId": carTypeId == null ? null : carTypeId,
      "carTypeName": carTypeName == null ? null : carTypeName,
      "code": code == null ? null : code,
      "startTime": startTime == null ? null : startTime,
      "endTime": endTime == null ? null : endTime,
      "fromAddress": fromAddress == null ? null : fromAddress,
      "fromProvinceId": fromProvinceId == null ? null : fromProvinceId,
      "toAddress": toAddress == null ? null : toAddress,
      "managerStaffId": managerStaffId == null ? null : managerStaffId,
      "managerStaffName": managerStaffName == null ? null : managerStaffName,
      "managerStaffEmail": managerStaffEmail == null ? null : managerStaffEmail,
      "numPersonTogether": numPersonTogether == null ? null : numPersonTogether,
      "status": status == null ? null : status,
      "createdDateView": createdDateView == null ? null : createdDateView,
      "typeBookCar": typeBookCar == null ? null : typeBookCar,
      "content": content == null ? null : content,
      "statusManage": statusManage == null ? null : statusManage,
      "statusDriver": statusDriver == null ? null : statusDriver,
      "internalProvince": internalProvince == null ? null : internalProvince,
      "score": score == null ? null : score,
      "goodsWeight": goodsWeight == null ? null : goodsWeight,
      "flag": flag == null ? null : flag,
      "fromDate": fromDate,
      "toDate": toDate,
      "estimateDistance": estimateDistance == null ? null : estimateDistance,
      "estimateTime": estimateTime == null ? null : estimateTime,
      "fwmodelId": fwmodelId == null ? null : fwmodelId,
      "reasonManage": reasonManage == null ? null : reasonManage,
      "reasonCaptainCar": reasonCaptainCar == null ? null : reasonCaptainCar,
      "reasonManagerCar": reasonManagerCar == null ? null : reasonManagerCar,
      "reasonDriverBoard": reasonDriverBoard == null ? null : reasonDriverBoard,
      "reasonAdministrative": reasonAdministrative == null ? null : reasonAdministrative,
      "reasonViceManager": reasonViceManager == null ? null : reasonViceManager,
      "reasonTthtPqlts": reasonTthtPqlts == null ? null : reasonTthtPqlts,
      "statusCaptainCar": statusCaptainCar == null ? null : statusCaptainCar,
      "statusDriverBoard": statusDriverBoard == null ? null : statusDriverBoard,
      "statusManagerCar": statusManagerCar == null ? null : statusManagerCar,
      "statusViceManager": statusViceManager == null ? null : statusViceManager,
      "statusTthtPqlts": statusTthtPqlts == null ? null : statusTthtPqlts,
      "statusAdministrative": statusAdministrative == null ? null : statusAdministrative,
      "managerCarName": managerCarName == null ? null : managerCarName,
      "tthtPqltsName": tthtPqltsName == null ? null : tthtPqltsName,
      "captainCarName": captainCarName == null ? null : captainCarName,
      "driverBoardName": driverBoardName == null ? null : driverBoardName,
      "administrativeName": administrativeName == null ? null : administrativeName,
      "viceManagerName": viceManagerName == null ? null : viceManagerName,
      "driverName": driverName == null ? null : driverName,
      "licenseCar": licenseCar == null ? null : licenseCar,
      "carId": carId == null ? null : carId,
      "driverId": driverId == null ? null : driverId,
      "driverCode": driverCode == null ? null : driverCode,
      "tthtPqltsId": tthtPqltsId == null ? null : tthtPqltsId,
      "captainCarId": captainCarId == null ? null : captainCarId,
      "phoneNumberDriver": phoneNumberDriver == null ? null : phoneNumberDriver,
      "pairingCar": pairingCar == null ? null : pairingCar,
      "toAddressExtend": toAddressExtend == null ? null : toAddressExtend,
      "contentExtend": contentExtend == null ? null : contentExtend,
      "endTimeExtend": endTimeExtend == null ? null : endTimeExtend,
      "nextPersionId": nextPersionId == null ? null : nextPersionId,
      "truckApproveDtx": truckApproveDtx == null ? null : truckApproveDtx,
      "truckApproveQlx": truckApproveQlx == null ? null : truckApproveQlx,
      "truckApproveTtht": truckApproveTtht == null ? null : truckApproveTtht,
      "weight": weight == null ? null : weight,
      "longtitudeCar": longtitudeCar == null ? null : longtitudeCar,
      "latitudeCar": latitudeCar == null ? null : latitudeCar,
      "onCommandCar": onCommandCar == null ? null : onCommandCar,
      "onCommandDriver": onCommandDriver == null ? null : onCommandDriver,
      "longtitudeDriver": longtitudeDriver == null ? null : longtitudeDriver,
      "latitudeDriver": latitudeDriver == null ? null : latitudeDriver,
      "driverEmail": driverEmail == null ? null : driverEmail,
      "scoreText": scoreText == null ? null : scoreText,
      "managerCarEmail": managerCarEmail == null ? null : managerCarEmail,
      "managerCarId": managerCarId == null ? null : managerCarId,
      "captainCarEmail": captainCarEmail == null ? null : captainCarEmail,
      "viceManagerId": viceManagerId == null ? null : viceManagerId,
      "workingHere": workingHere == null ? null : workingHere,
      "administrativeEmail": administrativeEmail == null ? null : administrativeEmail,
      "administrativeId": administrativeId == null ? null : administrativeId,
      "driverBoardEmail": driverBoardEmail == null ? null : driverBoardEmail,
      "driverBoardId": driverBoardId == null ? null : driverBoardId,
      "viceManagerEmail": viceManagerEmail == null ? null : viceManagerEmail,
      "tthtPqltsEmail": tthtPqltsEmail == null ? null : tthtPqltsEmail,
      "serveField": serveField == null ? null : serveField,
      "serveFieldName": serveFieldName == null ? null : serveFieldName,
      "startPoint": startPoint == null ? null : startPoint.toJson(),
      "endPoint": endPoint == null ? null : endPoint.toJson(),
      "routePlans": routePlans == null ? null : routePlans.map((v) => v.toJson()).toList(),
      "bookCarAddress": bookCarAddress == null ? null : bookCarAddress.map((v) => v.toJson()).toList(),
      "listAttachment": listAttachment == null ? null : listAttachment.map((v) => v.toJson()).toList(),
    };
    map.removeWhere((key, value) => key == null || value == null);
    return map;
  }
}

class LatLongDto {
  double longitude;
  double latitude;

  LatLongDto({
    this.longitude,
    this.latitude,
  });

  factory LatLongDto.fromJson(Map<String, dynamic> json) => LatLongDto(
        latitude: json["latitude"] == null ? null : json["latitude"],
        longitude: json["longitude"] == null ? null : json["longitude"],
      );

  Map<String, dynamic> toJson() {
    Map<String, dynamic> map = {
      "latitude": latitude == null ? null : latitude,
      "longitude": longitude == null ? null : longitude,
    };
    map.removeWhere((key, value) => key == null || value == null);
    return map;
  }
}

BookCarAddress bookCarAddressFromJson(String str) => BookCarAddress.fromJson(json.decode(str));

String bookCarAddressToJson(BookCarAddress data) => json.encode(data.toJson());

class BookCarAddress {
   String toAddress;
   int position;
   int toProvinceId;

  BookCarAddress({
    this.toAddress,
    this.position,
    this.toProvinceId,
  });

  factory BookCarAddress.fromJson(Map<String, dynamic> json) => BookCarAddress(
        toAddress: json["toAddress"],
        position: json["position"],
        toProvinceId: json["toProvinceId"],
      );

  Map<String, dynamic> toJson() => {
        "toAddress": toAddress,
        "position": position,
        "toProvinceId": toProvinceId,
      };
}
