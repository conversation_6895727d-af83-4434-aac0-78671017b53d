import '../models.dart';

class SysUserRequest {
  AuthenticationInfo authenticationInfo;
  int sysUserId;
  int flag;
  String email;
  String loginName;

  SysUserRequest({
    this.authenticationInfo,
    this.sysUserId,
    this.flag,
    this.email,
    this.loginName,
  });

  SysUserRequest.fromJson(Map<String, dynamic> json) {
    authenticationInfo = json['authenticationInfo'] != null
        ? new AuthenticationInfo.fromJson(
        json['authenticationInfo'])
        : null;
    sysUserId = json['sysUserId'];
    flag = json['flag'];
    email = json['email'];
    loginName = json['loginName'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.authenticationInfo != null) {
      data['authenticationInfo'] = this.authenticationInfo.toJson();
    }
    data['sysUserId'] = this.sysUserId;
    data['flag'] = this.flag;
    data['email'] = this.email;
    data['loginName'] = this.loginName;
    data.removeWhere((key, value) => key == null || value == null);
    return data;
  }
}