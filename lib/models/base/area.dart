class Area {
  String defaultSortField;
  int messageColumn;
  int areaId;
  String codeLocation;
  String nameLocation;
  int provinceId;
  String areaLevel;
  int flag;
  Null fromDate;
  Null toDate;

  Area(
      {this.defaultSortField,
      this.messageColumn,
      this.areaId,
      this.codeLocation,
      this.nameLocation,
      this.provinceId,
      this.areaLevel,
      this.flag,
      this.fromDate,
      this.toDate});

  Area.fromJson(Map<String, dynamic> json) {
    defaultSortField = json['defaultSortField'];
    messageColumn = json['messageColumn'];
    areaId = json['areaId'];
    codeLocation = json['codeLocation'];
    nameLocation = json['nameLocation'];
    provinceId = json['provinceId'];
    areaLevel = json['areaLevel'];
    flag = json['flag'];
    fromDate = json['fromDate'];
    toDate = json['toDate'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['defaultSortField'] = this.defaultSortField;
    data['messageColumn'] = this.messageColumn;
    data['areaId'] = this.areaId;
    data['codeLocation'] = this.codeLocation;
    data['nameLocation'] = this.nameLocation;
    data['provinceId'] = this.provinceId;
    data['areaLevel'] = this.areaLevel;
    data['flag'] = this.flag;
    data['fromDate'] = this.fromDate;
    data['toDate'] = this.toDate;
    return data;
  }
}
