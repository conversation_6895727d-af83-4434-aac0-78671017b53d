class LstServeFieldDto {
  LstServeFieldDto({
    this.name,
    this.code,
  });

  String name;
  int code;

  factory LstServeFieldDto.fromJson(Map<String, dynamic> json) =>
      LstServeFieldDto(
        name: json["name"] == null ? null : json["name"],
        code: json["code"] == null ? null : json["code"],
      );

  Map<String, dynamic> toJson() {
    Map<String, dynamic> map = {
      "name": name == null ? null : name,
      "code": code == null ? null : code,
    };
    map.removeWhere((key, value) => key == null || value == null);
    return map;
  }
}
