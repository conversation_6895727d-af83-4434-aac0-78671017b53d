class AuthenticationInfo {
  AuthenticationInfo({this.password, this.username, this.version});

  String password;
  String username;
  String version;

  factory AuthenticationInfo.fromJson(Map<String, dynamic> json) =>
      AuthenticationInfo(
        password: json["password"] == null ? null : json["password"],
        username: j<PERSON>["username"] == null ? null : json["username"],
        version: json["version"] == null ? null : json["version"],
      );

  Map<String, dynamic> toJson() {
    Map<String, dynamic> map = {
      "password": password == null ? null : password,
      "username": username == null ? null : username,
      "version": version == null ? null : version,
    };
    map.removeWhere((key, value) => key == null || value == null);
    return map;
  }
}
