// To parse this JSON data, do
//
//     final googleAddressDto = googleAddressDtoFromJson(jsonString);

import 'dart:convert';

GoogleAddressDto googleAddressDtoFromJson(String str) => GoogleAddressDto.fromJson(json.decode(str));

String googleAddressDtoToJson(GoogleAddressDto data) => json.encode(data.toJson());

class GoogleAddressDto {
  List<Result> results;
  String status;

  GoogleAddressDto({
    this.results,
    this.status,
  });

  factory GoogleAddressDto.fromJson(Map<String, dynamic> json) => GoogleAddressDto(
        results: List<Result>.from(json["results"].map((x) => Result.fromJson(x))),
        status: json["status"],
      );

  Map<String, dynamic> toJson() => {
        "results": List<dynamic>.from(results.map((x) => x.toJson())),
        "status": status,
      };
}

class Result {
  List<AddressComponent> addressComponents;
  String formattedAddress;
  Geometry geometry;
  String placeId;
  List<String> types;

  Result({
    this.addressComponents,
    this.formattedAddress,
    this.geometry,
    this.placeId,
    this.types,
  });

  factory Result.fromJson(Map<String, dynamic> json) => Result(
        addressComponents: List<AddressComponent>.from(json["address_components"].map((x) => AddressComponent.fromJson(x))),
        formattedAddress: json["formatted_address"],
        geometry: Geometry.fromJson(json["geometry"]),
        placeId: json["place_id"],
        types: List<String>.from(json["types"].map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "address_components": List<dynamic>.from(addressComponents.map((x) => x.toJson())),
        "formatted_address": formattedAddress,
        "geometry": geometry.toJson(),
        "place_id": placeId,
        "types": List<dynamic>.from(types.map((x) => x)),
      };
}

class AddressComponent {
  String longName;
  String shortName;
  List<String> types;

  AddressComponent({
    this.longName,
    this.shortName,
    this.types,
  });

  factory AddressComponent.fromJson(Map<String, dynamic> json) => AddressComponent(
        longName: json["long_name"],
        shortName: json["short_name"],
        types: List<String>.from(json["types"].map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "long_name": longName,
        "short_name": shortName,
        "types": List<dynamic>.from(types.map((x) => x)),
      };
}

class Geometry {
  Bounds bounds;
  LocationAddress location;
  String locationType;
  Bounds viewport;

  Geometry({
    this.bounds,
    this.location,
    this.locationType,
    this.viewport,
  });

  factory Geometry.fromJson(Map<String, dynamic> json) => Geometry(
        bounds: Bounds.fromJson(json["bounds"]),
        location: LocationAddress.fromJson(json["location"]),
        locationType: json["location_type"],
        viewport: Bounds.fromJson(json["viewport"]),
      );

  Map<String, dynamic> toJson() => {
        "bounds": bounds.toJson(),
        "location": location.toJson(),
        "location_type": locationType,
        "viewport": viewport.toJson(),
      };
}

class Bounds {
  LocationAddress northeast;
  LocationAddress southwest;

  Bounds({
    this.northeast,
    this.southwest,
  });

  factory Bounds.fromJson(Map<String, dynamic> json) => Bounds(
        northeast: LocationAddress.fromJson(json["northeast"]),
        southwest: LocationAddress.fromJson(json["southwest"]),
      );

  Map<String, dynamic> toJson() => {
        "northeast": northeast.toJson(),
        "southwest": southwest.toJson(),
      };
}

class LocationAddress {
  double lat;
  double lng;

  LocationAddress({
    this.lat,
    this.lng,
  });

  factory LocationAddress.fromJson(Map<String, dynamic> json) => LocationAddress(
        lat: json["lat"].toDouble(),
        lng: json["lng"].toDouble(),
      );

  Map<String, dynamic> toJson() => {
        "lat": lat,
        "lng": lng,
      };
}
