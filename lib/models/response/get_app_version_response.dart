import 'get_history_detail_car_response.dart';

class GetAppVersionResponse {
  ResultInfo resultInfo;
  AppVersionWorkItemDTO appVersionWorkItemDTO;

  GetAppVersionResponse({
    this.resultInfo,
    this.appVersionWorkItemDTO,
  });

  GetAppVersionResponse.fromJson(Map<String, dynamic> json) {
    resultInfo = json['resultInfo'] != null ? new ResultInfo.fromJson(json['resultInfo']) : null;
    appVersionWorkItemDTO = json['appVersionWorkItemDTO'] != null ? new AppVersionWorkItemDTO.fromJson(json['appVersionWorkItemDTO']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.resultInfo != null) {
      data['resultInfo'] = this.resultInfo.toJson();
    }
    if (this.appVersionWorkItemDTO != null) {
      data['appVersionWorkItemDTO'] = this.appVersionWorkItemDTO.toJson();
    }
    return data;
  }
}

class AppVersionWorkItemDTO {
  String version;
  String link;

  AppVersionWorkItemDTO({
    this.version,
    this.link,
  });

  AppVersionWorkItemDTO.fromJson(Map<String, dynamic> json) {
    version = json['version'];
    link = json['link'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['version'] = this.version;
    data['link'] = this.link;
    return data;
  }
}
