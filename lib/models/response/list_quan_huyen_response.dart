import 'package:trackcarvcc/models/models.dart';

class ListQuanHuyenResponse {
  ResultInfo resultInfo;
  Null lstBookCarDto;
  Null bookCarDto;
  Null areaProvinceCity;
  List<Area> areaDistrict;
  Null areaWard;
  Null catVehicleDTO;
  Null cost;
  Null total;
  Null lstApprovePersons;
  Null lstCarLocationCurrents;

  ListQuanHuyenResponse(
      {this.resultInfo,
      this.lstBookCarDto,
      this.bookCarDto,
      this.areaProvinceCity,
      this.areaDistrict,
      this.areaWard,
      this.catVehicleDTO,
      this.cost,
      this.total,
      this.lstApprovePersons,
      this.lstCarLocationCurrents});

  ListQuanHuyenResponse.fromJson(Map<String, dynamic> json) {
    resultInfo = json['resultInfo'] != null
        ? new ResultInfo.fromJson(json['resultInfo'])
        : null;
    lstBookCarDto = json['lstBookCarDto'];
    bookCarDto = json['bookCarDto'];
    areaProvinceCity = json['areaProvinceCity'];
    if (json['areaDistrict'] != null) {
      areaDistrict = new List<Area>();
      json['areaDistrict'].forEach((v) {
        areaDistrict.add(new Area.fromJson(v));
      });
    }
    areaWard = json['areaWard'];
    catVehicleDTO = json['catVehicleDTO'];
    cost = json['cost'];
    total = json['total'];
    lstApprovePersons = json['lstApprovePersons'];
    lstCarLocationCurrents = json['lstCarLocationCurrents'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.resultInfo != null) {
      data['resultInfo'] = this.resultInfo.toJson();
    }
    data['lstBookCarDto'] = this.lstBookCarDto;
    data['bookCarDto'] = this.bookCarDto;
    data['areaProvinceCity'] = this.areaProvinceCity;
    if (this.areaDistrict != null) {
      data['areaDistrict'] = this.areaDistrict.map((v) => v.toJson()).toList();
    }
    data['areaWard'] = this.areaWard;
    data['catVehicleDTO'] = this.catVehicleDTO;
    data['cost'] = this.cost;
    data['total'] = this.total;
    data['lstApprovePersons'] = this.lstApprovePersons;
    data['lstCarLocationCurrents'] = this.lstCarLocationCurrents;
    return data;
  }
}
