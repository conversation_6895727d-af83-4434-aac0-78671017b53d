class ListCarHistoryResponse {
  ResultInfo resultInfo;
  List<LstBookCarDtoHistory> lstBookCarDto;
  Null bookCarDto;
  Null areaProvinceCity;
  Null areaDistrict;
  Null areaWard;
  Null catVehicleDTO;
  Null cost;
  Null total;
  Null lstApprovePersons;
  Null lstCarLocationCurrents;

  ListCarHistoryResponse(
      {this.resultInfo,
        this.lstBookCarDto,
        this.bookCarDto,
        this.areaProvinceCity,
        this.areaDistrict,
        this.areaWard,
        this.catVehicleDTO,
        this.cost,
        this.total,
        this.lstApprovePersons,
        this.lstCarLocationCurrents});

  ListCarHistoryResponse.fromJson(Map<String, dynamic> json) {
    resultInfo = json['resultInfo'] != null
        ? new ResultInfo.fromJson(json['resultInfo'])
        : null;
    if (json['lstBookCarDto'] != null) {
      lstBookCarDto = new List<LstBookCarDtoHistory>();
      json['lstBookCarDto'].forEach((v) {
        lstBookCarDto.add(new LstBookCarDtoHistory.fromJson(v));
      });
    }
    bookCarDto = json['bookCarDto'];
    areaProvinceCity = json['areaProvinceCity'];
    areaDistrict = json['areaDistrict'];
    areaWard = json['areaWard'];
    catVehicleDTO = json['catVehicleDTO'];
    cost = json['cost'];
    total = json['total'];
    lstApprovePersons = json['lstApprovePersons'];
    lstCarLocationCurrents = json['lstCarLocationCurrents'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.resultInfo != null) {
      data['resultInfo'] = this.resultInfo.toJson();
    }
    if (this.lstBookCarDto != null) {
      data['lstBookCarDto'] =
          this.lstBookCarDto.map((v) => v.toJson()).toList();
    }
    data['bookCarDto'] = this.bookCarDto;
    data['areaProvinceCity'] = this.areaProvinceCity;
    data['areaDistrict'] = this.areaDistrict;
    data['areaWard'] = this.areaWard;
    data['catVehicleDTO'] = this.catVehicleDTO;
    data['cost'] = this.cost;
    data['total'] = this.total;
    data['lstApprovePersons'] = this.lstApprovePersons;
    data['lstCarLocationCurrents'] = this.lstCarLocationCurrents;
    return data;
  }
}

class ResultInfo {
  String status;
  Null message;

  ResultInfo({this.status, this.message});

  ResultInfo.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this.status;
    data['message'] = this.message;
    return data;
  }
}

class LstBookCarDtoHistory {
  String defaultSortField;
  int messageColumn;
  int bookCarId;
  String code;
  String fromAddress;
  String toAddress;
  String licenseCar;
  int flag;
  Null fromDate;
  Null toDate;
  int fwmodelId;

  LstBookCarDtoHistory(
      {this.defaultSortField,
        this.messageColumn,
        this.bookCarId,
        this.code,
        this.fromAddress,
        this.toAddress,
        this.licenseCar,
        this.flag,
        this.fromDate,
        this.toDate,
        this.fwmodelId});

  LstBookCarDtoHistory.fromJson(Map<String, dynamic> json) {
    defaultSortField = json['defaultSortField'];
    messageColumn = json['messageColumn'];
    bookCarId = json['bookCarId'];
    code = json['code'];
    fromAddress = json['fromAddress'];
    toAddress = json['toAddress'];
    licenseCar = json['licenseCar'];
    flag = json['flag'];
    fromDate = json['fromDate'];
    toDate = json['toDate'];
    fwmodelId = json['fwmodelId'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['defaultSortField'] = this.defaultSortField;
    data['messageColumn'] = this.messageColumn;
    data['bookCarId'] = this.bookCarId;
    data['code'] = this.code;
    data['fromAddress'] = this.fromAddress;
    data['toAddress'] = this.toAddress;
    data['licenseCar'] = this.licenseCar;
    data['flag'] = this.flag;
    data['fromDate'] = this.fromDate;
    data['toDate'] = this.toDate;
    data['fwmodelId'] = this.fwmodelId;
    return data;
  }
}
