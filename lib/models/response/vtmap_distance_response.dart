// To parse this JSON data, do
//
//     final vtMapDistanceResponse = vtMapDistanceResponseFromJson(jsonString);

import 'dart:convert';

VtMapDistanceResponse vtMapDistanceResponseFromJson(String str) => VtMapDistanceResponse.fromJson(json.decode(str));

String vtMapDistanceResponseToJson(VtMapDistanceResponse data) => json.encode(data.toJson());

class VtMapDistanceResponse {
  final List<String> destinationAddresses;
  final List<String> originAddresses;
  final List<Row> rows;
  final String status;

  VtMapDistanceResponse({
    this.destinationAddresses,
    this.originAddresses,
    this.rows,
    this.status,
  });

  factory VtMapDistanceResponse.fromJson(Map<String, dynamic> json) => VtMapDistanceResponse(
    destinationAddresses: List<String>.from(json["destination_addresses"].map((x) => x)),
    originAddresses: List<String>.from(json["origin_addresses"].map((x) => x)),
    rows: List<Row>.from(json["rows"].map((x) => Row.fromJson(x))),
    status: json["status"],
  );

  Map<String, dynamic> toJson() => {
    "destination_addresses": List<dynamic>.from(destinationAddresses.map((x) => x)),
    "origin_addresses": List<dynamic>.from(originAddresses.map((x) => x)),
    "rows": List<dynamic>.from(rows.map((x) => x.toJson())),
    "status": status,
  };
}

class Row {
  final List<Element> elements;

  Row({
    this.elements,
  });

  factory Row.fromJson(Map<String, dynamic> json) => Row(
    elements: List<Element>.from(json["elements"].map((x) => Element.fromJson(x))),
  );

  Map<String, dynamic> toJson() => {
    "elements": List<dynamic>.from(elements.map((x) => x.toJson())),
  };
}

class Element {
  final String status;
  final Distance distance;
  final Distance duration;

  Element({
    this.status,
    this.distance,
    this.duration,
  });

  factory Element.fromJson(Map<String, dynamic> json) => Element(
    status: json["status"],
    distance: Distance.fromJson(json["distance"]),
    duration: Distance.fromJson(json["duration"]),
  );

  Map<String, dynamic> toJson() => {
    "status": status,
    "distance": distance.toJson(),
    "duration": duration.toJson(),
  };
}

class Distance {
  final String text;
  final double value;

  Distance({
    this.text,
    this.value,
  });

  factory Distance.fromJson(Map<String, dynamic> json) => Distance(
    text: json["text"],
    value: json["value"].toDouble(),
  );

  Map<String, dynamic> toJson() => {
    "text": text,
    "value": value,
  };
}
