import 'package:trackcarvcc/models/models.dart';

class ApproveRejectBookCarResponse {
  ResultInfo resultInfo;
  List<LstBookCarDto> lstBookCarDto;
  LstBookCarDto bookCarDto;

  ApproveRejectBookCarResponse({
    this.resultInfo,
    this.lstBookCarDto,
    this.bookCarDto,
  });

  ApproveRejectBookCarResponse.fromJson(Map<String, dynamic> json) {
    resultInfo = json['resultInfo'] != null
        ? ResultInfo.fromJson(json['resultInfo'])
        : null;
    if (json['lstBookCarDto'] != null) {
      lstBookCarDto = List<LstBookCarDto>();
      json['lstBookCarDto'].forEach((v) {
        lstBookCarDto.add(LstBookCarDto.fromJson(v));
      });
    }
    bookCarDto = json['bookCarDto'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    if (this.resultInfo != null) {
      data['resultInfo'] = this.resultInfo.toJson();
    }
    if (this.lstBookCarDto != null) {
      data['lstBookCarDto'] =
          this.lstBookCarDto.map((v) => v.toJson()).toList();
    }
    data['bookCarDto'] = this.bookCarDto;
    return data;
  }
}
