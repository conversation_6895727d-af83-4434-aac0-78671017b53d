class ListCarByUnitResponse {
  ResultInfo resultInfo;
  Null lstBookCarDto;
  Null bookCarDto;
  Null areaProvinceCity;
  Null areaDistrict;
  Null areaWard;
  List<CatVehicleDTO> catVehicleDTO;
  Null cost;
  Null total;
  Null lstApprovePersons;
  Null lstCarLocationCurrents;

  ListCarByUnitResponse(
      {this.resultInfo,
        this.lstBookCarDto,
        this.bookCarDto,
        this.areaProvinceCity,
        this.areaDistrict,
        this.areaWard,
        this.catVehicleDTO,
        this.cost,
        this.total,
        this.lstApprovePersons,
        this.lstCarLocationCurrents});

  ListCarByUnitResponse.fromJson(Map<String, dynamic> json) {
    resultInfo = json['resultInfo'] != null
        ? new ResultInfo.fromJson(json['resultInfo'])
        : null;
    lstBookCarDto = json['lstBookCarDto'];
    bookCarDto = json['bookCarDto'];
    areaProvinceCity = json['areaProvinceCity'];
    areaDistrict = json['areaDistrict'];
    areaWard = json['areaWard'];
    if (json['catVehicleDTO'] != null) {
      catVehicleDTO = new List<CatVehicleDTO>();
      json['catVehicleDTO'].forEach((v) {
        catVehicleDTO.add(new CatVehicleDTO.fromJson(v));
      });
    }
    cost = json['cost'];
    total = json['total'];
    lstApprovePersons = json['lstApprovePersons'];
    lstCarLocationCurrents = json['lstCarLocationCurrents'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.resultInfo != null) {
      data['resultInfo'] = this.resultInfo.toJson();
    }
    data['lstBookCarDto'] = this.lstBookCarDto;
    data['bookCarDto'] = this.bookCarDto;
    data['areaProvinceCity'] = this.areaProvinceCity;
    data['areaDistrict'] = this.areaDistrict;
    data['areaWard'] = this.areaWard;
    if (this.catVehicleDTO != null) {
      data['catVehicleDTO'] =
          this.catVehicleDTO.map((v) => v.toJson()).toList();
    }
    data['cost'] = this.cost;
    data['total'] = this.total;
    data['lstApprovePersons'] = this.lstApprovePersons;
    data['lstCarLocationCurrents'] = this.lstCarLocationCurrents;
    return data;
  }
}

class ResultInfo {
  String status;
  Null message;

  ResultInfo({this.status, this.message});

  ResultInfo.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this.status;
    data['message'] = this.message;
    return data;
  }
}

class CatVehicleDTO {
  String defaultSortField;
  int messageColumn;
  int carId;
  String licenseCar;
  String onCommand;
  int sysGroupId;
  String sysGroupName;
  Null registrationDate;
  bool importing;

  CatVehicleDTO(
      {this.defaultSortField,
        this.messageColumn,
        this.carId,
        this.licenseCar,
        this.onCommand,
        this.sysGroupId,
        this.sysGroupName,
        this.registrationDate,
        this.importing});

  CatVehicleDTO.fromJson(Map<String, dynamic> json) {
    defaultSortField = json['defaultSortField'];
    messageColumn = json['messageColumn'];
    carId = json['carId'];
    licenseCar = json['licenseCar'];
    onCommand = json['onCommand'];
    sysGroupId = json['sysGroupId'];
    sysGroupName = json['sysGroupName'];
    registrationDate = json['registrationDate'];
    importing = json['importing'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['defaultSortField'] = this.defaultSortField;
    data['messageColumn'] = this.messageColumn;
    data['carId'] = this.carId;
    data['licenseCar'] = this.licenseCar;
    data['onCommand'] = this.onCommand;
    data['sysGroupId'] = this.sysGroupId;
    data['sysGroupName'] = this.sysGroupName;
    data['registrationDate'] = this.registrationDate;
    data['importing'] = this.importing;
    return data;
  }
}
