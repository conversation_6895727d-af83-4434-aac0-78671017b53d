import '../models.dart';

class ListBookCarResponse {
  ListBookCarResponse({
    this.resultInfo,
    this.lstBookCarDto,
    this.bookCarDto,
    this.areaProvinceCity,
    this.areaDistrict,
    this.areaWard,
    this.catVehicleDto,
    this.cost,
    this.total,
    this.lstApprovePersons,
    this.lstCarLocationCurrents,
  });

  ResultInfo resultInfo;
  List<LstBookCarDto> lstBookCarDto;
  dynamic bookCarDto;
  dynamic areaProvinceCity;
  dynamic areaDistrict;
  dynamic areaWard;
  dynamic catVehicleDto;
  dynamic cost;
  dynamic total;
  dynamic lstApprovePersons;
  dynamic lstCarLocationCurrents;

  factory ListBookCarResponse.fromJson(Map<String, dynamic> json) =>
      ListBookCarResponse(
        resultInfo: json["resultInfo"] == null
            ? null
            : ResultInfo.fromJson(json["resultInfo"]),
        lstBookCarDto: json["lstBookCarDto"] == null
            ? null
            : List<LstBookCarDto>.from(
                json["lstBookCarDto"].map((x) => LstBookCarDto.fromJson(x))),
        bookCarDto: json["bookCarDto"],
        areaProvinceCity: json["areaProvinceCity"],
        areaDistrict: json["areaDistrict"],
        areaWard: json["areaWard"],
        catVehicleDto: json["catVehicleDTO"],
        cost: json["cost"],
        total: json["total"],
        lstApprovePersons: json["lstApprovePersons"],
        lstCarLocationCurrents: json["lstCarLocationCurrents"],
      );

  Map<String, dynamic> toJson() => {
        "resultInfo": resultInfo == null ? null : resultInfo.toJson(),
        "lstBookCarDto": lstBookCarDto == null
            ? null
            : List<dynamic>.from(lstBookCarDto.map((x) => x.toJson())),
        "bookCarDto": bookCarDto,
        "areaProvinceCity": areaProvinceCity,
        "areaDistrict": areaDistrict,
        "areaWard": areaWard,
        "catVehicleDTO": catVehicleDto,
        "cost": cost,
        "total": total,
        "lstApprovePersons": lstApprovePersons,
        "lstCarLocationCurrents": lstCarLocationCurrents,
      };
}
