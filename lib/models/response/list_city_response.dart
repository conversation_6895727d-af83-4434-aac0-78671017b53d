import 'package:trackcarvcc/models/models.dart';

class ListCityResponse {
  ResultInfo resultInfo;
  Null lstBookCarDto;
  Null bookCarDto;
  List<Area> areaProvinceCity;
  Null areaDistrict;
  Null areaWard;
  Null catVehicleDTO;
  Null cost;
  Null total;
  Null lstApprovePersons;
  Null lstCarLocationCurrents;

  ListCityResponse(
      {this.resultInfo,
      this.lstBookCarDto,
      this.bookCarDto,
      this.areaProvinceCity,
      this.areaDistrict,
      this.areaWard,
      this.catVehicleDTO,
      this.cost,
      this.total,
      this.lstApprovePersons,
      this.lstCarLocationCurrents});

  ListCityResponse.fromJson(Map<String, dynamic> json) {
    resultInfo = json['resultInfo'] != null
        ? new ResultInfo.fromJson(json['resultInfo'])
        : null;
    lstBookCarDto = json['lstBookCarDto'];
    bookCarDto = json['bookCarDto'];
    if (json['areaProvinceCity'] != null) {
      areaProvinceCity = new List<Area>();
      json['areaProvinceCity'].forEach((v) {
        areaProvinceCity.add(new Area.fromJson(v));
      });
    }
    areaDistrict = json['areaDistrict'];
    areaWard = json['areaWard'];
    catVehicleDTO = json['catVehicleDTO'];
    cost = json['cost'];
    total = json['total'];
    lstApprovePersons = json['lstApprovePersons'];
    lstCarLocationCurrents = json['lstCarLocationCurrents'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.resultInfo != null) {
      data['resultInfo'] = this.resultInfo.toJson();
    }
    data['lstBookCarDto'] = this.lstBookCarDto;
    data['bookCarDto'] = this.bookCarDto;
    if (this.areaProvinceCity != null) {
      data['areaProvinceCity'] =
          this.areaProvinceCity.map((v) => v.toJson()).toList();
    }
    data['areaDistrict'] = this.areaDistrict;
    data['areaWard'] = this.areaWard;
    data['catVehicleDTO'] = this.catVehicleDTO;
    data['cost'] = this.cost;
    data['total'] = this.total;
    data['lstApprovePersons'] = this.lstApprovePersons;
    data['lstCarLocationCurrents'] = this.lstCarLocationCurrents;
    return data;
  }
}
