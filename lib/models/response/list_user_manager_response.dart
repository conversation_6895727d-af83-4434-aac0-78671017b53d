import 'package:trackcarvcc/models/models.dart';

class ListUserManagerResponse {
  ResultInfo resultInfo;
  List<UserLogin> lstBookCarDto;
  Null bookCarDto;
  Null areaProvinceCity;
  Null areaDistrict;
  Null areaWard;
  Null catVehicleDTO;
  Null cost;
  Null total;
  Null lstApprovePersons;
  Null lstCarLocationCurrents;

  ListUserManagerResponse(
      {this.resultInfo,
      this.lstBookCarDto,
      this.bookCarDto,
      this.areaProvinceCity,
      this.areaDistrict,
      this.areaWard,
      this.catVehicleDTO,
      this.cost,
      this.total,
      this.lstApprovePersons,
      this.lstCarLocationCurrents});

  ListUserManagerResponse.fromJson(Map<String, dynamic> json) {
    resultInfo = json['resultInfo'] != null
        ? new ResultInfo.fromJson(json['resultInfo'])
        : null;
    if (json['lstBookCarDto'] != null) {
      lstBookCarDto = new List<UserLogin>();
      json['lstBookCarDto'].forEach((v) {
        lstBookCarDto.add(new UserLogin.fromJson(v));
      });
    }
    bookCarDto = json['bookCarDto'];
    areaProvinceCity = json['areaProvinceCity'];
    areaDistrict = json['areaDistrict'];
    areaWard = json['areaWard'];
    catVehicleDTO = json['catVehicleDTO'];
    cost = json['cost'];
    total = json['total'];
    lstApprovePersons = json['lstApprovePersons'];
    lstCarLocationCurrents = json['lstCarLocationCurrents'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.resultInfo != null) {
      data['resultInfo'] = this.resultInfo.toJson();
    }
    if (this.lstBookCarDto != null) {
      data['lstBookCarDto'] =
          this.lstBookCarDto.map((v) => v.toJson()).toList();
    }
    data['bookCarDto'] = this.bookCarDto;
    data['areaProvinceCity'] = this.areaProvinceCity;
    data['areaDistrict'] = this.areaDistrict;
    data['areaWard'] = this.areaWard;
    data['catVehicleDTO'] = this.catVehicleDTO;
    data['cost'] = this.cost;
    data['total'] = this.total;
    data['lstApprovePersons'] = this.lstApprovePersons;
    data['lstCarLocationCurrents'] = this.lstCarLocationCurrents;
    return data;
  }
}
