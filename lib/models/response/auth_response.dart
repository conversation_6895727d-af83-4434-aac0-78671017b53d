import '../base/result_info.dart';
import '../models.dart';

class AuthResponse {
  AuthResponse({
    this.lstSysUserComDto,
    this.resultInfo,
    this.userLogin,
    this.user,
    this.listUser,
  });

  dynamic lstSysUserComDto;
  ResultInfo resultInfo;
  UserLogin userLogin;
  dynamic user;
  dynamic listUser;

  factory AuthResponse.fromJson(Map<String, dynamic> json) => AuthResponse(
        lstSysUserComDto: json["lstSysUserComDTO"],
        resultInfo: json["resultInfo"] == null
            ? null
            : ResultInfo.fromJson(json["resultInfo"]),
        userLogin: json["userLogin"] == null
            ? null
            : UserLogin.fromJson(json["userLogin"]),
        user: json["user"],
        listUser: json["listUser"],
      );

  Map<String, dynamic> toJson() => {
        "lstSysUserComDTO": lstSysUserComDto,
        "resultInfo": resultInfo.toJson(),
        "userLogin": userLogin.toJson(),
        "user": user,
        "listUser": listUser,
      };
}
