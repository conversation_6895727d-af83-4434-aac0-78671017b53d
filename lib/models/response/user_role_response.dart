import 'package:trackcarvcc/models/models.dart';

class UserRoleResponse {
  ResultInfo resultInfo;
  Null lstBookCarDto;
  Null bookCarDto;
  Null areaProvinceCity;
  Null areaDistrict;
  Null areaWard;
  Null catVehicleDTO;
  Null cost;
  Null total;
  List<LstApprovePersons> lstApprovePersons;
  Null lstCarLocationCurrents;

  UserRoleResponse(
      {this.resultInfo,
        this.lstBookCarDto,
        this.bookCarDto,
        this.areaProvinceCity,
        this.areaDistrict,
        this.areaWard,
        this.catVehicleDTO,
        this.cost,
        this.total,
        this.lstApprovePersons,
        this.lstCarLocationCurrents});

  UserRoleResponse.fromJson(Map<String, dynamic> json) {
    resultInfo = json['resultInfo'] != null
        ? new ResultInfo.fromJson(json['resultInfo'])
        : null;
    lstBookCarDto = json['lstBookCarDto'];
    bookCarDto = json['bookCarDto'];
    areaProvinceCity = json['areaProvinceCity'];
    areaDistrict = json['areaDistrict'];
    areaWard = json['areaWard'];
    catVehicleDTO = json['catVehicleDTO'];
    cost = json['cost'];
    total = json['total'];
    if (json['lstApprovePersons'] != null) {
      lstApprovePersons = new List<LstApprovePersons>();
      json['lstApprovePersons'].forEach((v) {
        lstApprovePersons.add(new LstApprovePersons.fromJson(v));
      });
    }
    lstCarLocationCurrents = json['lstCarLocationCurrents'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.resultInfo != null) {
      data['resultInfo'] = this.resultInfo.toJson();
    }
    data['lstBookCarDto'] = this.lstBookCarDto;
    data['bookCarDto'] = this.bookCarDto;
    data['areaProvinceCity'] = this.areaProvinceCity;
    data['areaDistrict'] = this.areaDistrict;
    data['areaWard'] = this.areaWard;
    data['catVehicleDTO'] = this.catVehicleDTO;
    data['cost'] = this.cost;
    data['total'] = this.total;
    if (this.lstApprovePersons != null) {
      data['lstApprovePersons'] =
          this.lstApprovePersons.map((v) => v.toJson()).toList();
    }
    data['lstCarLocationCurrents'] = this.lstCarLocationCurrents;
    return data;
  }
}

class LstApprovePersons {
  String defaultSortField;
  Null page;
  Null id;
  Null pathFile;
  Null filePathError;
  Null text;
  bool isSize;
  Null pageSize;
  Null keySearch;
  Null keySearchAction;
  Null keySearch2;
  Null totalRecord;
  Null updatedDate;
  Null updatedBy;
  Null createdDate;
  Null createdBy;
  Null description;
  int appParamId;
  String parOrder;
  String parType;
  String name;
  String code;
  Null status;
  int fwmodelId;

  LstApprovePersons(
      {this.defaultSortField,
        this.page,
        this.id,
        this.pathFile,
        this.filePathError,
        this.text,
        this.isSize,
        this.pageSize,
        this.keySearch,
        this.keySearchAction,
        this.keySearch2,
        this.totalRecord,
        this.updatedDate,
        this.updatedBy,
        this.createdDate,
        this.createdBy,
        this.description,
        this.appParamId,
        this.parOrder,
        this.parType,
        this.name,
        this.code,
        this.status,
        this.fwmodelId});

  LstApprovePersons.fromJson(Map<String, dynamic> json) {
    defaultSortField = json['defaultSortField'];
    page = json['page'];
    id = json['id'];
    pathFile = json['pathFile'];
    filePathError = json['filePathError'];
    text = json['text'];
    isSize = json['isSize'];
    pageSize = json['pageSize'];
    keySearch = json['keySearch'];
    keySearchAction = json['keySearchAction'];
    keySearch2 = json['keySearch2'];
    totalRecord = json['totalRecord'];
    updatedDate = json['updatedDate'];
    updatedBy = json['updatedBy'];
    createdDate = json['createdDate'];
    createdBy = json['createdBy'];
    description = json['description'];
    appParamId = json['appParamId'];
    parOrder = json['parOrder'];
    parType = json['parType'];
    name = json['name'];
    code = json['code'];
    status = json['status'];
    fwmodelId = json['fwmodelId'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['defaultSortField'] = this.defaultSortField;
    data['page'] = this.page;
    data['id'] = this.id;
    data['pathFile'] = this.pathFile;
    data['filePathError'] = this.filePathError;
    data['text'] = this.text;
    data['isSize'] = this.isSize;
    data['pageSize'] = this.pageSize;
    data['keySearch'] = this.keySearch;
    data['keySearchAction'] = this.keySearchAction;
    data['keySearch2'] = this.keySearch2;
    data['totalRecord'] = this.totalRecord;
    data['updatedDate'] = this.updatedDate;
    data['updatedBy'] = this.updatedBy;
    data['createdDate'] = this.createdDate;
    data['createdBy'] = this.createdBy;
    data['description'] = this.description;
    data['appParamId'] = this.appParamId;
    data['parOrder'] = this.parOrder;
    data['parType'] = this.parType;
    data['name'] = this.name;
    data['code'] = this.code;
    data['status'] = this.status;
    data['fwmodelId'] = this.fwmodelId;
    return data;
  }
}
