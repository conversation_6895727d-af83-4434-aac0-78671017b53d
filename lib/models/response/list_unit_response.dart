class ListUnitResponse {
  ResultInfo resultInfo;
  List<LstBookCarDtoUnit> lstBookCarDto;
  Null bookCarDto;
  Null areaProvinceCity;
  Null areaDistrict;
  Null areaWard;
  Null catVehicleDTO;
  Null cost;
  Null total;
  Null lstApprovePersons;
  Null lstCarLocationCurrents;

  ListUnitResponse(
      {this.resultInfo,
        this.lstBookCarDto,
        this.bookCarDto,
        this.areaProvinceCity,
        this.areaDistrict,
        this.areaWard,
        this.catVehicleDTO,
        this.cost,
        this.total,
        this.lstApprovePersons,
        this.lstCarLocationCurrents});

  ListUnitResponse.fromJson(Map<String, dynamic> json) {
    resultInfo = json['resultInfo'] != null
        ? new ResultInfo.fromJson(json['resultInfo'])
        : null;
    if (json['lstBookCarDto'] != null) {
      lstBookCarDto = new List<LstBookCarDtoUnit>();
      json['lstBookCarDto'].forEach((v) {
        lstBookCarDto.add(new LstBookCarDtoUnit.fromJson(v));
      });
    }
    bookCarDto = json['bookCarDto'];
    areaProvinceCity = json['areaProvinceCity'];
    areaDistrict = json['areaDistrict'];
    areaWard = json['areaWard'];
    catVehicleDTO = json['catVehicleDTO'];
    cost = json['cost'];
    total = json['total'];
    lstApprovePersons = json['lstApprovePersons'];
    lstCarLocationCurrents = json['lstCarLocationCurrents'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.resultInfo != null) {
      data['resultInfo'] = this.resultInfo.toJson();
    }
    if (this.lstBookCarDto != null) {
      data['lstBookCarDto'] =
          this.lstBookCarDto.map((v) => v.toJson()).toList();
    }
    data['bookCarDto'] = this.bookCarDto;
    data['areaProvinceCity'] = this.areaProvinceCity;
    data['areaDistrict'] = this.areaDistrict;
    data['areaWard'] = this.areaWard;
    data['catVehicleDTO'] = this.catVehicleDTO;
    data['cost'] = this.cost;
    data['total'] = this.total;
    data['lstApprovePersons'] = this.lstApprovePersons;
    data['lstCarLocationCurrents'] = this.lstCarLocationCurrents;
    return data;
  }
}

class ResultInfo {
  String status;
  Null message;

  ResultInfo({this.status, this.message});

  ResultInfo.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this.status;
    data['message'] = this.message;
    return data;
  }
}

class LstBookCarDtoUnit {
  String defaultSortField;
  int messageColumn;
  int sysGroupId;
  String sysGroupName;
  String sysGroupCode;
  int flag;
  Null fromDate;
  Null toDate;
  String latitude;
  String longtitude;

  LstBookCarDtoUnit(
      {this.defaultSortField,
        this.messageColumn,
        this.sysGroupId,
        this.sysGroupName,
        this.sysGroupCode,
        this.flag,
        this.fromDate,
        this.toDate,
        this.latitude,
        this.longtitude});

  LstBookCarDtoUnit.fromJson(Map<String, dynamic> json) {
    defaultSortField = json['defaultSortField'];
    messageColumn = json['messageColumn'];
    sysGroupId = json['sysGroupId'];
    sysGroupName = json['sysGroupName'];
    sysGroupCode = json['sysGroupCode'];
    flag = json['flag'];
    fromDate = json['fromDate'];
    toDate = json['toDate'];
    latitude = json['latitude'];
    longtitude = json['longtitude'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['defaultSortField'] = this.defaultSortField;
    data['messageColumn'] = this.messageColumn;
    data['sysGroupId'] = this.sysGroupId;
    data['sysGroupName'] = this.sysGroupName;
    data['sysGroupCode'] = this.sysGroupCode;
    data['flag'] = this.flag;
    data['fromDate'] = this.fromDate;
    data['toDate'] = this.toDate;
    data['latitude'] = this.latitude;
    data['longtitude'] = this.longtitude;
    return data;
  }
}
