import 'package:trackcarvcc/models/models.dart';

class UpdateCarLocationResponse {
  ResultInfo resultInfo;

  UpdateCarLocationResponse(
  {this.resultInfo});

  UpdateCarLocationResponse.fromJson(Map<String, dynamic> json) {
    resultInfo = json['resultInfo'] != null
        ? new ResultInfo.fromJson(json['resultInfo'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.resultInfo != null) {
      data['resultInfo'] = this.resultInfo.toJson();
    }
    return data;
  }
}