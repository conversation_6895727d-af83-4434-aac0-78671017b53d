import 'package:trackcarvcc/models/base/result_info.dart';

class CarMonitoringResponse {
  ResultInfo resultInfo;
  Null lstBookCarDto;
  Null bookCarDto;
  Null areaProvinceCity;
  Null areaDistrict;
  Null areaWard;
  Null catVehicleDTO;
  Null cost;
  Null total;
  Null lstApprovePersons;
  List<LstCarLocationCurrents> lstCarLocationCurrents;

  CarMonitoringResponse(
      {this.resultInfo,
        this.lstBookCarDto,
        this.bookCarDto,
        this.areaProvinceCity,
        this.areaDistrict,
        this.areaWard,
        this.catVehicleDTO,
        this.cost,
        this.total,
        this.lstApprovePersons,
        this.lstCarLocationCurrents});

  CarMonitoringResponse.fromJson(Map<String, dynamic> json) {
    resultInfo = json['resultInfo'] != null
        ? new ResultInfo.fromJson(json['resultInfo'])
        : null;
    lstBookCarDto = json['lstBookCarDto'];
    bookCarDto = json['bookCarDto'];
    areaProvinceCity = json['areaProvinceCity'];
    areaDistrict = json['areaDistrict'];
    areaWard = json['areaWard'];
    catVehicleDTO = json['catVehicleDTO'];
    cost = json['cost'];
    total = json['total'];
    lstApprovePersons = json['lstApprovePersons'];
    if (json['lstCarLocationCurrents'] != null) {
      lstCarLocationCurrents = new List<LstCarLocationCurrents>();
      json['lstCarLocationCurrents'].forEach((v) {
        lstCarLocationCurrents.add(new LstCarLocationCurrents.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.resultInfo != null) {
      data['resultInfo'] = this.resultInfo.toJson();
    }
    data['lstBookCarDto'] = this.lstBookCarDto;
    data['bookCarDto'] = this.bookCarDto;
    data['areaProvinceCity'] = this.areaProvinceCity;
    data['areaDistrict'] = this.areaDistrict;
    data['areaWard'] = this.areaWard;
    data['catVehicleDTO'] = this.catVehicleDTO;
    data['cost'] = this.cost;
    data['total'] = this.total;
    data['lstApprovePersons'] = this.lstApprovePersons;
    if (this.lstCarLocationCurrents != null) {
      data['lstCarLocationCurrents'] =
          this.lstCarLocationCurrents.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class LstCarLocationCurrents {
  Null vtrackingCurrentLocationId;
  Null vehicleId;
  Null vehiclePlace;
  Null department;
  Null driverName;
  Null phoneNumber;
  double latitude;
  double longtitude;
  int speed;
  Null carMotorState;
  int status;
  Null utcTime;
  int direction;
  Null insertTme;
  String carState;
  Null map;
  String licenseCar;
  int onCommand;
  int sysGroupId;
  String sysGroupName;
  int carTypeId;

  LstCarLocationCurrents(
      {this.vtrackingCurrentLocationId,
        this.vehicleId,
        this.vehiclePlace,
        this.department,
        this.driverName,
        this.phoneNumber,
        this.latitude,
        this.longtitude,
        this.speed,
        this.carMotorState,
        this.status,
        this.utcTime,
        this.direction,
        this.insertTme,
        this.carState,
        this.map,
        this.licenseCar,
        this.onCommand,
        this.sysGroupId,
        this.sysGroupName,
        this.carTypeId});

  LstCarLocationCurrents.fromJson(Map<String, dynamic> json) {
    vtrackingCurrentLocationId = json['vtrackingCurrentLocationId'];
    vehicleId = json['vehicleId'];
    vehiclePlace = json['vehiclePlace'];
    department = json['department'];
    driverName = json['driverName'];
    phoneNumber = json['phoneNumber'];
    latitude = json['latitude'];
    longtitude = json['longtitude'];
    speed = json['speed'];
    carMotorState = json['carMotorState'];
    status = json['status'];
    utcTime = json['utcTime'];
    direction = json['direction'];
    insertTme = json['insertTme'];
    carState = json['carState'];
    map = json['map'];
    licenseCar = json['licenseCar'];
    onCommand = json['onCommand'];
    sysGroupId = json['sysGroupId'];
    sysGroupName = json['sysGroupName'];
    carTypeId = json['carTypeId'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['vtrackingCurrentLocationId'] = this.vtrackingCurrentLocationId;
    data['vehicleId'] = this.vehicleId;
    data['vehiclePlace'] = this.vehiclePlace;
    data['department'] = this.department;
    data['driverName'] = this.driverName;
    data['phoneNumber'] = this.phoneNumber;
    data['latitude'] = this.latitude;
    data['longtitude'] = this.longtitude;
    data['speed'] = this.speed;
    data['carMotorState'] = this.carMotorState;
    data['status'] = this.status;
    data['utcTime'] = this.utcTime;
    data['direction'] = this.direction;
    data['insertTme'] = this.insertTme;
    data['carState'] = this.carState;
    data['map'] = this.map;
    data['licenseCar'] = this.licenseCar;
    data['onCommand'] = this.onCommand;
    data['sysGroupId'] = this.sysGroupId;
    data['sysGroupName'] = this.sysGroupName;
    data['carTypeId'] = this.carTypeId;
    return data;
  }
}