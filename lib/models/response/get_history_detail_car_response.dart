class GetHistoryCarDetailResponse {
  ResultInfo resultInfo;
  List<LstBookCarDtoDetail> lstBookCarDto;

  GetHistoryCarDetailResponse({this.resultInfo, this.lstBookCarDto});

  GetHistoryCarDetailResponse.fromJson(Map<String, dynamic> json) {
    resultInfo = json['resultInfo'] != null
        ? new ResultInfo.fromJson(json['resultInfo'])
        : null;
    if (json['lstBookCarDto'] != null) {
      lstBookCarDto = new List<LstBookCarDtoDetail>();
      json['lstBookCarDto'].forEach((v) {
        lstBookCarDto.add(new LstBookCarDtoDetail.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.resultInfo != null) {
      data['resultInfo'] = this.resultInfo.toJson();
    }
    if (this.lstBookCarDto != null) {
      data['lstBookCarDto'] =
          this.lstBookCarDto.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class ResultInfo {
  String status;
  Null message;

  ResultInfo({this.status, this.message});

  ResultInfo.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this.status;
    data['message'] = this.message;
    return data;
  }
}

class LstBookCarDtoDetail {
  String defaultSortField;
  int messageColumn;
  int sysUserId;
  String fullName;
  String code;
  int driverId;
  String driverName;
  String latitude;
  String longtitude;
  String utcTime;
  String content;
  int flag;
  Null fromDate;
  Null toDate;

  LstBookCarDtoDetail(
      {this.defaultSortField,
        this.messageColumn,
        this.sysUserId,
        this.fullName,
        this.code,
        this.driverId,
        this.driverName,
        this.latitude,
        this.longtitude,
        this.utcTime,
        this.content,
        this.flag,
        this.fromDate,
        this.toDate});

  LstBookCarDtoDetail.fromJson(Map<String, dynamic> json) {
    defaultSortField = json['defaultSortField'];
    messageColumn = json['messageColumn'];
    sysUserId = json['sysUserId'];
    fullName = json['fullName'];
    code = json['code'];
    driverId = json['driverId'];
    driverName = json['driverName'];
    latitude = json['latitude'];
    longtitude = json['longtitude'];
    utcTime = json['utcTime'];
    content = json['content'];
    flag = json['flag'];
    fromDate = json['fromDate'];
    toDate = json['toDate'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['defaultSortField'] = this.defaultSortField;
    data['messageColumn'] = this.messageColumn;
    data['sysUserId'] = this.sysUserId;
    data['fullName'] = this.fullName;
    data['code'] = this.code;
    data['driverId'] = this.driverId;
    data['driverName'] = this.driverName;
    data['latitude'] = this.latitude;
    data['longtitude'] = this.longtitude;
    data['utcTime'] = this.utcTime;
    data['content'] = this.content;
    data['flag'] = this.flag;
    data['fromDate'] = this.fromDate;
    data['toDate'] = this.toDate;
    return data;
  }
}