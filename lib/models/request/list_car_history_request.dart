import 'package:trackcarvcc/models/base/sys_user_request.dart';

class ListCarHistoryRequest {
  BookCarDto bookCarDto;
  SysUserRequest sysUserRequest;

  ListCarHistoryRequest({this.bookCarDto, this.sysUserRequest});

  ListCarHistoryRequest.fromJson(Map<String, dynamic> json) {
    bookCarDto = json['bookCarDto'] != null
        ? new BookCarDto.fromJson(json['bookCarDto'])
        : null;
    sysUserRequest = json['sysUserRequest'] != null
        ? new SysUserRequest.fromJson(json['sysUserRequest'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.bookCarDto != null) {
      data['bookCarDto'] = this.bookCarDto.toJson();
    }
    if (this.sysUserRequest != null) {
      data['sysUserRequest'] = this.sysUserRequest.toJson();
    }
    return data;
  }
}

class BookCarDto {
  int carId;
  String fromTimeSearch;
  String licenseCar;
  int sysGroupId;
  String toTimeSearch;

  BookCarDto(
      {this.carId,
        this.fromTimeSearch,
        this.licenseCar,
        this.sysGroupId,
        this.toTimeSearch});

  BookCarDto.fromJson(Map<String, dynamic> json) {
    carId = json['carId'];
    fromTimeSearch = json['fromTimeSearch'];
    licenseCar = json['licenseCar'];
    sysGroupId = json['sysGroupId'];
    toTimeSearch = json['toTimeSearch'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['carId'] = this.carId;
    data['fromTimeSearch'] = this.fromTimeSearch;
    data['licenseCar'] = this.licenseCar;
    data['sysGroupId'] = this.sysGroupId;
    data['toTimeSearch'] = this.toTimeSearch;
    return data;
  }
}
