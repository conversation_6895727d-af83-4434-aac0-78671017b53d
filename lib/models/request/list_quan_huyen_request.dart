import 'package:trackcarvcc/models/models.dart';

class ListQuanHuyenRequest {
  BookCarDto bookCarDto;
  SysUserRequest sysUserRequest;

  ListQuanHuyenRequest({this.bookCarDto, this.sysUserRequest});

  ListQuanHuyenRequest.fromJson(Map<String, dynamic> json) {
    bookCarDto = json['bookCarDto'] != null
        ? new BookCarDto.fromJson(json['bookCarDto'])
        : null;
    sysUserRequest = json['sysUserRequest'] != null
        ? new SysUserRequest.fromJson(json['sysUserRequest'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.bookCarDto != null) {
      data['bookCarDto'] = this.bookCarDto.toJson();
    }
    if (this.sysUserRequest != null) {
      data['sysUserRequest'] = this.sysUserRequest.toJson();
    }
    return data;
  }
}

class BookCarDto {
  int parentId;

  BookCarDto({this.parentId});

  BookCarDto.fromJson(Map<String, dynamic> json) {
    parentId = json['parentId'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['parentId'] = this.parentId;
    return data;
  }
}

