import 'package:trackcarvcc/models/base/sys_user_request.dart';

class GetHistoryCarDetailRequest {
  BookCarDtoHistoryCar bookCarDto;
  SysUserRequest sysUserRequest;

  GetHistoryCarDetailRequest({this.bookCarDto, this.sysUserRequest});

  GetHistoryCarDetailRequest.fromJson(Map<String, dynamic> json) {
    bookCarDto = json['bookCarDto'] != null
        ? new BookCarDtoHistoryCar.fromJson(json['bookCarDto'])
        : null;
    sysUserRequest = json['sysUserRequest'] != null
        ? new SysUserRequest.fromJson(json['sysUserRequest'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.bookCarDto != null) {
      data['bookCarDto'] = this.bookCarDto.toJson();
    }
    if (this.sysUserRequest != null) {
      data['sysUserRequest'] = this.sysUserRequest.toJson();
    }
    return data;
  }
}

class BookCarDtoHistoryCar {
  String code;

  BookCarDtoHistoryCar({this.code});

  BookCarDtoHistoryCar.fromJson(Map<String, dynamic> json) {
    code = json['code'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['code'] = this.code;
    return data;
  }
}