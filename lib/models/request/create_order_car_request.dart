import 'package:trackcarvcc/models/models.dart';

class CreateOrderCarRequest {
  LstBookCarDto bookCarDto;
  List<UserLogin> lstPersonTogether;
  SysUserRequest sysUserRequest;

  CreateOrderCarRequest(
      {this.bookCarDto, this.lstPersonTogether, this.sysUserRequest});

  CreateOrderCarRequest.fromJson(Map<String, dynamic> json) {
    bookCarDto = json['bookCarDto'] != null
        ? new LstBookCarDto.fromJson(json['bookCarDto'])
        : null;
    if (json['lstPersonTogether'] != null) {
      lstPersonTogether = new List<UserLogin>();
      json['lstPersonTogether'].forEach((v) {
        lstPersonTogether.add(new UserLogin.fromJson(v));
      });
    }
    sysUserRequest = json['sysUserRequest'] != null
        ? new SysUserRequest.fromJson(json['sysUserRequest'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.bookCarDto != null) {
      data['bookCarDto'] = this.bookCarDto.toJson();
    }
    if (this.lstPersonTogether != null) {
      data['lstPersonTogether'] =
          this.lstPersonTogether.map((v) => v.toJson()).toList();
    }
    if (this.sysUserRequest != null) {
      data['sysUserRequest'] = this.sysUserRequest.toJson();
    }
    return data;
  }
}
