

import 'package:trackcarvcc/models/models.dart';

class ListCarRequest {
  LstBookCarDto bookCarDto;

  ListCarRequest({this.bookCarDto});

  ListCarRequest.fromJson(Map<String, dynamic> json) {
    bookCarDto = json['bookCarDto'] != null
        ? new LstBookCarDto.fromJson(json['bookCarDto'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.bookCarDto != null) {
      data['bookCarDto'] = this.bookCarDto.toJson();
    }
    return data;
  }
}


