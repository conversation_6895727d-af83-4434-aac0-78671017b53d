class UserManagerRequest {
  int departmentId;
  int sysGroupId;

  UserManagerRequest({this.departmentId, this.sysGroupId});

  UserManagerRequest.fromJson(Map<String, dynamic> json) {
    departmentId = json['departmentId'];
    sysGroupId = json['sysGroupId'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['departmentId'] = this.departmentId;
    data['sysGroupId'] = this.sysGroupId;
    return data;
  }
}
