import '../base/authentication_info.dart';

class LoginRequest {
  AuthenticationInfo authenticationInfo;

  LoginRequest({this.authenticationInfo});

  LoginRequest.fromJson(Map<String, dynamic> json) {
    authenticationInfo = json['authenticationInfo'] != null
        ? new AuthenticationInfo.fromJson(json['authenticationInfo'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.authenticationInfo != null) {
      data['authenticationInfo'] = this.authenticationInfo.toJson();
    }
    return data;
  }
}
