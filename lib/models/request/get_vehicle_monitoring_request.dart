import 'package:trackcarvcc/models/base/sys_user_request.dart';

class GetVehicleMonitoringRequest {
  BookCarDtoObj bookCarDto;
  SysUserRequest sysUserRequest;

  GetVehicleMonitoringRequest({this.bookCarDto, this.sysUserRequest});

  GetVehicleMonitoringRequest.fromJson(Map<String, dynamic> json) {
    bookCarDto = json['bookCarDto'] != null
        ? new BookCarDtoObj.fromJson(json['bookCarDto'])
        : null;
    sysUserRequest = json['sysUserRequest'] != null
        ? new SysUserRequest.fromJson(json['sysUserRequest'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.bookCarDto != null) {
      data['bookCarDto'] = this.bookCarDto.toJson();
    }
    if (this.sysUserRequest != null) {
      data['sysUserRequest'] = this.sysUserRequest.toJson();
    }
    return data;
  }
}

class BookCarDtoObj {
  String licenseCar;
  List<String> listStatus;
  int sysGroupId;

  BookCarDtoObj({this.licenseCar, this.listStatus, this.sysGroupId});

  BookCarDtoObj.fromJson(Map<String, dynamic> json) {
    licenseCar = json['licenseCar'];
    listStatus = json['listStatus'].cast<String>();
    sysGroupId = json['sysGroupId'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['licenseCar'] = this.licenseCar;
    data['listStatus'] = this.listStatus;
    data['sysGroupId'] = this.sysGroupId;
    return data;
  }
}