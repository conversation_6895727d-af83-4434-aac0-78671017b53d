import 'package:trackcarvcc/models/base/authentication_info.dart';

class UpdateCarLocationRequest {
  AuthenticationInfo authenticationInfo;
  String loginName;
  String longitude;
  String latitude;

  UpdateCarLocationRequest(
      {this.authenticationInfo, this.loginName, this.longitude, this.latitude});

  UpdateCarLocationRequest.fromJson(Map<String, dynamic> json) {
    authenticationInfo = json['authenticationInfo'] != null
        ? new AuthenticationInfo.fromJson(json['authenticationInfo'])
        : null;
    loginName = json['loginName'];
    longitude = json['longitude'];
    latitude = json['latitude'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.authenticationInfo != null) {
      data['authenticationInfo'] = this.authenticationInfo.toJson();
    }
    data['loginName'] = this.loginName;
    data['longitude'] = this.longitude;
    data['latitude'] = this.latitude;
    return data;
  }
}
