import 'package:trackcarvcc/models/models.dart';

class CloseBookCarRequest {
  LstBookCarDto bookCarDto;
  SysUserRequest sysUserRequest;

  CloseBookCarRequest({this.bookCarDto, this.sysUserRequest});

  CloseBookCarRequest.fromJson(Map<String, dynamic> json) {
    bookCarDto = json['bookCarDto'] != null
        ? new LstBookCarDto.fromJson(json['bookCarDto'])
        : null;
    sysUserRequest = json['sysUserRequest'] != null
        ? new SysUserRequest.fromJson(json['sysUserRequest'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.bookCarDto != null) {
      data['bookCarDto'] = this.bookCarDto.toJson();
    }
    if (this.sysUserRequest != null) {
      data['sysUserRequest'] = this.sysUserRequest.toJson();
    }
    return data;
  }
}
