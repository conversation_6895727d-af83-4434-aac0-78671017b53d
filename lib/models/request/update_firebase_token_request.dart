import 'package:trackcarvcc/models/base/authentication_info.dart';

class UpdateFirebaseTokenRequest {
  AuthenticationInfo authenticationInfo;
  int sysUserId;
  String token;

  UpdateFirebaseTokenRequest(
      {this.authenticationInfo, this.sysUserId, this.token});

  UpdateFirebaseTokenRequest.fromJson(Map<String, dynamic> json) {
    authenticationInfo = json['authenticationInfo'] != null
        ? new AuthenticationInfo.fromJson(json['authenticationInfo'])
        : null;
    sysUserId = json['sysUserId'];
    token = json['token'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.authenticationInfo != null) {
      data['authenticationInfo'] = this.authenticationInfo.toJson();
    }
    data['sysUserId'] = this.sysUserId;
    data['token'] = this.token;
    return data;
  }
}