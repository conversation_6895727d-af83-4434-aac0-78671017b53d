import '../base/authentication_info.dart';

class GetListBookCarRequest {
  GetListBookCarRequest({
    this.authenticationInfo,
    this.sysUserId,
    this.typeMenu,
    this.email,
    this.loginName,
  });

  AuthenticationInfo authenticationInfo;
  int sysUserId;
  int typeMenu;
  String email;
  String loginName;

  factory GetListBookCarRequest.fromJson(Map<String, dynamic> json) =>
      GetListBookCarRequest(
        authenticationInfo: json["authenticationInfo"] == null
            ? null
            : AuthenticationInfo.fromJson(json["authenticationInfo"]),
        sysUserId: json["sysUserId"] == null ? null : json["sysUserId"],
        typeMenu: json["typeMenu"] == null ? null : json["typeMenu"],
        email: json["email"] == null ? null : json["email"],
        loginName: json["loginName"] == null ? null : json["loginName"],
      );

  Map<String, dynamic> toJson() => {
        "authenticationInfo":
            authenticationInfo == null ? null : authenticationInfo.toJson(),
        "sysUserId": sysUserId == null ? null : sysUserId,
        "typeMenu": typeMenu == null ? null : typeMenu,
        "email": email == null ? null : email,
        "loginName": loginName == null ? null : loginName,
      };
}
