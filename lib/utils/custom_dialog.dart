import 'package:flutter/material.dart';
import 'package:get/get.dart';

void showCustomDialog({String title, String content, Function() onCancel, Function() onConfirm}) {
  Get.dialog(
    AlertDialog(
      title: Text(
        title,
        style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
      ),
      content: Text(content),
      actions: <Widget>[
        FlatButton(
          minWidth: 80,
          onPressed: onCancel,
          textColor: Colors.red,
          child: Text('Hủy'),
        ),
        FlatButton(
          minWidth: 120,
          height: 40,
          onPressed: onConfirm,
          color: Colors.red,
          textColor: Colors.white,
          child: Text('Xác nhận'),
        ),
      ],
    ),
  );
}
