import 'dart:io';

import 'package:get/get.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:trackcarvcc/utils/custom_dialog.dart';

class DownloadUtil {
  static Future<String> getPath() async {
    try {
      bool dirDownloadExists = true;
      var directory;

      if (Platform.isIOS) {
        directory = await getApplicationDocumentsDirectory();
      } else {
        directory = Directory('/storage/emulated/0/Documents');
        // Put file in global download folder, if for an unknown reason it didn't exist, we fallback
        // ignore: avoid_slow_async_io
        if (!await directory.exists())
          directory = await getExternalStorageDirectory();
      }

      return directory?.path;
    } catch (e) {
      print(e);
      return '';
    }
  }

  // static Future<void> checkPermission() async {
  //   var status = await PermissionHandler().checkPermissionStatus(PermissionGroup.storage);
  //   if (status != PermissionStatus.granted) {
  //     await PermissionHandler().requestPermissions([PermissionGroup.storage]);
  //   }
  // }

  static Future<Map<Permission, PermissionStatus>> permissionServicesImage() async {
    Map<Permission, PermissionStatus> statuses = await [
      Permission.storage,
      Permission.camera,
    ].request();

    if (statuses[Permission.storage].isPermanentlyDenied || statuses[Permission.camera].isPermanentlyDenied) {
      showCustomDialog(
          title: "Cảnh báo",
          content: "Bạn chưa cấp quyền truy cập máy ảnh và tệp cho ứng dụng. Mở cài đặt cấp quyền để sử dụng được tất cả các chức năng.",
          onCancel: (){
            Get.back();
          },
          onConfirm: (){
            Get.back();
            openAppSettings();
          }
      );
    } else {
      if (statuses[Permission.storage].isDenied || statuses[Permission.camera].isDenied) {
        permissionServiceCall();
      }
    }
    return statuses;
  }

  static permissionServiceCall() async {
    await permissionServicesImage().then(
          (value) {
        if (value != null) {
          if (value[Permission.storage].isGranted &&
              value[Permission.camera].isGranted) {
          }
        }
      },
    );
  }
}
