///URL API
const String BASE_URL = "https://qlotomobile.congtrinhviettel.com.vn/oto-service/service/";
const String BASE_URL_IMAGE = "https://qlotomobile.congtrinhviettel.com.vn/static";

// const String BASE_URL = "http://************:8866/oto-service/service/";
// const String BASE_URL = "http://**************:8080/oto-service/service/";
// const String BASE_URL_IMAGE = "http://*************:8084/static";
// const String BASE_URL = "http://*************:8666/oto-service/service/";
// const String BASE_URL_IMAGE = "http://*************:8666/static";

const String GOOGLE_API = "https://maps.googleapis.com/maps/api/distancematrix/json";
const String GOOGLE_API_ADDRESS = "https://maps.google.com/maps/api/geocode/json";

const String AUTH_LOGIN = "SysUserRestService/service/auth";
const String LIST_BOOK_CAR = "BookCarRestService/service/getListBookCar";

// url address
const String LIST_CITY = "BookCarRestService/service/getDataProvinceCity";
const String LIST_DISTRICT = "BookCarRestService/service/getDataDistrict";
const String LIST_WARD = "BookCarRestService/service/getDataWard";
const String LIST_USER_BY_ROLE = "BookCarRestService/service/truck/getApprovePersons";
const String LIST_CAR_TYPE = "BookCarRestService/service/getListTypeCar";
const String LIST_USER_GO_WITH = "BookCarRestService/service/getListUser";
const String LIST_USER_MANAGER = "BookCarRestService/service/getListManager";
const String LIST_CAR = "BookCarRestService/service/getListCar";
const String LIST_DRIVER = "BookCarRestService/service/getListDriverCar";
const String LIST_ADD_ORDER_CAR = "BookCarRestService/service/addBookCar";
const String LIST_UPDATE_ORDER_CAR = "BookCarRestService/service/updateBookCar";
const String LIST_USER_TOGETHER = "BookCarRestService/service/getListUserTogether";
const String LIST_ATTACHMENT = "BookCarRestService/service/getAttachmentList";
const String MANAGE_APPROVE_REJECT_BOOK_CAR = "BookCarRestService/service/manageApproveRejectBookCar";
const String CAPTAIN_CAR_APPROVE_REJECT_BOOK_CAR = "BookCarRestService/service/captainCarApproveRejectBookCar";
const String MANAGER_CAR_APPROVE_REJECT_BOOK_CAR = "BookCarRestService/service/managerCarApproveRejectBookCar";
const String DRIVER_BOARD_APPROVE_REJECT_BOOK_CAR = "BookCarRestService/service/driverBoardApproveRejectBookCar";
const String ADMINISTRATIVE_CAR_APPROVE_REJECT_BOOK_CAR =
    "BookCarRestService/service/administrativeApproveRejectBookCar";
const String VICE_MANAGER_CAR_APPROVE_REJECT_BOOK_CAR = "BookCarRestService/service/viceManagerApproveRejectBookCar";
const String TTHTPQLTS_APPROVE_REJECT_BOOK_CAR = "BookCarRestService/service/tthtPqltsApproveRejectBookCar";
const String CLOSE_DRIVER_BOOK_CAR = "BookCarRestService/service/closeDriverBookCar";
const String CLOSE_BOOK_CAR = "BookCarRestService/service/closeBookCar";
const String CLOSE_MANAGER_BOOK_CAR = "BookCarRestService/service/closeManagerBookCar";
const String OPEN_COMMAND = "BookCarRestService/service/extendBookCar";
const String LIST_UNIT = "BookCarRestService/service/getBranch";
const String LIST_CAR_BY_UNIT = "BookCarRestService/service/searchCatVehicle";
const String LIST_CAR_HISTORY = "BookCarRestService/service/getHistoryCar";
const String RATE_BOOK_CAR = "BookCarRestService/service/rateBookCar";
const String VEHICLE_MONITORING = "BookCarRestService/service/getVehicleMonitoring";
const String GET_HISTORY_DETAIL_CAR = "BookCarRestService/service/getHistoryDetailCar";
const String UPDATE_CAR_LOCATION = "BookCarRestService/service/updateLocation";
const String UPDATE_FIREBASE_TOKEN = "BookCarRestService/service/updateTokenUser";
const String GET_APP_VERSION = "appVersionWsRsService/service/getAppVersion";
const String GET_LIST_ENGINEERING = "TechnicalDateRestService/service/getListTechnicalDate";
const String GET_LIST_LICENSE_CAR = "TechnicalDateRestService/service/getListLicenseCar";
const String CREATE_ENGINEERING = "TechnicalDateRestService/service/saveTechnicalDate";
const String UPDATE_STATUS_ENGINEERING = "TechnicalDateRestService/service/updateStatusTechnicalDate";
const String UPDATE_ENGINEERING_DETAIL = "TechnicalDateRestService/service/getListTechnicalDateDetail";
const String EDIT_ENGINEERING_DETAIL = "TechnicalDateRestService/service/editTechnicalDate";
const String GEN_CODE_PARTNER = "BookCarPartnerWmRsService/service/genCode";
const String GET_TO_ADDRESS = "BookCarPartnerWmRsService/service/getToAddress";
const String GET_FROM_ADDRESS = "BookCarPartnerWmRsService/service/getFromAddress";
const String GET_DESCRIPTION_WORK = "BookCarPartnerWmRsService/service/getDescriptionWork";
const String GET_TYPE_BOOK_CAR_PARTNER = "BookCarPartnerWmRsService/service/getTypeBookCarPartner";
const String GET_LICENSE_CAR_PARTNER = "BookCarPartnerWmRsService/service/getLicenseCarPartner";
const String GET_COST_ESTIMATE = "BookCarPartnerWmRsService/service/getCostEstimate";
const String ADD_BOOK_CAR_PARTNER = "BookCarPartnerWmRsService/service/addBookCarPartner";
const String GET_LIST_BOOK_CAR_PARTNER = "BookCarPartnerWmRsService/service/getListBookCarPartner";
const String GET_PERMISSION_BOOK_CAR_PARTNER = "BookCarPartnerWmRsService/service/getCheckPermission";
const String GET_DETAIL_CRAVE_CAR_PARTNER = "BookCarPartnerWmRsService/service/getListBookCarPartnerDetail";
const String UPDATE_BOOK_CAR_PARTNER = "BookCarPartnerWmRsService/service/updateBookCarPartner";
const String UPDATE_STATUS_BOOK_CAR_PARTNER = "BookCarPartnerWmRsService/service/updateStatusBookCarPartner";
const String APPROVE_REJECT_GDCNKT = "BookCarPartnerWmRsService/service/appProveRejectGDCNKT";
const String APPROVE_REJECT_QLTS = "BookCarPartnerWmRsService/service/appProveRejectQLTS";
const String GET_LIST_DRIVER_CAR = "BookCarPartnerWmRsService/service/getListDriverCar";
const String GET_LIST_PRODUCT = "BookCarPartnerWmRsService/service/getGoods";
const String GET_LIST_EXPLANATION = "explanationExcessWsService/service/explanation/getList";
const String EXPLANATION_UPDATE_STATUS = "explanationExcessWsService/service/explanation/updateStatus";
const String POST_ATTACHED_FILE = "BookCarRestService/service/uploadATTTInput";
String DOWNLOAD_ATTACHED_FILE(String filePath) => "BookCarRestService/service/downloadFileATTT?$filePath";
