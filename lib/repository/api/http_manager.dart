import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:trackcarvcc/helpers/logger.dart';
import 'package:trackcarvcc/models/base/base_api_response.dart';
import 'package:trackcarvcc/repository/api/urls.dart';
import '../../constants/constants.dart';

Map<String, dynamic> dioErrorHandle(DioError error) {
  UtilLogger.log("ERROR", error);

  switch (error.type) {
    case DioErrorType.RESPONSE:
      return error.response?.data;
    case DioErrorType.SEND_TIMEOUT:
    case DioErrorType.RECEIVE_TIMEOUT:
      return {"success": false, "code": "request_time_out"};

    case DioErrorType.DEFAULT:
      return {"success": false, "code": "default error"};

    default:
      return {"success": false, "code": "connect_to_server_fail"};
  }
}

class HTTPManager {
  final Dio _dio;

  HTTPManager(this._dio) {
    _initApiClient();
  }

  void _initApiClient() {
    _dio..options = exportOption(baseOptions);
  }

  BaseOptions baseOptions = BaseOptions(
    baseUrl: BASE_URL,
    connectTimeout: 100000,
    receiveTimeout: 100000,
    contentType: Headers.jsonContentType,
    responseType: ResponseType.json,
  );

  ///Setup Option
  BaseOptions exportOption(BaseOptions options) {
    /*Map<String, dynamic> header = {
      "Device-Id": Application.device?.uuid,
      "Device-Name": utf8.encode(Application.device?.name ?? ''),
      "Device-Model": Application.device?.model,
      "Device-Version": Application.device?.version,
      "Push-Token": Application.device?.token,
      "Type": Application.device?.type,
      "Lang": AppLanguage.defaultLanguage?.languageCode
    };
    options.headers.addAll(header);
    if (Application.user?.token != null) {
      options.headers["Authorization"] = "Bearer ${Application.user.token}";
    }
    UtilLogger.log("headers", options.headers);*/
    return options;
  }

  ///Post method
  Future<dynamic> post({
    String url,
    Map<String, dynamic> data,
    Options options,
  }) async {
    UtilLogger.log("POST URL", BASE_URL + url);
    UtilLogger.log("DATA", JsonEncoder().convert(data));
    try {
      final response = await _dio.post(
        url,
        data: data,
        options: options,
      );
      // if fail
      if (response.statusCode != successful) {
        return BaseApiResponse(
            code: response.statusCode,
            data: null,
            message: '',
            success: false)
            .toJson();
      }
      // success
      UtilLogger.log("POST RESPONSE", JsonEncoder().convert(response.data));
      return BaseApiResponse(
          code: successful,
          data: response.data,
          message: response.statusMessage,
          success: true)
          .toJson();
    } on DioError catch (error) {
      return dioErrorHandle(error);
    }
  }

  ///Get method
  Future<dynamic> get({
    String url,
    Map<String, dynamic> params,
    Options options,
  }) async {
    UtilLogger.log("GET URL", BASE_URL + url);
    UtilLogger.log("PARAMS", params);
    try {
      final response = await _dio.get(
        url,
        queryParameters: params,
        options: options,
      );
      // if fail
      if (response.statusCode != successful) {
        return BaseApiResponse(
            code: response.statusCode,
            data: null,
            message: '',
            success: false)
            .toJson();
      }
      // success
      UtilLogger.log("GET RESPONSE", JsonEncoder().convert(response.data));
      return BaseApiResponse(
          code: successful,
          data: response.data,
          message: response.statusMessage,
          success: true)
          .toJson();
    } on DioError catch (error) {
      return dioErrorHandle(error);
    }
  }

  ///Postfile
  Future<dynamic> postFile({
    String url,
    dynamic data,
    Options options,
  }) async {
    UtilLogger.log("POST URL", BASE_URL + url);
    try {
      final response = await _dio.post(
        url,
        data: data,
        options: options,
      );
      // if fail
      if (response.statusCode != successful) {
        return BaseApiResponse(
            code: response.statusCode,
            data: null,
            message: '',
            success: false)
            .toJson();
      }
      // success
      UtilLogger.log("POST RESPONSE", JsonEncoder().convert(response.data));
      return BaseApiResponse(
          code: successful,
          data: response.data,
          message: response.statusMessage,
          success: true)
          .toJson();
    } on DioError catch (error) {
      return dioErrorHandle(error);
    }
  }
}
