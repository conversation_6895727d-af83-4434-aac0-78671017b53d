import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:trackcarvcc/constants/http_code.dart';
import 'package:trackcarvcc/helpers/logger.dart';
import 'package:trackcarvcc/models/models.dart';
import 'package:trackcarvcc/models/request/close_book_car_request.dart';
import 'package:trackcarvcc/models/request/create_order_car_request.dart';
import 'package:trackcarvcc/models/request/get_history_car_detail_request.dart';
import 'package:trackcarvcc/models/request/get_vehicle_monitoring_request.dart';
import 'package:trackcarvcc/models/request/list_car_by_unit_request.dart';
import 'package:trackcarvcc/models/request/list_car_history_request.dart';
import 'package:trackcarvcc/models/request/list_car_request.dart';
import 'package:trackcarvcc/models/request/list_driver_request.dart';
import 'package:trackcarvcc/models/request/list_quan_huyen_request.dart';
import 'package:trackcarvcc/models/request/list_user_together_request.dart';
import 'package:trackcarvcc/models/request/update_car_location_request.dart';
import 'package:trackcarvcc/models/request/update_firebase_token_request.dart';
import 'package:trackcarvcc/models/request/user_go_with_request.dart';
import 'package:trackcarvcc/models/request/user_manager_request.dart';
import 'package:trackcarvcc/models/request/user_role_request.dart';
import 'package:trackcarvcc/repository/api/http_manager.dart';
import 'package:trackcarvcc/repository/api/urls.dart';
import 'package:trackcarvcc/repository/preferences/data_center.dart';
import 'package:trackcarvcc/ui/crave_car/model/crave_car_response.dart';
import 'package:trackcarvcc/ui/engineering_date/model/engineering_date_request.dart';

class Api {
  HTTPManager httpManager;

  Api(this.httpManager);

  ///Login api
  Future<dynamic> login(LoginRequest request) async {
    return httpManager.post(url: AUTH_LOGIN, data: request.toJson());
  }

  /// get list book car
  Future<dynamic> getListBookCar(GetListBookCarRequest request) async {
    return httpManager.post(url: LIST_BOOK_CAR, data: request.toJson());
  }

  /// get list city
  Future<dynamic> getListCity() async {
    return httpManager.post(url: LIST_CITY);
  }

  /// get list quan huyen
  Future<dynamic> getListQuanHuyen(ListQuanHuyenRequest request) async {
    return httpManager.post(url: LIST_DISTRICT, data: request.toJson());
  }

  /// get list quan huyen
  Future<dynamic> getListXaPhuong(ListQuanHuyenRequest request) async {
    return httpManager.post(url: LIST_WARD, data: request.toJson());
  }

  /// get list user by role
  Future<dynamic> getListUserByRole(UserRoleRequest request) async {
    return httpManager.post(url: LIST_USER_BY_ROLE, data: request.toJson());
  }

  /// get list car
  Future<dynamic> getListCarType() async {
    return httpManager.post(url: LIST_CAR_TYPE);
  }

  /// get list user go with
  Future<dynamic> getListUserGoWith(UserGoWithRequest request) async {
    return httpManager.post(url: LIST_USER_GO_WITH, data: request.toJson());
  }

  /// get list user manager
  Future<dynamic> getListUserMananger(UserManagerRequest request) async {
    return httpManager.post(url: LIST_USER_MANAGER, data: request.toJson());
  }

  /// get list car
  Future<dynamic> getListCar(ListCarRequest request) async {
    return httpManager.post(url: LIST_CAR, data: request.toJson());
  }

  /// get list driver
  Future<dynamic> getListCarDriver(ListDriverRequest request) async {
    return httpManager.post(url: LIST_DRIVER, data: request.toJson());
  }

  /// add order car creation
  Future<dynamic> createOrderCarCreation(CreateOrderCarRequest request) async {
    request.bookCarDto.code = null;
    return httpManager.post(url: LIST_ADD_ORDER_CAR, data: request.toJson());
  }

  /// update order car creation
  Future<dynamic> updateOrderCarCreation(CreateOrderCarRequest request) async {
    return httpManager.post(url: LIST_UPDATE_ORDER_CAR, data: request.toJson());
  }

  /// get expect distance
  Future<dynamic> getExpectDistance(String origin, String destination) async {
    BaseOptions baseOptions = BaseOptions(
      baseUrl: GOOGLE_API,
      connectTimeout: 100000,
      receiveTimeout: 100000,
      contentType: Headers.jsonContentType,
      responseType: ResponseType.json,
    );
    httpManager.exportOption(baseOptions);
    return httpManager.get(url: GOOGLE_API, params: {
      "units": "imperial",
      "origins": origin,
      "destinations": destination,
      "key": "AIzaSyArTdhzQmN1HTzNumYvNaKd_foe0MMRgqQ"
    });
  }

  Future<dynamic> vtMapDistanceMatrix(Map<String, dynamic> params) async {
    try {
      Dio dio = Dio(
        BaseOptions(
          baseUrl: 'https://api-maps.viettel.vn',
          connectTimeout: 100000,
          receiveTimeout: 100000,
          contentType: Headers.jsonContentType,
          responseType: ResponseType.json,
        ),
      );
      final response = await dio.get(
        '/gateway/routing/v2/distancematrix',
        queryParameters: params,
      );
      // if fail
      if (response.statusCode != successful) {
        return BaseApiResponse(
            code: response.statusCode,
            data: null,
            message: '',
            success: false)
            .toJson();
      }
      // success
      UtilLogger.log("POST RESPONSE", JsonEncoder().convert(response.data));
      return BaseApiResponse(
          code: successful,
          data: response.data,
          message: response.statusMessage,
          success: true)
          .toJson();
    } catch (e) {
      UtilLogger.log("vtMapDistanceMatrix error", e.toString());
      return Future.error(e);
    }
  }

  Future<dynamic> getGoogleFromAddress(String address) async {
    BaseOptions baseOptions = BaseOptions(
      baseUrl: GOOGLE_API,
      connectTimeout: 100000,
      receiveTimeout: 100000,
      contentType: Headers.jsonContentType,
      responseType: ResponseType.json,
    );
    httpManager.exportOption(baseOptions);
    return httpManager.get(url: GOOGLE_API_ADDRESS, params: {
      "address": address,
      "key": "AIzaSyArTdhzQmN1HTzNumYvNaKd_foe0MMRgqQ"
    });
  }

  /// get list user go together
  Future<dynamic> getListUserGoTogether(ListUserTogetherRequest request) async {
    return httpManager.post(url: LIST_USER_TOGETHER, data: request.toJson());
  }

  Future<dynamic> getListAttachment(int bookCarId) async {
    return httpManager.post(url: LIST_ATTACHMENT, data: {"bookCarId" : bookCarId});
  }

  Future<dynamic> manageApproveRejectBookCar(ListUserTogetherRequest request) async {
    return httpManager.post(url: MANAGE_APPROVE_REJECT_BOOK_CAR, data: request.toJson());
  }

  Future<dynamic> captainCarApproveRejectBookCar(ListUserTogetherRequest request) async {
    return httpManager.post(url: CAPTAIN_CAR_APPROVE_REJECT_BOOK_CAR, data: request.toJson());
  }

  Future<dynamic> managerCarApproveRejectBookCar(ListUserTogetherRequest request) async {
    return httpManager.post(url: MANAGER_CAR_APPROVE_REJECT_BOOK_CAR, data: request.toJson());
  }

  Future<dynamic> driverBoardApproveRejectBookCar(ListUserTogetherRequest request) async {
    return httpManager.post(url: DRIVER_BOARD_APPROVE_REJECT_BOOK_CAR, data: request.toJson());
  }

  Future<dynamic> administrativeApproveRejectBookCar(ListUserTogetherRequest request) async {
    return httpManager.post(url: ADMINISTRATIVE_CAR_APPROVE_REJECT_BOOK_CAR, data: request.toJson());
  }

  Future<dynamic> viceManagerApproveRejectBookCar(ListUserTogetherRequest request) async {
    return httpManager.post(url: VICE_MANAGER_CAR_APPROVE_REJECT_BOOK_CAR, data: request.toJson());
  }

  Future<dynamic> tthtPqltsApproveRejectBookCar(ListUserTogetherRequest request) async {
    return httpManager.post(url: TTHTPQLTS_APPROVE_REJECT_BOOK_CAR, data: request.toJson());
  }

  Future<dynamic> closeBookCar(CloseBookCarRequest request) async {
    return httpManager.post(url: CLOSE_BOOK_CAR, data: request.toJson());
  }

  Future<dynamic> closeManagerBookCar(CloseBookCarRequest request) async {
    return httpManager.post(url: CLOSE_MANAGER_BOOK_CAR, data: request.toJson());
  }

  Future<dynamic> closeDriverBookCar(CloseBookCarRequest request) async {
    return httpManager.post(url: CLOSE_DRIVER_BOOK_CAR, data: request.toJson());
  }

  Future<dynamic> openCommand(CreateOrderCarRequest request) async {
    return httpManager.post(url: OPEN_COMMAND, data: request.toJson());
  }

  /// get list unit
  Future<dynamic> getListUnit() async {
    return httpManager.post(url: LIST_UNIT, data: {"bookCarDto": {}});
  }

  /// get list car by nut
  Future<dynamic> getListCarByUnit(ListCarByUnitRequest request) async {
    return httpManager.post(url: LIST_CAR_BY_UNIT, data: request.toJson());
  }

  /// search list car order history
  Future<dynamic> getListCarHistory(ListCarHistoryRequest request) async {
    return httpManager.post(url: LIST_CAR_HISTORY, data: request.toJson());
  }

  Future<dynamic> rateBookCar(CreateOrderCarRequest request) async {
    return httpManager.post(url: RATE_BOOK_CAR, data: request.toJson());
  }

  Future<dynamic> getVehicleMonitoring(GetVehicleMonitoringRequest request) async {
    return httpManager.post(url: VEHICLE_MONITORING, data: request.toJson());
  }

  Future<dynamic> getHistoryDetailCar(GetHistoryCarDetailRequest request) async {
    return httpManager.post(url: GET_HISTORY_DETAIL_CAR, data: request.toJson());
  }

  Future<dynamic> updateCarLocation(UpdateCarLocationRequest request) async {
    return httpManager.post(url: UPDATE_CAR_LOCATION, data: request.toJson());
  }

  Future<dynamic> updateFirebaseToken(UpdateFirebaseTokenRequest request) async {
    return httpManager.post(url: UPDATE_FIREBASE_TOKEN, data: request.toJson());
  }

  Future<dynamic> getAppVersion() async {
    return httpManager.post(url: GET_APP_VERSION);
  }

  Future<dynamic> getListEngineeringDate() async {
    return httpManager.post(url: GET_LIST_ENGINEERING, data: {
      "sysUserId": DataCenter.shared().getUserInfo().sysUserId,
      "employeeCode": DataCenter.shared().getUserInfo().employeeCode
    });
  }

  Future<dynamic> getListLicenseCar() async {
    return httpManager
        .post(url: GET_LIST_LICENSE_CAR, data: {"sysUserId": DataCenter.shared().getUserInfo().sysUserId});
  }

  Future<dynamic> createEngineeringDate(EngineeringDateRequest request) async {
    return httpManager.post(url: CREATE_ENGINEERING, data: request.toJson());
  }

  Future<dynamic> updateStatusEngineeringDate(EngineeringDateRequest request) async {
    return httpManager.post(url: UPDATE_STATUS_ENGINEERING, data: request.toJson());
  }

  Future<dynamic> getEngineeringDateDetail(int technicalDateId) async {
    return httpManager.post(url: UPDATE_ENGINEERING_DETAIL, data: {"technicalDateId": technicalDateId});
  }

  Future<dynamic> editEngineeringDate(EngineeringDateRequest request) async {
    return httpManager.post(url: EDIT_ENGINEERING_DETAIL, data: request.toJson());
  }

  Future<dynamic> genCode(int sysUserId, int sysGroupId) async {
    return httpManager.post(url: GEN_CODE_PARTNER, data: {"sysUserId": sysUserId, "sysGroupId": sysGroupId});
  }

  Future<dynamic> getToAddress(String toAddress) async {
    return httpManager.post(url: GET_TO_ADDRESS, data: {"toAddress": toAddress});
  }

  Future<dynamic> getFromAddress(String fromAddress) async {
    return httpManager.post(url: GET_FROM_ADDRESS, data: {"fromAddress": fromAddress});
  }

  Future<dynamic> getDescriptionWork() async {
    return httpManager.post(url: GET_DESCRIPTION_WORK, data: {});
  }

  Future<dynamic> getTypeBookCarPartner() async {
    return httpManager.post(url: GET_TYPE_BOOK_CAR_PARTNER, data: {});
  }

  Future<dynamic> getLicenseCarPartner(String licenseCarPartner) async {
    return httpManager.post(url: GET_LICENSE_CAR_PARTNER, data: {"licenseCarPartner": licenseCarPartner});
  }

  Future<dynamic> getCostEstimate(CraveCarRequest request) async {
    return httpManager.post(url: GET_COST_ESTIMATE, data: request.toJson());
  }

  Future<dynamic> addBookCarPartner(CraveCarRequest request) async {
    return httpManager.post(url: ADD_BOOK_CAR_PARTNER, data: request.toJson());
  }

  Future<dynamic> getListBookCarPartner(CraveCarRequest request) async {
    return httpManager.post(url: GET_LIST_BOOK_CAR_PARTNER, data: request.toJson());
  }

  Future<dynamic> explanationGetList(Map<String, dynamic> request) async {
    return httpManager.post(url: GET_LIST_EXPLANATION, data: request);
  }

  Future<dynamic> explanationUpdateStatus(Map<String, dynamic> request) async {
    return httpManager.post(url: EXPLANATION_UPDATE_STATUS, data: request);
  }

  Future<dynamic> getCheckPermission(CraveCarRequest request) async {
    return httpManager.post(url: GET_PERMISSION_BOOK_CAR_PARTNER, data: request.toJson());
  }

  Future<dynamic> getListBookCarPartnerDetail(int id) async {
    return httpManager.post(url: GET_DETAIL_CRAVE_CAR_PARTNER, data: {"bookCarPartnerId": id});
  }

  Future<dynamic> updateBookCarPartner(CraveCarRequest request) async {
    return httpManager.post(url: UPDATE_BOOK_CAR_PARTNER, data: request.toJson());
  }

  Future<dynamic> updateStatusBookCarPartner(CraveCarRequest request) async {
    return httpManager.post(url: UPDATE_STATUS_BOOK_CAR_PARTNER, data: request.toJson());
  }

  Future<dynamic> appProveRejectGDCNKT(CraveCarRequest request) async {
    return httpManager.post(url: APPROVE_REJECT_GDCNKT, data: request.toJson());
  }

  Future<dynamic> appProveRejectQLTS(CraveCarRequest request) async {
    return httpManager.post(url: APPROVE_REJECT_QLTS, data: request.toJson());
  }

  Future<dynamic> getListDriverCar(String keySearch) async {
    return httpManager.post(url: GET_LIST_DRIVER_CAR, data: {"keySearch": keySearch});
  }

  Future<dynamic> getGoods(String keySearch) async {
    return httpManager.post(url: GET_LIST_PRODUCT, data: {"keySearch": keySearch});
  }

  Future<dynamic> postAttachFile(File file) async {
    final formData = FormData.fromMap({
      "file": await MultipartFile.fromFile(file.path, filename: file.path.split('/').last),
    });
    return httpManager.postFile(url: POST_ATTACHED_FILE, data: formData);
  }

  Future<dynamic> downloadAttachFile(String filePath) async {
    return httpManager.get(url: DOWNLOAD_ATTACHED_FILE(filePath));
  }
}
