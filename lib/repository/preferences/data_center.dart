import 'dart:convert';

import 'package:shared_preferences/shared_preferences.dart';
import 'package:trackcarvcc/models/models.dart';
import 'package:trackcarvcc/repository/preferences/pref_key.dart';

class DataCenter {
  DataCenter._create();

  static DataCenter _instance;

  static DataCenter shared() {
    _instance ??= DataCenter._create();
    return _instance;
  }

  SharedPreferences prefs;

  initDataCenter() async {
    prefs = await SharedPreferences.getInstance();
  }

  /// SAVE
  // Access token
  saveSession({String accessToken}) async {
    await prefs.setString(keyAccessToken, accessToken);
  }

  saveUserInfo(UserLogin user) async {
    await prefs.setString(keyUserInfo, jsonEncode(user));
  }

  savePassword(String password) async {
    await prefs.setString(keyPassword, password);
  }

  saveFirebaseToken(String firebaseToken) async {
    await prefs.setString(keyFirebaseToken, firebaseToken);
  }

  /// GET
  String getAccessToken() {
    return prefs.getString(keyAccessToken);
  }

  String getFirebaseToken() {
    return prefs.getString(keyFirebaseToken);
  }

  UserLogin getUserInfo() {
    if (prefs.getString(keyUserInfo) == null) return null;
    return UserLogin.fromJson(jsonDecode(prefs.getString(keyUserInfo)));
  }

  String getPassword() {
    return prefs.getString(keyPassword);
  }

  clearAllData() {
    prefs.clear();
  }
}
