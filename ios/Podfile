# Uncomment this line to define a global platform for your project
platform :ios, '12.0'

# Add Viettel Map source
source 'https://cdn.cocoapods.org/'
source "https://github.com/vtmaps/pod_control.git"

# CocoaPods analytics sends network stats synchronously affecting flutter build latency.
ENV['COCOAPODS_DISABLE_STATS'] = 'true'

project 'Runner', {
  'Debug' => :debug,
  'Profile' => :release,
  'Release' => :release,
}

def flutter_root
  generated_xcode_build_settings_path = File.expand_path(File.join('..', 'Flutter', 'Generated.xcconfig'), __FILE__)
  unless File.exist?(generated_xcode_build_settings_path)
    raise "#{generated_xcode_build_settings_path} must exist. If you're running pod install manually, make sure flutter pub get is executed first"
  end

  File.foreach(generated_xcode_build_settings_path) do |line|
    matches = line.match(/FLUTTER_ROOT\=(.*)/)
    return matches[1].strip if matches
  end
  raise "FLUTTER_ROOT not found in #{generated_xcode_build_settings_path}. Try deleting Generated.xcconfig, then run flutter pub get"
end

require File.expand_path(File.join('packages', 'flutter_tools', 'bin', 'podhelper'), flutter_root)

flutter_ios_podfile_setup

target 'Runner' do
  use_frameworks!
  use_modular_headers!

  flutter_install_all_ios_pods File.dirname(File.realpath(__FILE__))
  pod 'ViettelMapSDK', '~> 1.0.3'
end

post_install do |installer|
  # Đặt deployment target và sửa xcconfig
  installer.pods_project.targets.each do |target|
    flutter_additional_ios_build_settings(target)
    target.build_configurations.each do |config|
      config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '11.0'
      config.build_settings['ENABLE_BITCODE'] = 'NO'
      xcconfig_path = config.base_configuration_reference.real_path
      xcconfig = File.read(xcconfig_path)
      xcconfig_mod = xcconfig.gsub(/DT_TOOLCHAIN_DIR/, "TOOLCHAIN_DIR")
      File.open(xcconfig_path, "w") { |file| file << xcconfig_mod }
    end
  end

  bitcode_strip_path = `xcrun --find bitcode_strip`.strip

  def strip_bitcode_from_framework(bitcode_strip_path, framework_relative_path)
    framework_path = File.join(Dir.pwd, framework_relative_path)
    command = "#{bitcode_strip_path} #{framework_path} -r -o #{framework_path}"
    puts "Stripping bitcode: #{command}"
    system(command)
  end

  framework_paths = [
    # Mapbox Core frameworks
    "Pods/Mapbox-iOS-SDK/Mapbox.xcframework/ios-arm64/Mapbox.framework/Mapbox",
    "Pods/Mapbox-iOS-SDK/Mapbox.xcframework/ios-arm64_x86_64-simulator/Mapbox.framework/Mapbox",
    "Pods/MapboxNavigationNative/MapboxNavigationNative.xcframework/ios-arm64/MapboxNavigationNative.framework/MapboxNavigationNative",
    "Pods/MapboxNavigationNative/MapboxNavigationNative.xcframework/ios-arm64_x86_64-simulator/MapboxNavigationNative.framework/MapboxNavigationNative",

    # Mapbox Common frameworks
    "Pods/MapboxCommon/MapboxCommon.xcframework/ios-arm64/MapboxCommon.framework/MapboxCommon",
    "Pods/MapboxCommon/MapboxCommon.xcframework/ios-arm64_x86_64-maccatalyst/MapboxCommon.framework/MapboxCommon",
    "Pods/MapboxCommon/MapboxCommon.xcframework/ios-arm64_x86_64-simulator/MapboxCommon.framework/MapboxCommon",

    # Mapbox Core Maps frameworks
    "Pods/MapboxCoreMaps/MapboxCoreMaps.xcframework/ios-arm64/MapboxCoreMaps.framework/MapboxCoreMaps",
    "Pods/MapboxCoreMaps/MapboxCoreMaps.xcframework/ios-arm64_x86_64-maccatalyst/MapboxCoreMaps.framework/MapboxCoreMaps",
    "Pods/MapboxCoreMaps/MapboxCoreMaps.xcframework/ios-arm64_x86_64-simulator/MapboxCoreMaps.framework/MapboxCoreMaps",

    # Mapbox Mobile Events frameworks
    "Pods/MapboxMobileEvents/MapboxMobileEvents.xcframework/ios-arm64_armv7/MapboxMobileEvents.framework/MapboxMobileEvents",
    "Pods/MapboxMobileEvents/MapboxMobileEvents.xcframework/ios-ios-arm64_i386_x86_64-simulator/MapboxMobileEvents.framework/MapboxMobileEvents",
    "Pods/MapboxMobileEvents/MapboxMobileEvents.xcframework/ios-arm64_x86_64-maccatalyst/MapboxMobileEvents.framework/MapboxMobileEvents"
  ]

  # Strip từng framework
  framework_paths.each do |framework_relative_path|
    strip_bitcode_from_framework(bitcode_strip_path, framework_relative_path)
  end

  # Copy Privacy Manifest cho file_picker và các SDK khác
  puts "📋 Adding Privacy Manifest for third-party SDKs..."

  # Tạo privacy manifest cho file_picker
  file_picker_path = "Pods/file_picker/ios/Classes"
  if Dir.exist?(file_picker_path)
    privacy_manifest_content = <<~MANIFEST
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>NSPrivacyTracking</key>
    <false/>
    <key>NSPrivacyTrackingDomains</key>
    <array/>
    <key>NSPrivacyCollectedDataTypes</key>
    <array/>
    <key>NSPrivacyAccessedAPITypes</key>
    <array>
        <dict>
            <key>NSPrivacyAccessedAPIType</key>
            <string>NSPrivacyAccessedAPICategoryFileTimestamp</string>
            <key>NSPrivacyAccessedAPITypeReasons</key>
            <array>
                <string>C617.1</string>
            </array>
        </dict>
    </array>
</dict>
</plist>
    MANIFEST

    File.write("#{file_picker_path}/PrivacyInfo.xcprivacy", privacy_manifest_content)
    puts "✅ Added Privacy Manifest for file_picker"
  end
end
