<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>VCC Tracking</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>VCC Tracking</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsLocalNetworking</key>
		<true/>
	</dict>
	<key>NSAppleMusicUsageDescription</key>
	<string>This permission is not needed by the app, but it is required by an underlying API. If you see this dialog, contact us.</string>
	<key>NSCalendarsUsageDescription</key>
	<string>$(PRODUCT_NAME) need calendars access for create events feature &amp;amp; better user experience</string>
	<key>NSCameraUsageDescription</key>
	<string>Can I use the camera please?</string>
	<key>NSContactsUsageDescription</key>
	<string>$(PRODUCT_NAME) need contacts access for finding product &amp;amp; chatting between users</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>VCC Tracking cần truy cập vị trí của bạn để hiển thị trên bản đồ và theo dõi xe, lái xe xung quanh bạn</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>VCC Tracking cần truy cập vị trí của bạn để hiển thị trên bản đồ và theo dõi xe, lái xe xung quanh bạn</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>Can I use the mic please?</string>
	<key>NSMotionUsageDescription</key>
	<string>$(PRODUCT_NAME) motion use for better user experience</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>$(PRODUCT_NAME) need photo library access for uploading images</string>
	<key>NSSpeechRecognitionUsageDescription</key>
	<string>$(PRODUCT_NAME) speech use for better user experience</string>
	<key>UIBackgroundModes</key>
	<array>
		<string>fetch</string>
		<string>remote-notification</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UIStatusBarHidden</key>
	<false/>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
    <string>Ứng dụng cần quyền truy cập vị trí của bạn kể cả khi chạy nền để cung cấp chức năng theo dõi hiệu quả.</string>
    <key>MGLMapboxAccessToken</key>
    <string>********************************</string>
    <key>MGLMapboxMetricsEnabledSettingShownInApp</key>
    <true/>
    <key>CADisableMinimumFrameDurationOnPhone</key>
    <true/>
</dict>
</plist>
