PODS:
  - BSImagePicker (3.3.3)
  - Firebase/CoreOnly (7.3.0):
    - FirebaseCore (= 7.3.0)
  - Firebase/Messaging (7.3.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 7.3.0)
  - firebase_core (0.7.0):
    - Firebase/CoreOnly (= 7.3.0)
    - Flutter
  - firebase_messaging (8.0.0-dev.15):
    - Firebase/Messaging (= 7.3.0)
    - firebase_core
    - Flutter
  - FirebaseCore (7.3.0):
    - FirebaseCoreDiagnostics (~> 7.0)
    - GoogleUtilities/Environment (~> 7.0)
    - GoogleUtilities/Logger (~> 7.0)
  - FirebaseCoreDiagnostics (7.11.0):
    - GoogleDataTransport (~> 8.4)
    - GoogleUtilities/Environment (~> 7.0)
    - GoogleUtilities/Logger (~> 7.0)
    - nanopb (~> 2.30908.0)
  - FirebaseInstallations (7.11.0):
    - FirebaseCore (~> 7.0)
    - GoogleUtilities/Environment (~> 7.0)
    - GoogleUtilities/UserDefaults (~> 7.0)
    - PromisesObjC (~> 1.2)
  - FirebaseInstanceID (7.11.0):
    - FirebaseCore (~> 7.0)
    - FirebaseInstallations (~> 7.0)
    - GoogleUtilities/Environment (~> 7.0)
    - GoogleUtilities/UserDefaults (~> 7.0)
  - FirebaseMessaging (7.3.0):
    - FirebaseCore (~> 7.0)
    - FirebaseInstanceID (~> 7.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.0)
    - GoogleUtilities/Environment (~> 7.0)
    - GoogleUtilities/Reachability (~> 7.0)
    - GoogleUtilities/UserDefaults (~> 7.0)
  - Flutter (1.0.0)
  - flutter_absolute_path (0.0.1):
    - Flutter
  - flutter_keyboard_visibility (0.0.1):
    - Flutter
  - flutter_local_notifications (0.0.1):
    - Flutter
  - fluttertoast (0.0.2):
    - Flutter
    - Toast
  - FMDB (2.7.12):
    - FMDB/standard (= 2.7.12)
  - FMDB/Core (2.7.12)
  - FMDB/standard (2.7.12):
    - FMDB/Core
  - geocoding (1.0.5):
    - Flutter
  - GoogleDataTransport (8.4.0):
    - GoogleUtilities/Environment (~> 7.2)
    - nanopb (~> 2.30908.0)
    - PromisesObjC (~> 1.2)
  - GoogleUtilities/AppDelegateSwizzler (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (7.13.3):
    - GoogleUtilities/Privacy
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/Logger (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (7.13.3):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (7.13.3)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (7.13.3)
  - GoogleUtilities/Reachability (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - image_picker (0.0.1):
    - Flutter
  - location (0.0.1):
    - Flutter
  - multi_image_picker2 (5.0.00):
    - BSImagePicker (~> 3.3.1)
    - Flutter
  - nanopb (2.30908.0):
    - nanopb/decode (= 2.30908.0)
    - nanopb/encode (= 2.30908.0)
  - nanopb/decode (2.30908.0)
  - nanopb/encode (2.30908.0)
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider (0.0.1):
    - Flutter
  - "permission_handler (5.1.0+2)":
    - Flutter
  - photo_manager (0.0.1):
    - Flutter
  - Polyline (4.2.1)
  - PromisesObjC (1.2.12)
  - shared_preferences (0.0.1):
    - Flutter
  - Solar (2.1.0)
  - sqflite (0.0.2):
    - Flutter
    - FMDB (>= 2.7.5)
  - syncfusion_flutter_pdfviewer (0.0.1):
    - Flutter
  - Toast (4.1.1)
  - Turf (0.3.0)
  - url_launcher (0.0.1):
    - Flutter
  - ViettelMapAnnotationExtension (0.0.1):
    - ViettelMapSDK (~> 1.0.1)
  - ViettelMapCoreNavigation (1.0.3):
    - Turf (~> 0.3.0)
    - ViettelMapDirections (~> 1.0.3)
    - ViettelMapMobileEvents
    - ViettelMapNavigationNative (~> 6.2.1)
  - ViettelMapDirections (1.0.3):
    - Polyline (~> 4.2)
  - ViettelMapGeocoder (1.0.19):
    - ViettelMapSDK (~> 1.0.3)
  - ViettelMapMobileEvents (0.0.1)
  - ViettelMapNavigation (1.0.3):
    - Solar (~> 2.1)
    - ViettelMapCoreNavigation (= 1.0.3)
    - ViettelMapSDK (~> 1.0.3)
    - ViettelMapSpeechSwift (~> 0.1.0)
  - ViettelMapNavigationNative (6.2.1)
  - ViettelMapSDK (1.0.3)
  - ViettelMapSpeechSwift (0.1.0)
  - vtmap_gl (0.0.1):
    - Flutter
    - ViettelMapAnnotationExtension
    - ViettelMapDirections (~> 1.0.3)
    - ViettelMapGeocoder (~> 1.0.19)
    - ViettelMapNavigation
    - ViettelMapSDK (~> 1.0.3)

DEPENDENCIES:
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - firebase_messaging (from `.symlinks/plugins/firebase_messaging/ios`)
  - Flutter (from `Flutter`)
  - flutter_absolute_path (from `.symlinks/plugins/flutter_absolute_path/ios`)
  - flutter_keyboard_visibility (from `.symlinks/plugins/flutter_keyboard_visibility/ios`)
  - flutter_local_notifications (from `.symlinks/plugins/flutter_local_notifications/ios`)
  - fluttertoast (from `.symlinks/plugins/fluttertoast/ios`)
  - geocoding (from `.symlinks/plugins/geocoding/ios`)
  - image_picker (from `.symlinks/plugins/image_picker/ios`)
  - location (from `.symlinks/plugins/location/ios`)
  - multi_image_picker2 (from `.symlinks/plugins/multi_image_picker2/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider (from `.symlinks/plugins/path_provider/ios`)
  - permission_handler (from `.symlinks/plugins/permission_handler/ios`)
  - photo_manager (from `.symlinks/plugins/photo_manager/ios`)
  - shared_preferences (from `.symlinks/plugins/shared_preferences/ios`)
  - sqflite (from `.symlinks/plugins/sqflite/ios`)
  - syncfusion_flutter_pdfviewer (from `.symlinks/plugins/syncfusion_flutter_pdfviewer/ios`)
  - url_launcher (from `.symlinks/plugins/url_launcher/ios`)
  - ViettelMapSDK (~> 1.0.3)
  - vtmap_gl (from `.symlinks/plugins/vtmap_gl/ios`)

SPEC REPOS:
  https://github.com/vtmaps/pod_control.git:
    - ViettelMapAnnotationExtension
    - ViettelMapCoreNavigation
    - ViettelMapDirections
    - ViettelMapGeocoder
    - ViettelMapMobileEvents
    - ViettelMapNavigation
    - ViettelMapNavigationNative
    - ViettelMapSDK
    - ViettelMapSpeechSwift
  trunk:
    - BSImagePicker
    - Firebase
    - FirebaseCore
    - FirebaseCoreDiagnostics
    - FirebaseInstallations
    - FirebaseInstanceID
    - FirebaseMessaging
    - FMDB
    - GoogleDataTransport
    - GoogleUtilities
    - nanopb
    - Polyline
    - PromisesObjC
    - Solar
    - Toast
    - Turf

EXTERNAL SOURCES:
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  firebase_messaging:
    :path: ".symlinks/plugins/firebase_messaging/ios"
  Flutter:
    :path: Flutter
  flutter_absolute_path:
    :path: ".symlinks/plugins/flutter_absolute_path/ios"
  flutter_keyboard_visibility:
    :path: ".symlinks/plugins/flutter_keyboard_visibility/ios"
  flutter_local_notifications:
    :path: ".symlinks/plugins/flutter_local_notifications/ios"
  fluttertoast:
    :path: ".symlinks/plugins/fluttertoast/ios"
  geocoding:
    :path: ".symlinks/plugins/geocoding/ios"
  image_picker:
    :path: ".symlinks/plugins/image_picker/ios"
  location:
    :path: ".symlinks/plugins/location/ios"
  multi_image_picker2:
    :path: ".symlinks/plugins/multi_image_picker2/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider:
    :path: ".symlinks/plugins/path_provider/ios"
  permission_handler:
    :path: ".symlinks/plugins/permission_handler/ios"
  photo_manager:
    :path: ".symlinks/plugins/photo_manager/ios"
  shared_preferences:
    :path: ".symlinks/plugins/shared_preferences/ios"
  sqflite:
    :path: ".symlinks/plugins/sqflite/ios"
  syncfusion_flutter_pdfviewer:
    :path: ".symlinks/plugins/syncfusion_flutter_pdfviewer/ios"
  url_launcher:
    :path: ".symlinks/plugins/url_launcher/ios"
  vtmap_gl:
    :path: ".symlinks/plugins/vtmap_gl/ios"

SPEC CHECKSUMS:
  BSImagePicker: 57900b323f951711608acaf97d52e9183530cb6d
  Firebase: 26223c695fe322633274198cb19dca8cb7e54416
  firebase_core: 5e68116439d011bef6bbda3e995d92d200a4adac
  firebase_messaging: 4f2ba98128bea6506c58e9fcf8317cc38c60f298
  FirebaseCore: 4d3c72622ce0e2106aaa07bb4b2935ba2c370972
  FirebaseCoreDiagnostics: 68ad972f99206cef818230f3f3179d52ccfb7f8c
  FirebaseInstallations: a58d4f72ec5861840b84df489f2668d970df558a
  FirebaseInstanceID: ad5135045a498d7775903efd39762d2cdfa1be27
  FirebaseMessaging: 68d1bcb14880189558a8ae57167abe0b7e417232
  Flutter: 0e3d915762c693b495b44d77113d4970485de6ec
  flutter_absolute_path: c2e67e8421ee4704ec720ee76eaae06ce1e31769
  flutter_keyboard_visibility: 4625131e43015dbbe759d9b20daaf77e0e3f6619
  flutter_local_notifications: ef18f0537538fcd18e9106b3ddc91cc10b4e579a
  fluttertoast: 3a276f62eb0a34f7736f6d8f105e436faf19468e
  FMDB: 728731dd336af3936ce00f91d9d8495f5718a0e6
  geocoding: 942dfd796e4eae1e70d4f79d68cacc3482267bc9
  GoogleDataTransport: cd9db2180fcecd8da1b561aea31e3e56cf834aa7
  GoogleUtilities: ea963c370a38a8069cc5f7ba4ca849a60b6d7d15
  image_picker: 1c50c15f268eac05df9884b7a57b157a7e5d7fb5
  location: 270786085f436c3740dcfa807d11b8b5a70f4425
  multi_image_picker2: 27a1ede19adfe265c41c21f06d3c92ae5f8727f3
  nanopb: a0ba3315591a9ae0a16a309ee504766e90db0c96
  package_info_plus: ae4a63389b62b5be8544b36bfc5b069617938813
  path_provider: 961ce7cdf0cba062c2f6ac640bcd96d310ec8645
  permission_handler: a6eb0c0a10e733fc3e1dcea4e2d426beb7c91064
  photo_manager: 33cddb794e3c2d32ad4627a1968e5057d8751fab
  Polyline: 0e9890790292741c8186201a536b6bb6a78d02dd
  PromisesObjC: 3113f7f76903778cf4a0586bd1ab89329a0b7b97
  shared_preferences: 47eaded4d5dc0bb45e04e66ce0b93ce876aff8a1
  Solar: 2dc6e7cc39186cb0c8228fa08df76fb50c7d8f24
  sqflite: 954affaf2567c73cda074440299a625e3b2cbf8a
  syncfusion_flutter_pdfviewer: dfb514751af5b6b71e504c9c04a2e4ddbc1dd895
  Toast: 1f5ea13423a1e6674c4abdac5be53587ae481c4e
  Turf: c6bdf62d6a70c647874f295dd1cf4eefc0c3e9e6
  url_launcher: 57d0ad20ca4ccf92256bb343ea186dbcf76fc042
  ViettelMapAnnotationExtension: c880a0848fd6137ea8fb13213d41f2b731fc62df
  ViettelMapCoreNavigation: bb959064c0a79c683bc6ab028abeeba5f558463d
  ViettelMapDirections: 2cf4f4084cd35137c84f84387a9f45093f1605c7
  ViettelMapGeocoder: 23e5d341455ae5da052f31bef3e2e26b6f4356c3
  ViettelMapMobileEvents: dd95a00320bf287cf1b11bc2c4e22b5977e61d9f
  ViettelMapNavigation: 4ec5e869dd3dc01f23e4b7ab1af9b7b0360d3f3a
  ViettelMapNavigationNative: 190a7f8e52b2c99d0c0bec9ec6160b11cdf82e33
  ViettelMapSDK: a01fb60d9466c29bab129dc3bc84a64b20646116
  ViettelMapSpeechSwift: 21f6cac0203f6c508152cfa2e635a405bde2cc6c
  vtmap_gl: d64f7dd903765aaa40e3baba338f7a88ab5f5c3c

PODFILE CHECKSUM: dda2720ced3a8e3cac10a71bb90c8303c7169a4e

COCOAPODS: 1.16.2
